{% extends "base.html" %}

{% block title %}الرئيسية - نظام إدارة الأرشيف العام{% endblock %}

{% block extra_css %}
<style>
    /* Modern Dashboard Styles */
    .dashboard-container {
        max-width: 1400px;
        margin: 0 auto;
        padding: 0 20px;
    }

    .welcome-section {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        text-align: center;
        position: relative;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }

    .welcome-section::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        animation: float 6s ease-in-out infinite;
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-20px) rotate(180deg); }
    }

    .welcome-content {
        position: relative;
        z-index: 2;
    }

    .welcome-title {
        font-size: 2.2rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .welcome-subtitle {
        font-size: 1rem;
        opacity: 0.9;
        margin-bottom: 1rem;
    }

    .avatar-circle {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.2);
        border: 3px solid rgba(255, 255, 255, 0.3);
        display: inline-flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        font-weight: 700;
        color: white;
        margin-bottom: 1rem;
        backdrop-filter: blur(10px);
    }

    .quick-stats {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 15px;
        padding: 1rem;
        backdrop-filter: blur(10px);
        margin-top: 1rem;
    }

    .mini-stat {
        color: white;
        text-align: center;
    }

    .mini-number {
        font-size: 1.3rem;
        font-weight: 700;
        margin-bottom: 0.25rem;
    }

    .mini-label {
        font-size: 0.8rem;
        opacity: 0.8;
    }

    /* Navigation Grid Styles */
    .navigation-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
        max-width: 1200px;
        margin-left: auto;
        margin-right: auto;
    }

    .nav-card {
        background: white;
        border-radius: 15px;
        padding: 1.8rem;
        text-align: center;
        box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        cursor: pointer;
        border: 1px solid #f0f0f0;
    }

    .nav-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        transform: scaleX(0);
        transition: transform 0.3s ease;
    }

    .nav-card:hover::before {
        transform: scaleX(1);
    }

    .nav-card:hover {
        transform: translateY(-8px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        border-color: #667eea;
    }

    .nav-icon {
        font-size: 2.5rem;
        margin-bottom: 1rem;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        display: block;
    }

    .nav-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 0.5rem;
    }

    .nav-description {
        font-size: 0.9rem;
        color: #7f8c8d;
        line-height: 1.4;
    }

    /* Stats Cards */
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.2rem;
        margin-bottom: 2rem;
        max-width: 800px;
        margin-left: auto;
        margin-right: auto;
    }

    .stat-card {
        background: white;
        border-radius: 12px;
        padding: 1.5rem;
        text-align: center;
        box-shadow: 0 3px 10px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
        cursor: pointer;
        border: 1px solid #f0f0f0;
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 6px 20px rgba(0,0,0,0.12);
    }

    .stat-icon {
        font-size: 2rem;
        margin-bottom: 0.8rem;
        color: #667eea;
    }

    .stat-number {
        font-size: 1.8rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 0.3rem;
    }

    .stat-label {
        color: #7f8c8d;
        font-weight: 500;
        font-size: 0.9rem;
    }

    /* Recent Activity Styles */
    .recent-activity {
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        overflow: hidden;
        border: 1px solid #f0f0f0;
    }

    .activity-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1.2rem 1.5rem;
        font-weight: 600;
        font-size: 1.1rem;
    }

    .activity-list {
        max-height: 350px;
        overflow-y: auto;
    }

    .activity-item {
        padding: 1rem 1.5rem;
        border-bottom: 1px solid #f8f9fa;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
    }

    .activity-item:hover {
        background: #f8f9fa;
        transform: translateX(5px);
    }

    .activity-item:last-child {
        border-bottom: none;
    }

    .activity-icon {
        width: 35px;
        height: 35px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 1rem;
        flex-shrink: 0;
        font-size: 0.9rem;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .dashboard-container {
            padding: 0 15px;
        }

        .navigation-grid {
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
        }

        .nav-card {
            padding: 1.5rem;
        }

        .welcome-title {
            font-size: 1.8rem;
        }

        .stats-grid {
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        }
    }

    @media (max-width: 480px) {
        .navigation-grid {
            grid-template-columns: 1fr;
        }

        .stats-grid {
            grid-template-columns: repeat(2, 1fr);
        }
    }

    .activity-content {
        flex: 1;
    }

    .activity-title {
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 0.25rem;
        font-size: 0.95rem;
    }

    .activity-time {
        font-size: 0.8rem;
        color: #7f8c8d;
    }

    .empty-state {
        text-align: center;
        padding: 2rem;
        color: #7f8c8d;
    }

    .empty-state i {
        font-size: 3rem;
        margin-bottom: 1rem;
        opacity: 0.5;
        color: #bdc3c7;
    }

    .empty-state h5 {
        color: #7f8c8d;
        margin-bottom: 0.5rem;
    }

    .empty-state p {
        font-size: 0.9rem;
        margin: 0;
    }
</style>
{% endblock %}

{% block content %}
<div class="dashboard-container">
    <!-- Welcome Section -->
    <div class="welcome-section">
        <div class="welcome-content">
            <div class="avatar-circle">
                {{ (current_user.full_name or current_user.username)[0].upper() }}
            </div>
            <h1 class="welcome-title">
                مرحباً {{ current_user.full_name or current_user.username }}
            </h1>
            <p class="welcome-subtitle">
                {{ current_user.role }} - {{ current_user.department or 'نظام إدارة الأرشيف العام' }}
            </p>
            <div class="quick-stats">
                <div class="row text-center">
                    <div class="col-4">
                        <div class="mini-stat">
                            <div class="mini-number">{{ stats.total_documents }}</div>
                            <div class="mini-label">وثائق</div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="mini-stat">
                            <div class="mini-number">{{ stats.total_incoming }}</div>
                            <div class="mini-label">واردة</div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="mini-stat">
                            <div class="mini-number">{{ stats.total_outgoing }}</div>
                            <div class="mini-label">صادرة</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="stats-grid">
        <div class="stat-card" onclick="location.href='{{ url_for('documents') }}'">
            <div class="stat-icon">
                <i class="fas fa-folder"></i>
            </div>
            <div class="stat-number">{{ stats.total_documents }}</div>
            <div class="stat-label">إجمالي الوثائق</div>
        </div>

        <div class="stat-card" onclick="location.href='{{ url_for('incoming_documents') }}'">
            <div class="stat-icon">
                <i class="fas fa-inbox"></i>
            </div>
            <div class="stat-number">{{ stats.total_incoming }}</div>
            <div class="stat-label">الكتب الواردة</div>
        </div>

        <div class="stat-card" onclick="location.href='{{ url_for('outgoing_documents') }}'">
            <div class="stat-icon">
                <i class="fas fa-paper-plane"></i>
            </div>
            <div class="stat-number">{{ stats.total_outgoing }}</div>
            <div class="stat-label">الكتب الصادرة</div>
        </div>

        <div class="stat-card" onclick="location.href='{{ url_for('profile') }}'">
            <div class="stat-icon">
                <i class="fas fa-user"></i>
            </div>
            <div class="stat-number">1</div>
            <div class="stat-label">المستخدم النشط</div>
        </div>
    </div>

    <!-- Main Navigation Grid -->
    <div class="navigation-grid">
        <!-- الوثائق (Documents) -->
        <div class="nav-card" onclick="location.href='{{ url_for('documents') }}'">
            <div class="nav-icon">
                <i class="fas fa-folder-open"></i>
            </div>
            <div class="nav-title">الوثائق</div>
            <div class="nav-description">إدارة وتصفح جميع الوثائق المحفوظة في النظام</div>
        </div>

        <!-- الصادرة (Outgoing Documents) -->
        <div class="nav-card" onclick="location.href='{{ url_for('outgoing_documents') }}'">
            <div class="nav-icon">
                <i class="fas fa-paper-plane"></i>
            </div>
            <div class="nav-title">الصادرة</div>
            <div class="nav-description">إدارة الكتب والمراسلات الصادرة من المؤسسة</div>
        </div>

        <!-- البحث (Search) -->
        <div class="nav-card" onclick="location.href='{{ url_for('search') }}'">
            <div class="nav-icon">
                <i class="fas fa-search"></i>
            </div>
            <div class="nav-title">البحث</div>
            <div class="nav-description">البحث المتقدم في الوثائق والمراسلات</div>
        </div>

        <!-- التقويم (Calendar) -->
        <div class="nav-card" onclick="location.href='{{ url_for('calendar') }}'">
            <div class="nav-icon">
                <i class="fas fa-calendar-alt"></i>
            </div>
            <div class="nav-title">التقويم</div>
            <div class="nav-description">عرض المواعيد والأحداث المهمة</div>
        </div>

        <!-- الميزات المتقدمة (Advanced Features) -->
        <div class="nav-card" onclick="location.href='{{ url_for('ocr_scanner') }}'">
            <div class="nav-icon">
                <i class="fas fa-magic"></i>
            </div>
            <div class="nav-title">الميزات المتقدمة</div>
            <div class="nav-description">ماسح OCR والتوقيعات الرقمية والأدوات المتقدمة</div>
        </div>

        <!-- التقارير (Reports) -->
        <div class="nav-card" onclick="location.href='{{ url_for('reports') }}'">
            <div class="nav-icon">
                <i class="fas fa-chart-bar"></i>
            </div>
            <div class="nav-title">التقارير</div>
            <div class="nav-description">تقارير وإحصائيات شاملة عن النظام</div>
        </div>

        <!-- الإعدادات (Settings) -->
        <div class="nav-card" onclick="location.href='{{ url_for('settings') }}'">
            <div class="nav-icon">
                <i class="fas fa-cog"></i>
            </div>
            <div class="nav-title">الإعدادات</div>
            <div class="nav-description">إعدادات النظام والتخصيص</div>
        </div>

        <!-- سجل الأنشطة (Activity Log) -->
        <div class="nav-card" onclick="location.href='{{ url_for('activity_log') }}'">
            <div class="nav-icon">
                <i class="fas fa-history"></i>
            </div>
            <div class="nav-title">سجل الأنشطة</div>
            <div class="nav-description">تتبع جميع العمليات والأنشطة في النظام</div>
        </div>

        <!-- إدارة المستخدمين (User Management) -->
        {% if current_user.is_admin() %}
        <div class="nav-card" onclick="location.href='{{ url_for('users') }}'">
            <div class="nav-icon">
                <i class="fas fa-users"></i>
            </div>
            <div class="nav-title">إدارة المستخدمين</div>
            <div class="nav-description">إضافة وإدارة حسابات المستخدمين</div>
        </div>
        {% endif %}

        <!-- إدارة الأقسام (Department Management) -->
        {% if current_user.is_admin() %}
        <div class="nav-card" onclick="location.href='{{ url_for('departments') }}'">
            <div class="nav-icon">
                <i class="fas fa-building"></i>
            </div>
            <div class="nav-title">إدارة الأقسام</div>
            <div class="nav-description">تنظيم وإدارة أقسام المؤسسة</div>
        </div>
        {% endif %}

        <!-- صيانة النظام (System Maintenance) -->
        {% if current_user.is_admin() %}
        <div class="nav-card" onclick="location.href='{{ url_for('maintenance') }}'">
            <div class="nav-icon">
                <i class="fas fa-tools"></i>
            </div>
            <div class="nav-title">صيانة النظام</div>
            <div class="nav-description">أدوات صيانة وتحسين أداء النظام</div>
        </div>
        {% endif %}

        <!-- الإشعارات (Notifications) -->
        <div class="nav-card" onclick="showComingSoon()">
            <div class="nav-icon">
                <i class="fas fa-bell"></i>
            </div>
            <div class="nav-title">الإشعارات</div>
            <div class="nav-description">إدارة التنبيهات والإشعارات</div>
        </div>

        <!-- مدير النظام (System Administrator) -->
        {% if current_user.is_admin() %}
        <div class="nav-card" onclick="location.href='{{ url_for('profile') }}'">
            <div class="nav-icon">
                <i class="fas fa-user-shield"></i>
            </div>
            <div class="nav-title">مدير النظام</div>
            <div class="nav-description">لوحة تحكم المدير والإعدادات المتقدمة</div>
        </div>
        {% endif %}
    </div>

    <!-- Recent Activity Section -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="recent-activity">
                <div class="activity-header">
                    <i class="fas fa-clock me-2"></i>
                    النشاط الأخير
                </div>

                <div class="activity-list">
                    {% if stats.recent_documents or stats.recent_incoming or stats.recent_outgoing %}
                        {% for doc in stats.recent_documents[:3] %}
                        <div class="activity-item">
                            <div class="activity-icon">
                                <i class="fas fa-file"></i>
                            </div>
                            <div class="activity-content">
                                <div class="activity-title">{{ doc.title[:40] }}...</div>
                                <div class="activity-time">منذ {{ doc.created_at.strftime('%H:%M') }}</div>
                            </div>
                        </div>
                        {% endfor %}

                        {% for inc in stats.recent_incoming[:2] %}
                        <div class="activity-item">
                            <div class="activity-icon">
                                <i class="fas fa-inbox"></i>
                            </div>
                            <div class="activity-content">
                                <div class="activity-title">{{ inc.subject[:40] }}...</div>
                                <div class="activity-time">منذ {{ inc.created_at.strftime('%H:%M') }}</div>
                            </div>
                        </div>
                        {% endfor %}

                        {% for out in stats.recent_outgoing[:2] %}
                        <div class="activity-item">
                            <div class="activity-icon">
                                <i class="fas fa-paper-plane"></i>
                            </div>
                            <div class="activity-content">
                                <div class="activity-title">{{ out.subject[:40] }}...</div>
                                <div class="activity-time">منذ {{ out.created_at.strftime('%H:%M') }}</div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="empty-state">
                            <i class="fas fa-inbox"></i>
                            <h5>لا يوجد نشاط حديث</h5>
                            <p>ابدأ بإضافة وثائق جديدة لرؤية النشاط هنا</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
    // Animate navigation cards
    function animateCards() {
        const cards = document.querySelectorAll('.nav-card, .stat-card');

        cards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';

            setTimeout(() => {
                card.style.transition = 'all 0.6s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 100);
        });
    }

    // Animate counters in stats
    function animateCounters() {
        const counters = document.querySelectorAll('.stat-number, .mini-number');

        counters.forEach(counter => {
            const target = parseInt(counter.textContent) || 0;
            const increment = Math.max(1, target / 30);
            let current = 0;

            const timer = setInterval(() => {
                current += increment;
                if (current >= target) {
                    counter.textContent = target;
                    clearInterval(timer);
                } else {
                    counter.textContent = Math.floor(current);
                }
            }, 50);
        });
    }

    // Show coming soon message
    function showComingSoon() {
        alert('هذه الميزة قيد التطوير وستكون متاحة قريباً');
    }

    // Initialize animations when page loads
    document.addEventListener('DOMContentLoaded', function() {
        // Add loading animation
        setTimeout(() => {
            animateCards();
            animateCounters();
        }, 300);

        // Add hover effects to navigation cards
        const navCards = document.querySelectorAll('.nav-card');
        navCards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-8px) scale(1.02)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });

        console.log('🚀 Dashboard loaded successfully');
    });


</script>
{% endblock %}
