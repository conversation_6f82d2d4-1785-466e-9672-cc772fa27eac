2025-06-06 21:30:15.127 | INFO     | __main__:init_database:246 - تم إنشاء المستخدم الافتراضي: admin
2025-06-06 21:30:15.128 | INFO     | __main__:<module>:256 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-06 21:30:16.222 | INFO     | __main__:<module>:256 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-06 21:30:38.286 | INFO     | __main__:login:194 - تم تسجيل دخول المستخدم: admin
2025-06-06 21:31:55.227 | INFO     | __main__:logout:207 - تم تسجيل خروج المستخدم: admin
2025-06-06 21:32:28.785 | INFO     | __main__:login:194 - تم تسجيل دخول المستخدم: admin
2025-06-06 21:36:45.327 | INFO     | __main__:<module>:425 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-06 21:37:09.538 | INFO     | __main__:<module>:456 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-06 21:42:41.001 | INFO     | __main__:<module>:456 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-06 21:42:41.891 | INFO     | __main__:<module>:456 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-06 21:42:59.208 | INFO     | __main__:login:194 - تم تسجيل دخول المستخدم: admin
2025-06-06 21:44:44.093 | INFO     | __main__:add_document:296 - تم إضافة وثيقة جديدة: طالب بواسطة admin
2025-06-06 21:48:24.060 | INFO     | __main__:login:194 - تم تسجيل دخول المستخدم: admin
2025-06-06 21:50:45.525 | INFO     | __main__:<module>:594 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-06 21:51:20.819 | INFO     | __main__:<module>:732 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-06 21:51:52.245 | INFO     | __main__:<module>:864 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-06 22:02:48.685 | INFO     | __main__:<module>:864 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-06 22:02:49.698 | INFO     | __main__:<module>:864 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-06 22:03:04.592 | INFO     | __main__:login:194 - تم تسجيل دخول المستخدم: admin
2025-06-06 22:03:13.374 | INFO     | __main__:login:194 - تم تسجيل دخول المستخدم: admin
2025-06-06 22:21:26.284 | INFO     | __main__:<module>:899 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-06 22:21:27.194 | INFO     | __main__:<module>:899 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-06 22:21:55.156 | INFO     | __main__:login:227 - تم تسجيل دخول المستخدم: admin
2025-06-06 22:23:01.763 | INFO     | __main__:add_incoming:485 - تم إضافة كتاب وارد جديد: IN-2025-0001
2025-06-06 22:25:55.529 | INFO     | __main__:<module>:899 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-06 22:26:11.748 | INFO     | __main__:<module>:899 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-06 22:26:23.879 | INFO     | __main__:<module>:899 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-06 22:26:45.183 | INFO     | __main__:<module>:899 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-06 22:26:58.553 | INFO     | __main__:<module>:900 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-06 22:27:11.632 | INFO     | __main__:<module>:901 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-06 22:27:25.854 | INFO     | __main__:<module>:903 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-06 22:27:41.122 | INFO     | __main__:<module>:905 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-06 22:30:30.986 | INFO     | __main__:<module>:906 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-06 22:30:46.221 | INFO     | __main__:<module>:907 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-06 22:32:45.802 | INFO     | __main__:<module>:907 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-06 22:32:46.714 | INFO     | __main__:<module>:907 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-06 22:33:05.254 | INFO     | __main__:login:227 - تم تسجيل دخول المستخدم: admin
2025-06-06 22:34:07.856 | INFO     | __main__:add_outgoing:626 - تم إضافة كتاب صادر جديد: OUT-2025-0001
2025-06-06 22:40:29.914 | INFO     | __main__:<module>:915 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-06 22:40:45.172 | INFO     | __main__:<module>:923 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-06 22:59:11.357 | INFO     | __main__:init_database:1062 - تم إنشاء قاعدة البيانات والمستخدمين الافتراضيين
2025-06-06 22:59:11.358 | INFO     | __main__:<module>:1072 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-06 22:59:13.008 | INFO     | __main__:init_database:1062 - تم إنشاء قاعدة البيانات والمستخدمين الافتراضيين
2025-06-06 22:59:13.008 | INFO     | __main__:<module>:1072 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-06 22:59:22.560 | INFO     | __main__:login:327 - تم تسجيل دخول المستخدم: admin
2025-06-06 23:00:35.806 | INFO     | __main__:add_incoming:595 - تم إضافة كتاب وارد جديد: IN-2025-0001
2025-06-06 23:03:24.188 | INFO     | __main__:init_database:1065 - تم إنشاء قاعدة البيانات والمستخدمين الافتراضيين
2025-06-06 23:03:24.189 | INFO     | __main__:<module>:1075 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-06 23:03:42.249 | INFO     | __main__:init_database:1118 - تم إنشاء قاعدة البيانات والمستخدمين الافتراضيين
2025-06-06 23:03:42.250 | INFO     | __main__:<module>:1128 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-06 23:04:07.298 | INFO     | __main__:init_database:1187 - تم إنشاء قاعدة البيانات والمستخدمين الافتراضيين
2025-06-06 23:04:07.299 | INFO     | __main__:<module>:1197 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 00:11:18.579 | INFO     | __main__:init_database:1649 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 00:11:18.581 | INFO     | __main__:<module>:1659 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 00:11:20.436 | INFO     | __main__:init_database:1649 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 00:11:20.437 | INFO     | __main__:<module>:1659 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 00:11:31.173 | INFO     | __main__:login:452 - تم تسجيل دخول المستخدم: admin
2025-06-07 00:13:21.239 | INFO     | __main__:login:452 - تم تسجيل دخول المستخدم: admin
2025-06-07 00:14:51.734 | INFO     | __main__:init_database:1652 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 00:14:51.735 | INFO     | __main__:<module>:1662 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 00:15:03.822 | INFO     | __main__:init_database:1652 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 00:15:03.823 | INFO     | __main__:<module>:1662 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 00:15:18.790 | INFO     | __main__:init_database:1656 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 00:15:18.790 | INFO     | __main__:<module>:1666 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 00:15:30.744 | INFO     | __main__:init_database:1659 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 00:15:30.745 | INFO     | __main__:<module>:1669 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 00:15:43.666 | INFO     | __main__:init_database:1662 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 00:15:43.667 | INFO     | __main__:<module>:1672 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 00:15:55.933 | INFO     | __main__:init_database:1665 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 00:15:55.934 | INFO     | __main__:<module>:1675 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 00:16:10.003 | INFO     | __main__:init_database:1675 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 00:16:10.004 | INFO     | __main__:<module>:1685 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 00:18:09.723 | INFO     | __main__:init_database:1679 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 00:18:09.724 | INFO     | __main__:<module>:1689 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 00:28:19.550 | INFO     | __main__:init_database:1679 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 00:28:19.551 | INFO     | __main__:<module>:1689 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 00:28:21.467 | INFO     | __main__:init_database:1679 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 00:28:21.468 | INFO     | __main__:<module>:1689 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 00:28:27.620 | INFO     | __main__:login:455 - تم تسجيل دخول المستخدم: admin
2025-06-07 00:28:54.748 | INFO     | __main__:create_system_backup:1617 - تم إنشاء نسخة احتياطية: backups\backup_20250607_002854.zip
2025-06-07 00:28:58.482 | INFO     | __main__:init_database:1683 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 00:28:58.483 | INFO     | __main__:<module>:1693 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 00:29:30.182 | INFO     | __main__:init_database:1778 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 00:29:30.183 | INFO     | __main__:<module>:1788 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 01:45:33.634 | INFO     | __main__:init_database:2144 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 01:45:33.635 | ERROR    | __main__:init_database:2162 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 01:45:33.635 | INFO     | __main__:<module>:2172 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 01:45:35.751 | INFO     | __main__:init_database:2144 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 01:45:35.752 | ERROR    | __main__:init_database:2162 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 01:45:35.752 | INFO     | __main__:<module>:2172 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 01:46:12.707 | INFO     | __main__:login:591 - تم تسجيل دخول المستخدم: admin
2025-06-07 01:50:03.885 | INFO     | __main__:init_database:2343 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 01:50:03.886 | ERROR    | __main__:init_database:2361 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 01:50:03.887 | INFO     | __main__:<module>:2371 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 01:51:16.343 | INFO     | __main__:init_database:2352 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 01:51:16.343 | ERROR    | __main__:init_database:2370 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 01:51:16.344 | INFO     | __main__:<module>:2380 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 01:51:55.236 | INFO     | __main__:init_database:2434 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 01:51:55.237 | ERROR    | __main__:init_database:2452 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 01:51:55.238 | INFO     | __main__:<module>:2462 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 01:52:14.995 | INFO     | __main__:init_database:2459 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 01:52:14.996 | ERROR    | __main__:init_database:2477 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 01:52:14.997 | INFO     | __main__:<module>:2487 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 01:53:46.828 | INFO     | __main__:init_database:2459 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 01:53:46.829 | ERROR    | __main__:init_database:2477 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 01:53:46.829 | INFO     | __main__:<module>:2487 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 01:56:25.034 | INFO     | __main__:init_database:2459 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 01:56:25.035 | ERROR    | __main__:init_database:2477 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 01:56:25.035 | INFO     | __main__:<module>:2487 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 01:56:27.290 | INFO     | __main__:init_database:2459 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 01:56:27.290 | ERROR    | __main__:init_database:2477 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 01:56:27.291 | INFO     | __main__:<module>:2487 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 01:56:37.494 | INFO     | __main__:login:707 - تم تسجيل دخول المستخدم: admin
2025-06-07 01:56:47.156 | INFO     | __main__:login:707 - تم تسجيل دخول المستخدم: admin
2025-06-07 01:58:03.760 | INFO     | __main__:init_database:2459 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 01:58:03.761 | ERROR    | __main__:init_database:2477 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 01:58:03.762 | INFO     | __main__:<module>:2581 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 01:58:26.659 | INFO     | __main__:init_database:2459 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 01:58:26.660 | ERROR    | __main__:init_database:2477 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 01:58:26.661 | INFO     | __main__:<module>:2581 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 01:59:39.857 | INFO     | __main__:init_database:2459 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 01:59:39.858 | ERROR    | __main__:init_database:2477 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 01:59:39.859 | INFO     | __main__:<module>:2581 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 01:59:42.469 | INFO     | __main__:init_database:2459 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 01:59:42.477 | ERROR    | __main__:init_database:2477 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 01:59:42.479 | INFO     | __main__:<module>:2581 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 01:59:45.447 | INFO     | __main__:login:707 - تم تسجيل دخول المستخدم: admin
2025-06-07 02:00:01.635 | INFO     | __main__:login:707 - تم تسجيل دخول المستخدم: admin
2025-06-07 02:00:21.016 | INFO     | __main__:init_database:2459 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 02:00:21.017 | ERROR    | __main__:init_database:2477 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 02:00:21.018 | INFO     | __main__:<module>:2649 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 02:00:30.394 | INFO     | __main__:login:707 - تم تسجيل دخول المستخدم: admin
2025-06-07 02:02:57.699 | INFO     | __main__:init_database:2459 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 02:02:57.699 | ERROR    | __main__:init_database:2477 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 02:02:57.700 | INFO     | __main__:<module>:2649 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 02:02:59.973 | INFO     | __main__:init_database:2459 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 02:02:59.974 | ERROR    | __main__:init_database:2477 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 02:02:59.975 | INFO     | __main__:<module>:2649 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 02:03:13.839 | INFO     | __main__:login:707 - تم تسجيل دخول المستخدم: admin
2025-06-07 02:03:21.738 | INFO     | __main__:login:707 - تم تسجيل دخول المستخدم: admin
2025-06-07 02:05:43.063 | INFO     | __main__:init_database:2464 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 02:05:43.064 | ERROR    | __main__:init_database:2482 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 02:05:43.065 | INFO     | __main__:<module>:2654 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 02:06:49.655 | INFO     | __main__:init_database:2552 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 02:06:49.659 | ERROR    | __main__:init_database:2570 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 02:06:49.661 | INFO     | __main__:<module>:2742 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 02:07:11.813 | INFO     | __main__:init_database:2552 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 02:07:11.815 | ERROR    | __main__:init_database:2570 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 02:07:11.815 | INFO     | __main__:<module>:2802 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 02:07:50.727 | INFO     | __main__:init_database:2552 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 02:07:50.728 | ERROR    | __main__:init_database:2570 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 02:07:50.729 | INFO     | __main__:<module>:2963 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 02:08:11.099 | INFO     | __main__:init_database:2569 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 02:08:11.101 | ERROR    | __main__:init_database:2587 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 02:08:11.102 | INFO     | __main__:<module>:2980 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 02:12:33.535 | INFO     | __main__:init_database:2613 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 02:12:33.536 | ERROR    | __main__:init_database:2631 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 02:12:33.536 | INFO     | __main__:<module>:3082 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 02:12:36.468 | INFO     | __main__:init_database:2613 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 02:12:36.468 | ERROR    | __main__:init_database:2631 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 02:12:36.469 | INFO     | __main__:<module>:3082 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 02:13:10.760 | INFO     | __main__:login:773 - تم تسجيل دخول المستخدم: admin
2025-06-07 01:43:53.887 | INFO     | __main__:init_database:2721 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 01:43:53.888 | ERROR    | __main__:init_database:2739 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 01:43:53.889 | INFO     | __main__:<module>:3190 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 01:44:04.221 | INFO     | __main__:login:773 - تم تسجيل دخول المستخدم: admin
2025-06-07 01:44:15.914 | INFO     | __main__:init_database:2721 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 01:44:15.916 | ERROR    | __main__:init_database:2739 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 01:44:15.918 | INFO     | __main__:<module>:3261 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 01:44:37.292 | INFO     | __main__:login:773 - تم تسجيل دخول المستخدم: admin
2025-06-07 01:49:54.648 | INFO     | __main__:init_database:2721 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 01:49:54.650 | ERROR    | __main__:init_database:2739 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 01:49:54.655 | INFO     | __main__:<module>:3261 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 01:50:07.554 | INFO     | __main__:init_database:2721 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 01:50:07.555 | ERROR    | __main__:init_database:2739 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 01:50:07.557 | INFO     | __main__:<module>:3261 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 01:53:04.868 | INFO     | __main__:init_database:2721 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 01:53:04.869 | ERROR    | __main__:init_database:2739 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 01:53:04.870 | INFO     | __main__:<module>:3261 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 01:53:07.376 | INFO     | __main__:init_database:2721 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 01:53:07.377 | ERROR    | __main__:init_database:2739 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 01:53:07.378 | INFO     | __main__:<module>:3261 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 01:53:27.780 | INFO     | __main__:login:773 - تم تسجيل دخول المستخدم: admin
2025-06-07 01:55:56.560 | INFO     | __main__:init_database:2724 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 01:55:56.567 | ERROR    | __main__:init_database:2742 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 01:55:56.568 | INFO     | __main__:<module>:3264 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 01:57:01.767 | INFO     | __main__:init_database:2837 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 01:57:01.768 | ERROR    | __main__:init_database:2855 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 01:57:01.768 | INFO     | __main__:<module>:3377 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 01:57:42.210 | INFO     | __main__:init_database:2838 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 01:57:42.211 | ERROR    | __main__:init_database:2856 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 01:57:42.212 | INFO     | __main__:<module>:3378 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 01:58:20.583 | INFO     | __main__:init_database:2838 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 01:58:20.584 | ERROR    | __main__:init_database:2856 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 01:58:20.585 | INFO     | __main__:<module>:3407 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 02:00:21.566 | INFO     | __main__:init_database:2961 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 02:00:21.567 | ERROR    | __main__:init_database:2979 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 02:00:21.567 | INFO     | __main__:<module>:3530 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 02:00:39.062 | INFO     | __main__:init_database:2990 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 02:00:39.062 | ERROR    | __main__:init_database:3008 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 02:00:39.063 | INFO     | __main__:<module>:3559 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 02:01:06.816 | INFO     | __main__:init_database:2990 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 02:01:06.817 | ERROR    | __main__:init_database:3008 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 02:01:06.817 | INFO     | __main__:<module>:3682 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 02:01:22.196 | INFO     | __main__:init_database:2991 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 02:01:22.197 | ERROR    | __main__:init_database:3009 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 02:01:22.198 | INFO     | __main__:<module>:3683 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 02:03:21.335 | INFO     | __main__:init_database:2991 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 02:03:21.336 | ERROR    | __main__:init_database:3009 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 02:03:21.337 | INFO     | __main__:<module>:3703 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 02:03:41.722 | INFO     | __main__:init_database:3054 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 02:03:41.723 | ERROR    | __main__:init_database:3072 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 02:03:41.724 | INFO     | __main__:<module>:3766 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 02:03:53.739 | INFO     | __main__:init_database:3055 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 02:03:53.740 | ERROR    | __main__:init_database:3073 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 02:03:53.740 | INFO     | __main__:<module>:3767 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 02:06:08.553 | INFO     | __main__:init_database:3055 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 02:06:08.554 | ERROR    | __main__:init_database:3073 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 02:06:08.555 | INFO     | __main__:<module>:3767 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 02:06:11.326 | INFO     | __main__:init_database:3055 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 02:06:11.327 | ERROR    | __main__:init_database:3073 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 02:06:11.328 | INFO     | __main__:<module>:3767 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 02:06:35.319 | INFO     | __main__:login:808 - تم تسجيل دخول المستخدم: admin
2025-06-07 02:11:25.588 | INFO     | __main__:init_database:3055 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 02:11:25.589 | ERROR    | __main__:init_database:3073 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 02:11:25.590 | INFO     | __main__:<module>:3775 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 02:12:24.726 | INFO     | __main__:init_database:3055 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 02:12:24.726 | ERROR    | __main__:init_database:3073 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 02:12:24.727 | INFO     | __main__:<module>:3836 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 02:45:22.697 | INFO     | __main__:init_database:3055 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 02:45:22.699 | ERROR    | __main__:init_database:3073 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 02:45:22.700 | INFO     | __main__:<module>:3864 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 02:45:45.485 | INFO     | __main__:login:808 - تم تسجيل دخول المستخدم: admin
2025-06-07 02:46:57.626 | ERROR    | __main__:extract_text_from_image:2726 - خطأ في استخراج النص من الصورة: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 02:18:08.441 | INFO     | __main__:init_database:3055 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 02:18:08.441 | ERROR    | __main__:init_database:3073 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 02:18:08.442 | INFO     | __main__:<module>:3864 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 02:18:11.381 | INFO     | __main__:init_database:3055 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 02:18:11.382 | ERROR    | __main__:init_database:3073 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 02:18:11.383 | INFO     | __main__:<module>:3864 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 02:18:31.352 | INFO     | __main__:login:808 - تم تسجيل دخول المستخدم: admin
2025-06-07 02:19:30.367 | ERROR    | __main__:add_document:936 - خطأ في إضافة الوثيقة: generate_barcode() missing 1 required positional argument: 'data'
2025-06-07 02:19:52.666 | ERROR    | __main__:add_document:936 - خطأ في إضافة الوثيقة: generate_barcode() missing 1 required positional argument: 'data'
2025-06-07 02:21:13.709 | ERROR    | __main__:add_document:936 - خطأ في إضافة الوثيقة: generate_barcode() missing 1 required positional argument: 'data'
2025-06-07 02:21:32.439 | ERROR    | __main__:extract_text_from_image:2726 - خطأ في استخراج النص من الصورة: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 02:23:03.193 | ERROR    | __main__:add_document:936 - خطأ في إضافة الوثيقة: generate_barcode() missing 1 required positional argument: 'data'
2025-06-07 02:23:17.914 | ERROR    | __main__:add_document:936 - خطأ في إضافة الوثيقة: generate_barcode() missing 1 required positional argument: 'data'
2025-06-07 02:23:57.744 | INFO     | __main__:add_incoming:1096 - تم إضافة كتاب وارد جديد: IN-2025-0001
2025-06-07 02:24:35.707 | ERROR    | __main__:add_document:936 - خطأ في إضافة الوثيقة: generate_barcode() missing 1 required positional argument: 'data'
2025-06-07 02:26:13.564 | ERROR    | __main__:extract_text_from_image:2726 - خطأ في استخراج النص من الصورة: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 02:33:24.574 | INFO     | __main__:init_database:3087 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 02:33:24.574 | ERROR    | __main__:init_database:3105 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 02:33:24.575 | INFO     | __main__:<module>:3896 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 02:35:44.792 | INFO     | __main__:init_database:3122 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 02:35:44.793 | ERROR    | __main__:init_database:3140 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 02:35:44.794 | INFO     | __main__:<module>:3931 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 02:36:14.413 | INFO     | __main__:init_database:3171 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 02:36:14.414 | ERROR    | __main__:init_database:3189 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 02:36:14.415 | INFO     | __main__:<module>:3980 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 02:36:50.642 | INFO     | __main__:init_database:3171 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 02:36:50.644 | ERROR    | __main__:init_database:3189 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 02:36:50.645 | INFO     | __main__:<module>:4026 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 02:39:55.277 | INFO     | __main__:init_database:3171 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 02:39:55.278 | ERROR    | __main__:init_database:3189 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 02:39:55.279 | INFO     | __main__:<module>:4070 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 02:39:57.352 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:39:57.616 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 02:40:13.040 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:40:13.357 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 02:40:58.048 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:40:58.369 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 02:41:13.045 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:41:13.369 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 02:42:01.406 | INFO     | __main__:init_database:3171 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 02:42:01.406 | ERROR    | __main__:init_database:3189 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 02:42:01.407 | INFO     | __main__:<module>:4070 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 02:42:04.339 | INFO     | __main__:init_database:3171 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 02:42:04.340 | ERROR    | __main__:init_database:3189 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 02:42:04.342 | INFO     | __main__:<module>:4070 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 02:42:13.050 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:42:13.375 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 02:42:20.422 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/
2025-06-07 02:42:20.836 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/login
2025-06-07 02:42:23.986 | DEBUG    | __main__:before_request:4051 - طلب: POST http://localhost:5000/login
2025-06-07 02:42:24.363 | INFO     | __main__:login:808 - تم تسجيل دخول المستخدم: admin
2025-06-07 02:42:24.688 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/dashboard
2025-06-07 02:42:24.953 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:42:44.295 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/ocr-scanner
2025-06-07 02:42:44.543 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:42:55.634 | DEBUG    | __main__:before_request:4051 - طلب: POST http://localhost:5000/ocr-scanner/upload
2025-06-07 02:42:55.645 | INFO     | __main__:ocr_upload:3802 - تم حفظ الملف مؤقتاً: C:\Users\<USER>\OneDrive\Desktop\M\uploads\temp\20250607_024255_Screenshot_2025-06-06_222044.png
2025-06-07 02:42:55.674 | INFO     | __main__:ocr_upload:3813 - معالجة صورة: .png
2025-06-07 02:42:55.851 | WARNING  | __main__:extract_text_from_image:2772 - فشل في التكوين --oem 3 --psm 6 -l ara+eng: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 02:42:55.909 | WARNING  | __main__:extract_text_from_image:2772 - فشل في التكوين --oem 3 --psm 6 -l eng: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 02:42:55.987 | WARNING  | __main__:extract_text_from_image:2772 - فشل في التكوين --oem 3 --psm 6 -l ara: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 02:42:56.043 | WARNING  | __main__:extract_text_from_image:2772 - فشل في التكوين --oem 3 --psm 4 -l ara+eng: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 02:42:56.167 | WARNING  | __main__:extract_text_from_image:2772 - فشل في التكوين --oem 3 --psm 8 -l ara+eng: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 02:42:56.431 | ERROR    | __main__:extract_text_from_image:2787 - فشل في التكوين البسيط: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 02:42:56.432 | WARNING  | __main__:extract_text_from_image:2789 - لم يتم العثور على نص في الصورة
2025-06-07 02:42:56.447 | INFO     | __main__:ocr_upload:3830 - تم حذف الملف المؤقت: C:\Users\<USER>\OneDrive\Desktop\M\uploads\temp\20250607_024255_Screenshot_2025-06-06_222044.png
2025-06-07 02:42:56.512 | WARNING  | __main__:ocr_upload:3857 - لم يتم العثور على نص في الملف
2025-06-07 02:43:00.360 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:43:13.041 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:43:14.775 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:43:26.650 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/digital-signatures
2025-06-07 02:43:27.111 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:43:44.303 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/documents
2025-06-07 02:43:44.798 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:43:46.320 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/documents/add
2025-06-07 02:43:46.764 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:44:01.042 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:44:13.350 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:44:16.451 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:44:26.452 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/dashboard
2025-06-07 02:44:26.715 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:44:36.165 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/admin/maintenance
2025-06-07 02:44:36.624 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:44:57.221 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/documents/add
2025-06-07 02:44:57.665 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:45:02.045 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:45:08.920 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/dashboard
2025-06-07 02:45:09.181 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:45:13.045 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:45:21.798 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/activity-log
2025-06-07 02:45:22.064 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:45:24.643 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/trash
2025-06-07 02:45:25.096 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:45:26.564 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/users
2025-06-07 02:45:27.000 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:45:30.949 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/dashboard
2025-06-07 02:45:31.381 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:46:01.302 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:46:03.357 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/documents
2025-06-07 02:46:03.513 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:46:03.622 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:46:05.619 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/documents/add
2025-06-07 02:46:06.028 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:46:11.499 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/incoming
2025-06-07 02:46:12.322 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:46:13.038 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:46:15.275 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/incoming/add
2025-06-07 02:46:15.541 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:46:31.470 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/outgoing
2025-06-07 02:46:31.924 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:46:33.564 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/outgoing/add
2025-06-07 02:46:34.007 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:46:46.424 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/documents
2025-06-07 02:46:46.842 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:46:48.165 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/documents/add
2025-06-07 02:46:48.572 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:47:04.052 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:47:13.361 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:47:19.048 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:47:49.358 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:48:05.053 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:48:13.348 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:48:19.052 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:48:48.575 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:49:06.051 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:49:13.347 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:49:19.040 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:49:44.674 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/ocr-scanner
2025-06-07 02:49:44.941 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:49:48.650 | DEBUG    | __main__:before_request:4051 - طلب: POST http://localhost:5000/ocr-scanner/upload
2025-06-07 02:49:48.661 | INFO     | __main__:ocr_upload:3802 - تم حفظ الملف مؤقتاً: C:\Users\<USER>\OneDrive\Desktop\M\uploads\temp\20250607_024948_Screenshot_2025-06-06_222044.png
2025-06-07 02:49:48.661 | INFO     | __main__:ocr_upload:3813 - معالجة صورة: .png
2025-06-07 02:49:48.731 | WARNING  | __main__:extract_text_from_image:2772 - فشل في التكوين --oem 3 --psm 6 -l ara+eng: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 02:49:48.773 | WARNING  | __main__:extract_text_from_image:2772 - فشل في التكوين --oem 3 --psm 6 -l eng: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 02:49:48.812 | WARNING  | __main__:extract_text_from_image:2772 - فشل في التكوين --oem 3 --psm 6 -l ara: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 02:49:48.849 | WARNING  | __main__:extract_text_from_image:2772 - فشل في التكوين --oem 3 --psm 4 -l ara+eng: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 02:49:48.885 | WARNING  | __main__:extract_text_from_image:2772 - فشل في التكوين --oem 3 --psm 8 -l ara+eng: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 02:49:48.929 | ERROR    | __main__:extract_text_from_image:2787 - فشل في التكوين البسيط: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 02:49:48.930 | WARNING  | __main__:extract_text_from_image:2789 - لم يتم العثور على نص في الصورة
2025-06-07 02:49:48.931 | INFO     | __main__:ocr_upload:3830 - تم حذف الملف المؤقت: C:\Users\<USER>\OneDrive\Desktop\M\uploads\temp\20250607_024948_Screenshot_2025-06-06_222044.png
2025-06-07 02:49:48.932 | WARNING  | __main__:ocr_upload:3857 - لم يتم العثور على نص في الملف
2025-06-07 02:50:07.356 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:50:13.044 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:50:15.352 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:50:45.049 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:51:08.355 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:51:13.048 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:51:15.343 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:51:45.043 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:51:59.534 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:52:05.341 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/ocr-scanner
2025-06-07 02:52:05.865 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:52:09.927 | DEBUG    | __main__:before_request:4051 - طلب: POST http://localhost:5000/ocr-scanner/upload
2025-06-07 02:52:09.938 | INFO     | __main__:ocr_upload:3802 - تم حفظ الملف مؤقتاً: C:\Users\<USER>\OneDrive\Desktop\M\uploads\temp\20250607_025209_Screenshot_2025-06-06_222044.png
2025-06-07 02:52:09.938 | INFO     | __main__:ocr_upload:3813 - معالجة صورة: .png
2025-06-07 02:52:10.006 | WARNING  | __main__:extract_text_from_image:2772 - فشل في التكوين --oem 3 --psm 6 -l ara+eng: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 02:52:10.048 | WARNING  | __main__:extract_text_from_image:2772 - فشل في التكوين --oem 3 --psm 6 -l eng: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 02:52:10.087 | WARNING  | __main__:extract_text_from_image:2772 - فشل في التكوين --oem 3 --psm 6 -l ara: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 02:52:10.124 | WARNING  | __main__:extract_text_from_image:2772 - فشل في التكوين --oem 3 --psm 4 -l ara+eng: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 02:52:10.162 | WARNING  | __main__:extract_text_from_image:2772 - فشل في التكوين --oem 3 --psm 8 -l ara+eng: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 02:52:10.202 | ERROR    | __main__:extract_text_from_image:2787 - فشل في التكوين البسيط: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 02:52:10.202 | WARNING  | __main__:extract_text_from_image:2789 - لم يتم العثور على نص في الصورة
2025-06-07 02:52:10.204 | INFO     | __main__:ocr_upload:3830 - تم حذف الملف المؤقت: C:\Users\<USER>\OneDrive\Desktop\M\uploads\temp\20250607_025209_Screenshot_2025-06-06_222044.png
2025-06-07 02:52:10.204 | WARNING  | __main__:ocr_upload:3857 - لم يتم العثور على نص في الملف
2025-06-07 02:52:13.370 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:52:15.036 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:52:35.880 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:52:45.046 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:53:05.875 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:53:13.049 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:53:15.350 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:53:35.562 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:54:06.346 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:54:13.040 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:54:13.350 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:54:36.046 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:55:06.355 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:55:13.044 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:55:13.367 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:55:35.562 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:55:35.955 | DEBUG    | __main__:before_request:4051 - طلب: POST http://localhost:5000/ocr-scanner/upload
2025-06-07 02:55:35.963 | INFO     | __main__:ocr_upload:3802 - تم حفظ الملف مؤقتاً: C:\Users\<USER>\OneDrive\Desktop\M\uploads\temp\20250607_025535_Algorithm_flowchart_example.pdf
2025-06-07 02:55:35.964 | INFO     | __main__:ocr_upload:3816 - معالجة ملف PDF
2025-06-07 02:55:35.966 | WARNING  | __main__:extract_text_from_pdf:2848 - PyMuPDF غير متوفر، محاولة استخدام طريقة بديلة
2025-06-07 02:55:35.969 | ERROR    | __main__:extract_text_from_pdf:2880 - pdf2image غير متوفر
2025-06-07 02:55:35.970 | WARNING  | __main__:extract_text_from_pdf:2882 - لم يتم العثور على نص في ملف PDF
2025-06-07 02:55:35.971 | INFO     | __main__:ocr_upload:3830 - تم حذف الملف المؤقت: C:\Users\<USER>\OneDrive\Desktop\M\uploads\temp\20250607_025535_Algorithm_flowchart_example.pdf
2025-06-07 02:55:35.973 | WARNING  | __main__:ocr_upload:3857 - لم يتم العثور على نص في الملف
2025-06-07 02:56:05.570 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:56:13.355 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:56:13.619 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:56:36.042 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:57:06.351 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:57:13.043 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:57:13.355 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:57:22.772 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/digital-signatures
2025-06-07 02:57:23.649 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:57:54.049 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:58:13.350 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:58:13.616 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:58:24.042 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:58:54.354 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:59:13.036 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:59:13.359 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:59:24.051 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 02:59:54.349 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:00:13.049 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:00:13.369 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:00:24.040 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:00:54.359 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:01:13.044 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:01:13.368 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:01:55.041 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:02:13.357 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:02:13.623 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:02:56.037 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:03:13.351 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:03:13.614 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:03:57.063 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:04:13.355 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:04:13.618 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:04:58.050 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:05:13.346 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:05:13.616 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:05:59.052 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:06:13.357 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:06:13.622 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:07:00.354 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:07:13.043 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:07:13.353 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:08:01.042 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:08:13.355 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:08:13.620 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:09:02.050 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:09:13.356 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:09:13.618 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:10:03.049 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:10:13.351 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:10:13.612 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:11:04.047 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:11:13.371 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:11:13.636 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:12:05.037 | DEBUG    | __main__:before_request:4051 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:12:11.184 | INFO     | __main__:init_database:3221 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 03:12:11.185 | ERROR    | __main__:init_database:3239 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 03:12:11.186 | INFO     | __main__:<module>:4120 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 03:12:13.351 | DEBUG    | __main__:before_request:4101 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:12:13.617 | DEBUG    | __main__:before_request:4101 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:12:13.666 | DEBUG    | __main__:before_request:4101 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 03:12:13.740 | DEBUG    | __main__:before_request:4101 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 03:12:26.767 | INFO     | __main__:init_database:3221 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 03:12:26.769 | INFO     | __main__:init_database:3222 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 03:12:26.775 | INFO     | __main__:init_database:3226 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 03:12:26.776 | ERROR    | __main__:init_database:3244 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 03:12:26.777 | INFO     | __main__:<module>:4125 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 03:13:06.352 | DEBUG    | __main__:before_request:4106 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:13:06.618 | DEBUG    | __main__:before_request:4106 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 03:13:12.485 | INFO     | __main__:init_database:3286 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 03:13:12.485 | INFO     | __main__:init_database:3287 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 03:13:12.490 | INFO     | __main__:init_database:3291 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 03:13:12.490 | ERROR    | __main__:init_database:3309 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 03:13:12.491 | INFO     | __main__:<module>:4190 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 03:13:13.352 | DEBUG    | __main__:before_request:4171 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:13:13.614 | DEBUG    | __main__:before_request:4171 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:13:13.659 | DEBUG    | __main__:before_request:4171 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 03:13:13.724 | DEBUG    | __main__:before_request:4171 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 03:13:57.064 | INFO     | __main__:init_database:3314 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 03:13:57.065 | INFO     | __main__:init_database:3315 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 03:13:57.072 | INFO     | __main__:init_database:3319 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 03:13:57.073 | ERROR    | __main__:init_database:3337 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 03:13:57.073 | INFO     | __main__:<module>:4218 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 03:14:07.354 | DEBUG    | __main__:before_request:4199 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:14:07.619 | DEBUG    | __main__:before_request:4199 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 03:14:13.041 | DEBUG    | __main__:before_request:4199 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:14:13.366 | DEBUG    | __main__:before_request:4199 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:14:13.373 | DEBUG    | __main__:before_request:4199 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 03:14:13.616 | DEBUG    | __main__:before_request:4199 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 03:14:23.687 | INFO     | __main__:init_database:3314 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 03:14:23.689 | INFO     | __main__:init_database:3315 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 03:14:23.697 | INFO     | __main__:init_database:3319 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 03:14:23.697 | ERROR    | __main__:init_database:3337 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 03:14:23.698 | INFO     | __main__:<module>:4241 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 03:14:48.517 | INFO     | __main__:init_database:3330 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 03:14:48.518 | INFO     | __main__:init_database:3331 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 03:14:48.525 | INFO     | __main__:init_database:3335 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 03:14:48.526 | ERROR    | __main__:init_database:3353 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 03:14:48.527 | INFO     | __main__:<module>:4257 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 03:15:08.352 | DEBUG    | __main__:before_request:4238 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:15:08.603 | DEBUG    | __main__:before_request:4238 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 03:15:16.669 | INFO     | __main__:init_database:3359 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 03:15:16.670 | INFO     | __main__:init_database:3360 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 03:15:16.678 | INFO     | __main__:init_database:3364 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 03:15:16.679 | ERROR    | __main__:init_database:3382 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 03:15:16.680 | INFO     | __main__:<module>:4286 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 03:15:16.735 | DEBUG    | __main__:before_request:4267 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:15:16.744 | DEBUG    | __main__:before_request:4267 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:15:17.065 | DEBUG    | __main__:before_request:4267 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 03:15:17.420 | DEBUG    | __main__:before_request:4267 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 03:15:50.505 | INFO     | __main__:init_database:3359 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 03:15:50.506 | INFO     | __main__:init_database:3360 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 03:15:50.512 | INFO     | __main__:init_database:3364 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 03:15:50.512 | ERROR    | __main__:init_database:3382 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 03:15:50.513 | INFO     | __main__:<module>:4332 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 03:16:09.353 | DEBUG    | __main__:before_request:4313 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:16:09.616 | DEBUG    | __main__:before_request:4313 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 03:16:13.046 | DEBUG    | __main__:before_request:4313 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:16:13.353 | DEBUG    | __main__:before_request:4313 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:16:13.357 | DEBUG    | __main__:before_request:4313 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 03:16:13.619 | DEBUG    | __main__:before_request:4313 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 03:17:10.174 | DEBUG    | __main__:before_request:4313 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:17:10.566 | DEBUG    | __main__:before_request:4313 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 03:17:13.055 | DEBUG    | __main__:before_request:4313 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:17:13.371 | DEBUG    | __main__:before_request:4313 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:17:13.375 | DEBUG    | __main__:before_request:4313 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 03:17:13.635 | DEBUG    | __main__:before_request:4313 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 03:18:11.048 | DEBUG    | __main__:before_request:4313 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:18:11.371 | DEBUG    | __main__:before_request:4313 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 03:18:13.040 | DEBUG    | __main__:before_request:4313 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:18:13.348 | DEBUG    | __main__:before_request:4313 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:18:13.354 | DEBUG    | __main__:before_request:4313 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 03:18:13.613 | DEBUG    | __main__:before_request:4313 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 03:19:12.039 | DEBUG    | __main__:before_request:4313 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:19:12.361 | DEBUG    | __main__:before_request:4313 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 03:19:13.042 | DEBUG    | __main__:before_request:4313 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:19:13.351 | DEBUG    | __main__:before_request:4313 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:19:13.355 | DEBUG    | __main__:before_request:4313 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 03:19:13.615 | DEBUG    | __main__:before_request:4313 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 03:19:41.543 | INFO     | __main__:init_database:3359 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 03:19:41.544 | INFO     | __main__:init_database:3360 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 03:19:41.547 | INFO     | __main__:init_database:3364 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 03:19:41.548 | ERROR    | __main__:init_database:3382 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 03:19:41.549 | INFO     | __main__:<module>:4364 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 03:19:57.164 | INFO     | __main__:init_database:3359 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 03:19:57.166 | INFO     | __main__:init_database:3360 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 03:19:57.179 | INFO     | __main__:init_database:3364 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 03:19:57.180 | ERROR    | __main__:init_database:3382 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 03:19:57.181 | INFO     | __main__:<module>:4366 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 03:20:19.691 | INFO     | __main__:init_database:3359 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 03:20:19.691 | INFO     | __main__:init_database:3360 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 03:20:19.695 | INFO     | __main__:init_database:3364 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 03:20:19.696 | ERROR    | __main__:init_database:3382 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 03:20:19.696 | INFO     | __main__:<module>:4366 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 03:20:22.757 | INFO     | __main__:init_database:3359 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 03:20:22.758 | INFO     | __main__:init_database:3360 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 03:20:22.766 | INFO     | __main__:init_database:3364 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 03:20:22.766 | ERROR    | __main__:init_database:3382 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 03:20:22.768 | INFO     | __main__:<module>:4366 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 03:20:44.047 | DEBUG    | __main__:before_request:4347 - طلب: GET http://localhost:5000/
2025-06-07 03:20:44.333 | DEBUG    | __main__:before_request:4347 - طلب: GET http://localhost:5000/login
2025-06-07 03:20:47.662 | DEBUG    | __main__:before_request:4347 - طلب: POST http://localhost:5000/login
2025-06-07 03:20:48.052 | INFO     | __main__:login:824 - تم تسجيل دخول المستخدم: admin
2025-06-07 03:20:48.375 | DEBUG    | __main__:before_request:4347 - طلب: GET http://localhost:5000/dashboard
2025-06-07 03:20:48.658 | DEBUG    | __main__:before_request:4347 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:20:58.085 | DEBUG    | __main__:before_request:4347 - طلب: GET http://localhost:5000/ocr-scanner
2025-06-07 03:20:58.353 | DEBUG    | __main__:before_request:4347 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:21:13.042 | DEBUG    | __main__:before_request:4347 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:21:13.349 | DEBUG    | __main__:before_request:4347 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:21:13.616 | DEBUG    | __main__:before_request:4347 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:21:28.249 | DEBUG    | __main__:before_request:4347 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:21:31.193 | DEBUG    | __main__:before_request:4347 - طلب: POST http://localhost:5000/ocr-scanner/upload
2025-06-07 03:21:31.200 | INFO     | __main__:ocr_upload:3995 - تم حفظ الملف مؤقتاً: C:\Users\<USER>\OneDrive\Desktop\M\uploads\temp\20250607_032131_Screenshot_2025-06-06_222044.png
2025-06-07 03:21:31.200 | INFO     | __main__:ocr_upload:4006 - معالجة صورة: .png
2025-06-07 03:21:31.214 | INFO     | __main__:extract_text_from_image:2798 - بدء معالجة الصورة: C:\Users\<USER>\OneDrive\Desktop\M\uploads\temp\20250607_032131_Screenshot_2025-06-06_222044.png
2025-06-07 03:21:31.299 | DEBUG    | __main__:extract_text_from_image:2850 - فشل في عربي + إنجليزي - فقرات: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 03:21:31.475 | DEBUG    | __main__:extract_text_from_image:2850 - فشل في عربي + إنجليزي - عمود واحد: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 03:21:31.518 | DEBUG    | __main__:extract_text_from_image:2850 - فشل في عربي + إنجليزي - صفحة كاملة: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 03:21:31.566 | DEBUG    | __main__:extract_text_from_image:2850 - فشل في إنجليزي - فقرات: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 03:21:31.601 | DEBUG    | __main__:extract_text_from_image:2850 - فشل في إنجليزي - كلمة واحدة: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 03:21:31.685 | DEBUG    | __main__:extract_text_from_image:2850 - فشل في إنجليزي - سطر واحد: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 03:21:31.750 | DEBUG    | __main__:extract_text_from_image:2850 - فشل في عربي - فقرات: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 03:21:31.834 | DEBUG    | __main__:extract_text_from_image:2850 - فشل في عربي - عمود واحد: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 03:21:31.918 | DEBUG    | __main__:extract_text_from_image:2850 - فشل في نص متناثر: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 03:21:31.994 | DEBUG    | __main__:extract_text_from_image:2850 - فشل في نص متناثر مع OSD: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 03:21:32.047 | DEBUG    | __main__:extract_text_from_image:2850 - فشل في عربي + إنجليزي - فقرات: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 03:21:32.101 | DEBUG    | __main__:extract_text_from_image:2850 - فشل في عربي + إنجليزي - عمود واحد: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 03:21:32.159 | DEBUG    | __main__:extract_text_from_image:2850 - فشل في عربي + إنجليزي - صفحة كاملة: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 03:21:32.215 | DEBUG    | __main__:extract_text_from_image:2850 - فشل في إنجليزي - فقرات: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 03:21:32.263 | DEBUG    | __main__:extract_text_from_image:2850 - فشل في إنجليزي - كلمة واحدة: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 03:21:32.303 | DEBUG    | __main__:extract_text_from_image:2850 - فشل في إنجليزي - سطر واحد: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 03:21:32.344 | DEBUG    | __main__:extract_text_from_image:2850 - فشل في عربي - فقرات: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 03:21:32.415 | DEBUG    | __main__:extract_text_from_image:2850 - فشل في عربي - عمود واحد: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 03:21:32.468 | DEBUG    | __main__:extract_text_from_image:2850 - فشل في نص متناثر: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 03:21:32.512 | DEBUG    | __main__:extract_text_from_image:2850 - فشل في نص متناثر مع OSD: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 03:21:32.565 | DEBUG    | __main__:extract_text_from_image:2850 - فشل في عربي + إنجليزي - فقرات: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 03:21:32.601 | DEBUG    | __main__:extract_text_from_image:2850 - فشل في عربي + إنجليزي - عمود واحد: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 03:21:32.646 | DEBUG    | __main__:extract_text_from_image:2850 - فشل في عربي + إنجليزي - صفحة كاملة: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 03:21:32.686 | DEBUG    | __main__:extract_text_from_image:2850 - فشل في إنجليزي - فقرات: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 03:21:32.733 | DEBUG    | __main__:extract_text_from_image:2850 - فشل في إنجليزي - كلمة واحدة: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 03:21:32.779 | DEBUG    | __main__:extract_text_from_image:2850 - فشل في إنجليزي - سطر واحد: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 03:21:32.825 | DEBUG    | __main__:extract_text_from_image:2850 - فشل في عربي - فقرات: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 03:21:32.862 | DEBUG    | __main__:extract_text_from_image:2850 - فشل في عربي - عمود واحد: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 03:21:32.911 | DEBUG    | __main__:extract_text_from_image:2850 - فشل في نص متناثر: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 03:21:32.961 | DEBUG    | __main__:extract_text_from_image:2850 - فشل في نص متناثر مع OSD: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 03:21:33.012 | ERROR    | __main__:extract_text_from_image:2868 - فشل في التكوين البسيط: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 03:21:33.013 | WARNING  | __main__:extract_text_from_image:2870 - لم يتم العثور على نص في الصورة مع جميع التكوينات
2025-06-07 03:21:33.014 | INFO     | __main__:ocr_upload:4023 - تم حذف الملف المؤقت: C:\Users\<USER>\OneDrive\Desktop\M\uploads\temp\20250607_032131_Screenshot_2025-06-06_222044.png
2025-06-07 03:21:33.015 | WARNING  | __main__:ocr_upload:4050 - لم يتم العثور على نص في الملف
2025-06-07 03:21:44.624 | DEBUG    | __main__:before_request:4347 - طلب: POST http://localhost:5000/ocr-scanner/upload
2025-06-07 03:21:44.636 | INFO     | __main__:ocr_upload:3995 - تم حفظ الملف مؤقتاً: C:\Users\<USER>\OneDrive\Desktop\M\uploads\temp\20250607_032144_Algorithm_flowchart_example.pdf
2025-06-07 03:21:44.637 | INFO     | __main__:ocr_upload:4009 - معالجة ملف PDF
2025-06-07 03:21:44.637 | INFO     | __main__:extract_text_from_pdf:2880 - بدء معالجة ملف PDF: C:\Users\<USER>\OneDrive\Desktop\M\uploads\temp\20250607_032144_Algorithm_flowchart_example.pdf
2025-06-07 03:21:44.640 | WARNING  | __main__:extract_text_from_pdf:2941 - PyMuPDF غير متوفر، محاولة استخدام طريقة بديلة
2025-06-07 03:21:44.644 | ERROR    | __main__:extract_text_from_pdf:2987 - pdf2image غير متوفر. يرجى تثبيته: pip install pdf2image
2025-06-07 03:21:44.646 | WARNING  | __main__:extract_text_from_pdf:2991 - لم يتم العثور على نص في ملف PDF مع جميع الطرق
2025-06-07 03:21:44.648 | INFO     | __main__:ocr_upload:4023 - تم حذف الملف المؤقت: C:\Users\<USER>\OneDrive\Desktop\M\uploads\temp\20250607_032144_Algorithm_flowchart_example.pdf
2025-06-07 03:21:44.649 | WARNING  | __main__:ocr_upload:4050 - لم يتم العثور على نص في الملف
2025-06-07 03:21:53.160 | DEBUG    | __main__:before_request:4347 - طلب: GET http://localhost:5000/dashboard
2025-06-07 03:21:53.427 | DEBUG    | __main__:before_request:4347 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:22:01.231 | DEBUG    | __main__:before_request:4347 - طلب: GET http://localhost:5000/documents/add
2025-06-07 03:22:01.676 | DEBUG    | __main__:before_request:4347 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:22:13.043 | DEBUG    | __main__:before_request:4347 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:22:13.366 | DEBUG    | __main__:before_request:4347 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:22:13.632 | DEBUG    | __main__:before_request:4347 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:22:23.016 | DEBUG    | __main__:before_request:4347 - طلب: POST http://localhost:5000/documents/add
2025-06-07 03:22:23.021 | ERROR    | __main__:add_document:984 - خطأ في إضافة الوثيقة: generate_barcode() missing 1 required positional argument: 'data'
2025-06-07 03:22:23.446 | DEBUG    | __main__:before_request:4347 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:22:39.384 | DEBUG    | __main__:before_request:4347 - طلب: POST http://localhost:5000/documents/add
2025-06-07 03:22:39.390 | ERROR    | __main__:add_document:984 - خطأ في إضافة الوثيقة: generate_barcode() missing 1 required positional argument: 'data'
2025-06-07 03:22:39.810 | DEBUG    | __main__:before_request:4347 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:23:10.042 | DEBUG    | __main__:before_request:4347 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:23:16.487 | INFO     | __main__:init_database:3359 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 03:23:16.488 | INFO     | __main__:init_database:3360 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 03:23:16.495 | INFO     | __main__:init_database:3364 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 03:23:16.495 | ERROR    | __main__:init_database:3382 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 03:23:16.497 | INFO     | __main__:<module>:4366 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 03:23:16.551 | DEBUG    | __main__:before_request:4347 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:23:16.556 | DEBUG    | __main__:before_request:4347 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:23:16.876 | DEBUG    | __main__:before_request:4347 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 03:23:16.879 | DEBUG    | __main__:before_request:4347 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:23:17.248 | DEBUG    | __main__:before_request:4347 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 03:23:17.556 | DEBUG    | __main__:before_request:4347 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 03:23:38.757 | INFO     | __main__:init_database:3359 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 03:23:38.759 | INFO     | __main__:init_database:3360 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 03:23:38.766 | INFO     | __main__:init_database:3364 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 03:23:38.766 | ERROR    | __main__:init_database:3382 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 03:23:38.767 | INFO     | __main__:<module>:4366 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 03:23:40.355 | DEBUG    | __main__:before_request:4347 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:23:40.618 | DEBUG    | __main__:before_request:4347 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 03:27:19.469 | INFO     | __main__:init_database:3359 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 03:27:19.470 | INFO     | __main__:init_database:3360 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 03:27:19.481 | INFO     | __main__:init_database:3364 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 03:27:19.481 | ERROR    | __main__:init_database:3382 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 03:27:19.483 | INFO     | __main__:<module>:4366 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 03:27:22.485 | INFO     | __main__:init_database:3359 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 03:27:22.486 | INFO     | __main__:init_database:3360 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 03:27:22.502 | INFO     | __main__:init_database:3364 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 03:27:22.504 | ERROR    | __main__:init_database:3382 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 03:27:22.512 | INFO     | __main__:<module>:4366 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 03:28:12.356 | DEBUG    | __main__:before_request:4347 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:28:12.618 | DEBUG    | __main__:before_request:4347 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 03:28:13.054 | DEBUG    | __main__:before_request:4347 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:28:13.361 | DEBUG    | __main__:before_request:4347 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:28:13.365 | DEBUG    | __main__:before_request:4347 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 03:28:13.622 | DEBUG    | __main__:before_request:4347 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:28:13.670 | DEBUG    | __main__:before_request:4347 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 03:28:13.677 | DEBUG    | __main__:before_request:4347 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 03:28:21.761 | DEBUG    | __main__:before_request:4347 - طلب: GET http://localhost:5000/documents/add
2025-06-07 03:28:21.766 | DEBUG    | __main__:before_request:4347 - طلب: GET http://localhost:5000/login?next=/documents/add
2025-06-07 03:28:50.675 | DEBUG    | __main__:before_request:4347 - طلب: GET http://localhost:5000/documents/add
2025-06-07 03:28:50.938 | DEBUG    | __main__:before_request:4347 - طلب: GET http://localhost:5000/login?next=/documents/add
2025-06-07 03:28:54.324 | DEBUG    | __main__:before_request:4347 - طلب: POST http://localhost:5000/login?next=/documents/add
2025-06-07 03:28:54.713 | INFO     | __main__:login:824 - تم تسجيل دخول المستخدم: admin
2025-06-07 03:28:55.036 | DEBUG    | __main__:before_request:4347 - طلب: GET http://localhost:5000/documents/add
2025-06-07 03:29:11.005 | DEBUG    | __main__:before_request:4347 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:29:12.715 | DEBUG    | __main__:before_request:4347 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:29:15.781 | DEBUG    | __main__:before_request:4347 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:29:37.452 | DEBUG    | __main__:before_request:4347 - طلب: POST http://localhost:5000/documents/add
2025-06-07 03:29:37.558 | INFO     | __main__:add_document:962 - تم حفظ الملف: 20250607_032937_docx
2025-06-07 03:29:37.649 | INFO     | __main__:log_activity:2058 - تم تسجيل النشاط: admin - create - document:1
2025-06-07 03:29:37.653 | INFO     | __main__:add_document:978 - تم إضافة وثيقة جديدة: وووو (ID: 1) بواسطة admin
2025-06-07 03:29:37.981 | DEBUG    | __main__:before_request:4347 - طلب: GET http://localhost:5000/documents/1
2025-06-07 03:29:38.234 | DEBUG    | __main__:before_request:4347 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:29:40.206 | DEBUG    | __main__:before_request:4347 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:30:08.462 | DEBUG    | __main__:before_request:4347 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:30:10.039 | DEBUG    | __main__:before_request:4347 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:30:11.418 | DEBUG    | __main__:before_request:4347 - طلب: GET http://localhost:5000/documents/1/qr
2025-06-07 03:30:16.514 | DEBUG    | __main__:before_request:4347 - طلب: GET http://localhost:5000/documents/1
2025-06-07 03:30:29.406 | DEBUG    | __main__:before_request:4347 - طلب: GET http://localhost:5000/documents/1/sign
2025-06-07 03:30:37.367 | DEBUG    | __main__:before_request:4347 - طلب: GET http://localhost:5000/documents/1/ocr
2025-06-07 03:31:00.299 | DEBUG    | __main__:before_request:4347 - طلب: GET http://localhost:5000/documents/1/edit
2025-06-07 03:31:00.728 | DEBUG    | __main__:before_request:4347 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:31:06.062 | DEBUG    | __main__:before_request:4347 - طلب: GET http://localhost:5000/documents/1/qr
2025-06-07 03:31:11.053 | DEBUG    | __main__:before_request:4347 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:32:03.288 | DEBUG    | __main__:before_request:4347 - طلب: GET http://localhost:5000/documents/1/label
2025-06-07 03:32:12.038 | DEBUG    | __main__:before_request:4347 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:32:29.330 | DEBUG    | __main__:before_request:4347 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:32:30.455 | DEBUG    | __main__:before_request:4347 - طلب: GET http://localhost:5000/documents/1/signatures
2025-06-07 03:32:42.211 | DEBUG    | __main__:before_request:4347 - طلب: GET http://localhost:5000/documents/1/sign
2025-06-07 03:33:13.098 | DEBUG    | __main__:before_request:4347 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:33:21.948 | DEBUG    | __main__:before_request:4347 - طلب: GET http://localhost:5000/documents/1/signatures
2025-06-07 03:34:13.359 | DEBUG    | __main__:before_request:4347 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:34:40.821 | INFO     | __main__:init_database:3359 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 03:34:40.822 | INFO     | __main__:init_database:3360 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 03:34:40.829 | INFO     | __main__:init_database:3364 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 03:34:40.829 | ERROR    | __main__:init_database:3382 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 03:34:40.830 | INFO     | __main__:<module>:4366 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 03:35:13.341 | DEBUG    | __main__:before_request:4347 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:35:13.611 | DEBUG    | __main__:before_request:4347 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 03:35:18.513 | INFO     | __main__:init_database:3359 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 03:35:18.514 | INFO     | __main__:init_database:3360 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 03:35:18.519 | INFO     | __main__:init_database:3364 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 03:35:18.520 | ERROR    | __main__:init_database:3382 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 03:35:18.521 | INFO     | __main__:<module>:4336 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 03:36:13.356 | DEBUG    | __main__:before_request:4317 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:36:13.620 | DEBUG    | __main__:before_request:4317 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 03:37:13.354 | DEBUG    | __main__:before_request:4317 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:37:13.618 | DEBUG    | __main__:before_request:4317 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 03:38:13.049 | DEBUG    | __main__:before_request:4317 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:38:13.368 | DEBUG    | __main__:before_request:4317 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 03:39:13.044 | DEBUG    | __main__:before_request:4317 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:39:13.363 | DEBUG    | __main__:before_request:4317 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 03:40:02.359 | INFO     | __main__:init_database:3359 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 03:40:02.360 | INFO     | __main__:init_database:3360 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 03:40:02.365 | INFO     | __main__:init_database:3364 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 03:40:02.366 | ERROR    | __main__:init_database:3382 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 03:40:02.366 | INFO     | __main__:<module>:4336 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 03:40:13.359 | DEBUG    | __main__:before_request:4317 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:40:13.663 | DEBUG    | __main__:before_request:4317 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 03:40:17.829 | INFO     | __main__:init_database:3358 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 03:40:17.830 | INFO     | __main__:init_database:3359 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 03:40:17.836 | INFO     | __main__:init_database:3363 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 03:40:17.836 | ERROR    | __main__:init_database:3381 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 03:40:17.837 | INFO     | __main__:<module>:4335 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 03:40:32.526 | INFO     | __main__:init_database:3355 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 03:40:32.528 | INFO     | __main__:init_database:3356 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 03:40:32.534 | INFO     | __main__:init_database:3360 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 03:40:32.534 | ERROR    | __main__:init_database:3378 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 03:40:32.536 | INFO     | __main__:<module>:4332 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 03:40:50.165 | INFO     | __main__:init_database:3355 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 03:40:50.166 | INFO     | __main__:init_database:3356 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 03:40:50.171 | INFO     | __main__:init_database:3360 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 03:40:50.172 | ERROR    | __main__:init_database:3378 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 03:40:50.172 | INFO     | __main__:<module>:4339 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 03:41:13.362 | DEBUG    | __main__:before_request:4320 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:41:13.625 | DEBUG    | __main__:before_request:4320 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 03:41:27.921 | INFO     | __main__:init_database:3355 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 03:41:27.922 | INFO     | __main__:init_database:3356 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 03:41:27.928 | INFO     | __main__:init_database:3360 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 03:41:27.928 | ERROR    | __main__:init_database:3378 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 03:41:27.929 | INFO     | __main__:<module>:4350 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 03:45:38.272 | INFO     | __main__:init_database:3355 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 03:45:38.273 | INFO     | __main__:init_database:3356 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 03:45:38.280 | INFO     | __main__:init_database:3360 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 03:45:38.280 | ERROR    | __main__:init_database:3378 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 03:45:38.281 | INFO     | __main__:<module>:4350 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 03:45:41.380 | INFO     | __main__:init_database:3355 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 03:45:41.381 | INFO     | __main__:init_database:3356 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 03:45:41.395 | INFO     | __main__:init_database:3360 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 03:45:41.395 | ERROR    | __main__:init_database:3378 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 03:45:41.397 | INFO     | __main__:<module>:4350 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 03:46:13.357 | DEBUG    | __main__:before_request:4331 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:46:13.658 | DEBUG    | __main__:before_request:4331 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 03:46:28.732 | INFO     | __main__:init_database:3355 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 03:46:28.732 | INFO     | __main__:init_database:3356 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 03:46:28.736 | INFO     | __main__:init_database:3360 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 03:46:28.737 | ERROR    | __main__:init_database:3378 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 03:46:28.737 | INFO     | __main__:<module>:4350 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 03:46:31.934 | INFO     | __main__:init_database:3355 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 03:46:31.935 | INFO     | __main__:init_database:3356 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 03:46:31.946 | INFO     | __main__:init_database:3360 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 03:46:31.947 | ERROR    | __main__:init_database:3378 - خطأ في إنشاء سجل النشاط الأولي: name 'admin_user' is not defined
2025-06-07 03:46:31.949 | INFO     | __main__:<module>:4350 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 03:47:12.063 | DEBUG    | __main__:before_request:4331 - طلب: GET http://127.0.0.1:5000/
2025-06-07 03:47:12.074 | DEBUG    | __main__:before_request:4331 - طلب: GET http://127.0.0.1:5000/login
2025-06-07 03:47:12.463 | WARNING  | __main__:not_found_error:4302 - صفحة غير موجودة: http://127.0.0.1:5000/favicon.ico
2025-06-07 03:47:13.356 | DEBUG    | __main__:before_request:4331 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:47:13.619 | DEBUG    | __main__:before_request:4331 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 03:47:16.872 | DEBUG    | __main__:before_request:4331 - طلب: GET http://127.0.0.1:5000/forgot-password
2025-06-07 03:47:24.401 | INFO     | __main__:init_database:3355 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 03:47:24.408 | INFO     | __main__:init_database:3356 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 03:47:24.417 | INFO     | __main__:init_database:3360 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 03:47:24.523 | INFO     | __main__:init_database:3378 - تم إنشاء سجل النشاط الأولي
2025-06-07 03:47:24.540 | INFO     | __main__:<module>:4352 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 03:47:24.688 | DEBUG    | __main__:before_request:4333 - طلب: GET http://127.0.0.1:5000/login
2025-06-07 03:47:24.895 | DEBUG    | __main__:before_request:4333 - طلب: GET http://127.0.0.1:5000/login
2025-06-07 03:47:27.350 | DEBUG    | __main__:before_request:4333 - طلب: POST http://127.0.0.1:5000/login
2025-06-07 03:47:27.800 | INFO     | __main__:login:824 - تم تسجيل دخول المستخدم: admin
2025-06-07 03:47:27.822 | DEBUG    | __main__:before_request:4333 - طلب: GET http://127.0.0.1:5000/dashboard
2025-06-07 03:47:28.224 | DEBUG    | __main__:before_request:4333 - طلب: GET http://127.0.0.1:5000/api/notifications/unread-count
2025-06-07 03:47:40.770 | INFO     | __main__:init_database:3355 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 03:47:40.788 | INFO     | __main__:init_database:3356 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 03:47:40.805 | INFO     | __main__:init_database:3360 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 03:47:40.858 | INFO     | __main__:init_database:3378 - تم إنشاء سجل النشاط الأولي
2025-06-07 03:47:40.883 | INFO     | __main__:<module>:4352 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 03:47:41.154 | DEBUG    | __main__:before_request:4333 - طلب: GET http://127.0.0.1:5000/documents
2025-06-07 03:47:41.208 | DEBUG    | __main__:before_request:4333 - طلب: GET http://127.0.0.1:5000/login?next=/documents
2025-06-07 03:47:44.436 | DEBUG    | __main__:before_request:4333 - طلب: POST http://127.0.0.1:5000/login?next=/documents
2025-06-07 03:47:44.875 | INFO     | __main__:login:824 - تم تسجيل دخول المستخدم: admin
2025-06-07 03:47:44.893 | DEBUG    | __main__:before_request:4333 - طلب: GET http://127.0.0.1:5000/documents
2025-06-07 03:47:45.229 | DEBUG    | __main__:before_request:4333 - طلب: GET http://127.0.0.1:5000/api/notifications/unread-count
2025-06-07 03:47:48.179 | DEBUG    | __main__:before_request:4333 - طلب: GET http://127.0.0.1:5000/documents/add
2025-06-07 03:47:48.471 | DEBUG    | __main__:before_request:4333 - طلب: GET http://127.0.0.1:5000/api/notifications/unread-count
2025-06-07 03:47:51.086 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/
2025-06-07 03:47:51.336 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/login
2025-06-07 03:47:59.569 | DEBUG    | __main__:before_request:4333 - طلب: POST http://localhost:5000/login
2025-06-07 03:47:59.948 | INFO     | __main__:login:824 - تم تسجيل دخول المستخدم: admin
2025-06-07 03:48:00.283 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/dashboard
2025-06-07 03:48:00.559 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:48:06.357 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/documents/add
2025-06-07 03:48:06.622 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:48:13.060 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:48:25.989 | DEBUG    | __main__:before_request:4333 - طلب: POST http://localhost:5000/documents/add
2025-06-07 03:48:26.040 | INFO     | __main__:add_document:961 - تم حفظ الملف: 20250607_034826_docx
2025-06-07 03:48:26.115 | INFO     | __main__:log_activity:2054 - تم تسجيل النشاط: admin - create - document:1
2025-06-07 03:48:26.118 | INFO     | __main__:add_document:977 - تم إضافة وثيقة جديدة: ةة (ID: 1) بواسطة admin
2025-06-07 03:48:26.249 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/documents/1
2025-06-07 03:48:26.501 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:48:45.500 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/documents/1/qr
2025-06-07 03:48:45.508 | ERROR    | __main__:document_qr:3571 - خطأ في توليد QR code للوثيقة 1: Could not build url for endpoint 'view_document' with values ['doc_id']. Did you forget to specify values ['id']?
2025-06-07 03:48:48.980 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/documents/1
2025-06-07 03:48:54.902 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/documents/1/sign
2025-06-07 03:49:02.764 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/documents/1/qr
2025-06-07 03:49:02.772 | ERROR    | __main__:document_qr:3571 - خطأ في توليد QR code للوثيقة 1: Could not build url for endpoint 'view_document' with values ['doc_id']. Did you forget to specify values ['id']?
2025-06-07 03:49:13.044 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:49:37.348 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:49:42.346 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/documents/1/qr
2025-06-07 03:49:42.351 | ERROR    | __main__:document_qr:3571 - خطأ في توليد QR code للوثيقة 1: Could not build url for endpoint 'view_document' with values ['doc_id']. Did you forget to specify values ['id']?
2025-06-07 03:50:13.342 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:50:15.681 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/documents/1/sign
2025-06-07 03:51:05.976 | INFO     | __main__:init_database:3355 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 03:51:05.977 | INFO     | __main__:init_database:3356 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 03:51:05.983 | INFO     | __main__:init_database:3360 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 03:51:06.008 | INFO     | __main__:init_database:3378 - تم إنشاء سجل النشاط الأولي
2025-06-07 03:51:06.009 | INFO     | __main__:<module>:4352 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 03:51:13.347 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:51:13.602 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 03:51:18.350 | INFO     | __main__:init_database:3355 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 03:51:18.350 | INFO     | __main__:init_database:3356 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 03:51:18.356 | INFO     | __main__:init_database:3360 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 03:51:18.379 | INFO     | __main__:init_database:3378 - تم إنشاء سجل النشاط الأولي
2025-06-07 03:51:18.380 | INFO     | __main__:<module>:4352 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 03:51:30.597 | INFO     | __main__:init_database:3355 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 03:51:30.599 | INFO     | __main__:init_database:3356 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 03:51:30.608 | INFO     | __main__:init_database:3360 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 03:51:30.632 | INFO     | __main__:init_database:3378 - تم إنشاء سجل النشاط الأولي
2025-06-07 03:51:30.633 | INFO     | __main__:<module>:4352 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 03:51:46.694 | INFO     | __main__:init_database:3355 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 03:51:46.695 | INFO     | __main__:init_database:3356 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 03:51:46.702 | INFO     | __main__:init_database:3360 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 03:51:46.738 | INFO     | __main__:init_database:3378 - تم إنشاء سجل النشاط الأولي
2025-06-07 03:51:46.739 | INFO     | __main__:<module>:4352 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 03:51:59.098 | INFO     | __main__:init_database:3355 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 03:51:59.099 | INFO     | __main__:init_database:3356 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 03:51:59.108 | INFO     | __main__:init_database:3360 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 03:51:59.130 | INFO     | __main__:init_database:3378 - تم إنشاء سجل النشاط الأولي
2025-06-07 03:51:59.132 | INFO     | __main__:<module>:4352 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 03:52:13.358 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:52:13.624 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 03:52:50.652 | INFO     | __main__:init_database:3355 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 03:52:50.653 | INFO     | __main__:init_database:3356 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 03:52:50.665 | INFO     | __main__:init_database:3360 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 03:52:50.696 | INFO     | __main__:init_database:3378 - تم إنشاء سجل النشاط الأولي
2025-06-07 03:52:50.703 | INFO     | __main__:<module>:4352 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 03:53:03.235 | INFO     | __main__:init_database:3355 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 03:53:03.239 | INFO     | __main__:init_database:3356 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 03:53:03.249 | INFO     | __main__:init_database:3360 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 03:53:03.276 | INFO     | __main__:init_database:3378 - تم إنشاء سجل النشاط الأولي
2025-06-07 03:53:03.281 | INFO     | __main__:<module>:4352 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 03:53:13.356 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:53:13.625 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 03:54:13.053 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:54:13.376 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 03:55:24.860 | INFO     | __main__:init_database:3355 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 03:55:24.861 | INFO     | __main__:init_database:3356 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 03:55:24.867 | INFO     | __main__:init_database:3360 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 03:55:24.887 | INFO     | __main__:init_database:3378 - تم إنشاء سجل النشاط الأولي
2025-06-07 03:55:24.889 | INFO     | __main__:<module>:4352 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 03:55:28.234 | INFO     | __main__:init_database:3355 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 03:55:28.235 | INFO     | __main__:init_database:3356 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 03:55:28.244 | INFO     | __main__:init_database:3360 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 03:55:28.273 | INFO     | __main__:init_database:3378 - تم إنشاء سجل النشاط الأولي
2025-06-07 03:55:28.274 | INFO     | __main__:<module>:4352 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 03:56:02.835 | INFO     | __main__:init_database:3355 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 03:56:02.836 | INFO     | __main__:init_database:3356 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 03:56:02.842 | INFO     | __main__:init_database:3360 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 03:56:02.863 | INFO     | __main__:init_database:3378 - تم إنشاء سجل النشاط الأولي
2025-06-07 03:56:02.863 | INFO     | __main__:<module>:4352 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 03:56:06.101 | INFO     | __main__:init_database:3355 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 03:56:06.108 | INFO     | __main__:init_database:3356 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 03:56:06.118 | INFO     | __main__:init_database:3360 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 03:56:06.155 | INFO     | __main__:init_database:3378 - تم إنشاء سجل النشاط الأولي
2025-06-07 03:56:06.159 | INFO     | __main__:<module>:4352 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 03:56:13.346 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:56:13.610 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 03:56:16.881 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/
2025-06-07 03:56:17.210 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/login
2025-06-07 03:56:26.098 | DEBUG    | __main__:before_request:4333 - طلب: POST http://localhost:5000/login
2025-06-07 03:56:26.562 | INFO     | __main__:login:824 - تم تسجيل دخول المستخدم: admin
2025-06-07 03:56:26.897 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/dashboard
2025-06-07 03:56:27.175 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:56:38.217 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/calendar
2025-06-07 03:56:38.468 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:56:38.653 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/calendar/events?start=2025-06-01T00:00:00%2B03:00&end=2025-07-13T00:00:00%2B03:00
2025-06-07 03:56:38.684 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/calendar/events
2025-06-07 03:56:40.760 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/calendar/add
2025-06-07 03:56:51.302 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/documents
2025-06-07 03:56:51.767 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:56:53.663 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/documents/add
2025-06-07 03:56:54.113 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:57:11.238 | DEBUG    | __main__:before_request:4333 - طلب: POST http://localhost:5000/documents/add
2025-06-07 03:57:11.277 | INFO     | __main__:add_document:961 - تم حفظ الملف: 20250607_035711_docx
2025-06-07 03:57:11.341 | INFO     | __main__:log_activity:2054 - تم تسجيل النشاط: admin - create - document:1
2025-06-07 03:57:11.344 | INFO     | __main__:add_document:977 - تم إضافة وثيقة جديدة: ءي (ID: 1) بواسطة admin
2025-06-07 03:57:11.661 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/documents/1
2025-06-07 03:57:11.924 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:57:31.389 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/documents/1/qr
2025-06-07 03:57:42.182 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:57:57.946 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/documents/1/sign
2025-06-07 03:57:58.457 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:58:18.745 | DEBUG    | __main__:before_request:4333 - طلب: POST http://localhost:5000/documents/1/sign/normal
2025-06-07 03:58:18.795 | INFO     | __main__:log_activity:2054 - تم تسجيل النشاط: admin - sign - document:1
2025-06-07 03:58:18.817 | INFO     | __main__:sign_document:4141 - تم توقيع عادي للوثيقة 1 بواسطة admin
2025-06-07 03:58:21.176 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/documents/1
2025-06-07 03:58:21.440 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:58:33.415 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/dashboard
2025-06-07 03:58:34.326 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:58:42.920 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/digital-signatures
2025-06-07 03:58:43.385 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:58:51.641 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/signatures/1/verify
2025-06-07 03:58:51.644 | INFO     | __main__:verify_signature:4210 - بدء التحقق من التوقيع 1
2025-06-07 03:58:51.683 | INFO     | __main__:log_activity:2054 - تم تسجيل النشاط: admin - verify - signature:1
2025-06-07 03:58:51.685 | WARNING  | __main__:verify_signature:4228 - التوقيع 1 غير صحيح
2025-06-07 03:58:51.998 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/documents/1/signatures
2025-06-07 03:58:52.002 | INFO     | __main__:view_signatures:4165 - عرض توقيعات الوثيقة 1 - عدد التوقيعات: 1
2025-06-07 03:58:52.263 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:59:10.007 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/signatures/1/certificate
2025-06-07 03:59:22.470 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 03:59:40.457 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/digital-signatures
2025-06-07 04:00:11.351 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:00:41.044 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:01:11.349 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:01:41.054 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:02:11.354 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:02:41.044 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:03:11.353 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:03:27.105 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/documents
2025-06-07 04:03:27.581 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:03:31.073 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/documents/1
2025-06-07 04:03:31.529 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:04:02.042 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:04:32.356 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:05:02.046 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:05:32.362 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:06:02.048 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:06:32.353 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:07:02.039 | DEBUG    | __main__:before_request:4333 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:07:45.074 | INFO     | __main__:init_database:3434 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 04:07:45.076 | INFO     | __main__:init_database:3435 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 04:07:45.083 | INFO     | __main__:init_database:3439 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 04:07:45.108 | INFO     | __main__:init_database:3457 - تم إنشاء سجل النشاط الأولي
2025-06-07 04:07:45.109 | INFO     | __main__:<module>:4431 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 04:08:03.349 | DEBUG    | __main__:before_request:4412 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:08:03.618 | DEBUG    | __main__:before_request:4412 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 04:08:19.117 | INFO     | __main__:init_database:3508 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 04:08:19.119 | INFO     | __main__:init_database:3509 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 04:08:19.125 | INFO     | __main__:init_database:3513 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 04:08:19.151 | INFO     | __main__:init_database:3531 - تم إنشاء سجل النشاط الأولي
2025-06-07 04:08:19.153 | INFO     | __main__:<module>:4505 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 04:09:04.356 | DEBUG    | __main__:before_request:4486 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:09:04.608 | DEBUG    | __main__:before_request:4486 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 04:09:17.024 | INFO     | __main__:init_database:3508 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 04:09:17.025 | INFO     | __main__:init_database:3509 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 04:09:17.032 | INFO     | __main__:init_database:3513 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 04:09:17.053 | INFO     | __main__:init_database:3531 - تم إنشاء سجل النشاط الأولي
2025-06-07 04:09:17.057 | INFO     | __main__:<module>:4532 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 04:09:58.074 | INFO     | __main__:init_database:3512 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 04:09:58.075 | INFO     | __main__:init_database:3513 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 04:09:58.083 | INFO     | __main__:init_database:3517 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 04:09:58.139 | INFO     | __main__:init_database:3535 - تم إنشاء سجل النشاط الأولي
2025-06-07 04:09:58.140 | INFO     | __main__:<module>:4536 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 04:10:05.352 | DEBUG    | __main__:before_request:4517 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:10:05.619 | DEBUG    | __main__:before_request:4517 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 04:10:15.968 | INFO     | __main__:init_database:3512 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 04:10:15.968 | INFO     | __main__:init_database:3513 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 04:10:15.975 | INFO     | __main__:init_database:3517 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 04:10:15.996 | INFO     | __main__:init_database:3535 - تم إنشاء سجل النشاط الأولي
2025-06-07 04:10:15.997 | INFO     | __main__:<module>:4542 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 04:11:06.360 | DEBUG    | __main__:before_request:4523 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:11:06.625 | DEBUG    | __main__:before_request:4523 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 04:11:16.011 | INFO     | __main__:init_database:3512 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 04:11:16.012 | INFO     | __main__:init_database:3513 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 04:11:16.016 | INFO     | __main__:init_database:3517 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 04:11:16.037 | INFO     | __main__:init_database:3535 - تم إنشاء سجل النشاط الأولي
2025-06-07 04:11:16.038 | INFO     | __main__:<module>:4574 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 04:14:23.895 | INFO     | __main__:init_database:3512 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 04:14:23.896 | INFO     | __main__:init_database:3513 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 04:14:23.901 | INFO     | __main__:init_database:3517 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 04:14:23.922 | INFO     | __main__:init_database:3535 - تم إنشاء سجل النشاط الأولي
2025-06-07 04:14:23.923 | INFO     | __main__:<module>:4574 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 04:14:27.185 | INFO     | __main__:init_database:3512 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 04:14:27.186 | INFO     | __main__:init_database:3513 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 04:14:27.194 | INFO     | __main__:init_database:3517 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 04:14:27.221 | INFO     | __main__:init_database:3535 - تم إنشاء سجل النشاط الأولي
2025-06-07 04:14:27.225 | INFO     | __main__:<module>:4574 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 04:18:35.106 | INFO     | __main__:init_database:3511 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 04:18:35.107 | INFO     | __main__:init_database:3512 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 04:18:35.112 | INFO     | __main__:init_database:3516 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 04:18:35.132 | INFO     | __main__:init_database:3534 - تم إنشاء سجل النشاط الأولي
2025-06-07 04:18:35.133 | INFO     | __main__:<module>:4573 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 04:18:38.139 | INFO     | __main__:init_database:3511 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 04:18:38.141 | INFO     | __main__:init_database:3512 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 04:18:38.148 | INFO     | __main__:init_database:3516 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 04:18:38.221 | INFO     | __main__:init_database:3534 - تم إنشاء سجل النشاط الأولي
2025-06-07 04:18:38.232 | INFO     | __main__:<module>:4573 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 04:19:00.126 | DEBUG    | __main__:before_request:4554 - طلب: GET http://127.0.0.1:5000/
2025-06-07 04:19:00.139 | DEBUG    | __main__:before_request:4554 - طلب: GET http://127.0.0.1:5000/login
2025-06-07 04:19:03.461 | DEBUG    | __main__:before_request:4554 - طلب: POST http://127.0.0.1:5000/login
2025-06-07 04:19:03.841 | INFO     | __main__:login:848 - تم تسجيل دخول المستخدم: admin
2025-06-07 04:19:03.852 | DEBUG    | __main__:before_request:4554 - طلب: GET http://127.0.0.1:5000/dashboard
2025-06-07 04:19:04.127 | DEBUG    | __main__:before_request:4554 - طلب: GET http://127.0.0.1:5000/api/notifications/unread-count
2025-06-07 04:19:24.149 | DEBUG    | __main__:before_request:4554 - طلب: GET http://127.0.0.1:5000/documents
2025-06-07 04:19:24.326 | DEBUG    | __main__:before_request:4554 - طلب: GET http://127.0.0.1:5000/api/notifications/unread-count
2025-06-07 04:19:26.100 | DEBUG    | __main__:before_request:4554 - طلب: GET http://127.0.0.1:5000/documents/add
2025-06-07 04:19:26.258 | DEBUG    | __main__:before_request:4554 - طلب: GET http://127.0.0.1:5000/api/notifications/unread-count
2025-06-07 04:19:56.272 | DEBUG    | __main__:before_request:4554 - طلب: GET http://127.0.0.1:5000/api/notifications/unread-count
2025-06-07 04:20:25.204 | DEBUG    | __main__:before_request:4554 - طلب: POST http://127.0.0.1:5000/documents/add
2025-06-07 04:20:25.229 | INFO     | __main__:add_document:985 - تم حفظ الملف: 20250607_042025_docx
2025-06-07 04:20:25.306 | INFO     | __main__:log_activity:2078 - تم تسجيل النشاط: admin - create - document:1
2025-06-07 04:20:25.308 | INFO     | __main__:add_document:1001 - تم إضافة وثيقة جديدة: ؤلاب (ID: 1) بواسطة admin
2025-06-07 04:20:25.322 | DEBUG    | __main__:before_request:4554 - طلب: GET http://127.0.0.1:5000/documents/1
2025-06-07 04:20:25.528 | DEBUG    | __main__:before_request:4554 - طلب: GET http://127.0.0.1:5000/api/notifications/unread-count
2025-06-07 04:20:36.266 | DEBUG    | __main__:before_request:4554 - طلب: GET http://127.0.0.1:5000/documents/1/qr-preview
2025-06-07 04:20:36.460 | DEBUG    | __main__:before_request:4554 - طلب: GET http://127.0.0.1:5000/api/notifications/unread-count
2025-06-07 04:21:06.464 | DEBUG    | __main__:before_request:4554 - طلب: GET http://127.0.0.1:5000/api/notifications/unread-count
2025-06-07 04:21:20.264 | DEBUG    | __main__:before_request:4554 - طلب: GET http://127.0.0.1:5000/documents/1/a4-label
2025-06-07 04:21:20.269 | INFO     | __main__:document_a4_label:3743 - إنشاء ملصق A4 للوثيقة 1
2025-06-07 04:21:20.308 | ERROR    | __main__:create_document_a4_label:2351 - خطأ في إنشاء ملصق A4: 'int' object has no attribute 'full_name'
2025-06-07 04:21:20.320 | DEBUG    | __main__:before_request:4554 - طلب: GET http://127.0.0.1:5000/documents/1
2025-06-07 04:21:20.470 | DEBUG    | __main__:before_request:4554 - طلب: GET http://127.0.0.1:5000/api/notifications/unread-count
2025-06-07 04:21:43.633 | DEBUG    | __main__:before_request:4554 - طلب: GET http://127.0.0.1:5000/documents/1/a4-label
2025-06-07 04:21:43.637 | INFO     | __main__:document_a4_label:3743 - إنشاء ملصق A4 للوثيقة 1
2025-06-07 04:21:43.648 | ERROR    | __main__:create_document_a4_label:2351 - خطأ في إنشاء ملصق A4: 'int' object has no attribute 'full_name'
2025-06-07 04:21:43.662 | DEBUG    | __main__:before_request:4554 - طلب: GET http://127.0.0.1:5000/documents/1
2025-06-07 04:21:43.843 | DEBUG    | __main__:before_request:4554 - طلب: GET http://127.0.0.1:5000/api/notifications/unread-count
2025-06-07 04:21:58.941 | INFO     | __main__:init_database:3511 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 04:21:58.941 | INFO     | __main__:init_database:3512 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 04:21:58.945 | INFO     | __main__:init_database:3516 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 04:21:58.968 | INFO     | __main__:init_database:3534 - تم إنشاء سجل النشاط الأولي
2025-06-07 04:21:58.970 | INFO     | __main__:<module>:4573 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 04:22:02.240 | INFO     | __main__:init_database:3511 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 04:22:02.241 | INFO     | __main__:init_database:3512 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 04:22:02.248 | INFO     | __main__:init_database:3516 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 04:22:02.272 | INFO     | __main__:init_database:3534 - تم إنشاء سجل النشاط الأولي
2025-06-07 04:22:02.275 | INFO     | __main__:<module>:4573 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 04:22:14.049 | DEBUG    | __main__:before_request:4554 - طلب: GET http://127.0.0.1:5000/api/notifications/unread-count
2025-06-07 04:22:15.295 | DEBUG    | __main__:before_request:4554 - طلب: GET http://localhost:5000/
2025-06-07 04:22:15.327 | DEBUG    | __main__:before_request:4554 - طلب: GET http://localhost:5000/login
2025-06-07 04:22:18.049 | DEBUG    | __main__:before_request:4554 - طلب: POST http://localhost:5000/login
2025-06-07 04:22:18.413 | INFO     | __main__:login:848 - تم تسجيل دخول المستخدم: admin
2025-06-07 04:22:18.424 | DEBUG    | __main__:before_request:4554 - طلب: GET http://localhost:5000/dashboard
2025-06-07 04:22:18.905 | DEBUG    | __main__:before_request:4554 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:22:21.769 | DEBUG    | __main__:before_request:4554 - طلب: GET http://localhost:5000/documents
2025-06-07 04:22:22.254 | DEBUG    | __main__:before_request:4554 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:22:23.970 | DEBUG    | __main__:before_request:4554 - طلب: GET http://localhost:5000/documents/add
2025-06-07 04:22:24.516 | DEBUG    | __main__:before_request:4554 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:22:42.707 | DEBUG    | __main__:before_request:4554 - طلب: POST http://localhost:5000/documents/add
2025-06-07 04:22:42.729 | INFO     | __main__:add_document:985 - تم حفظ الملف: 20250607_042242_docx
2025-06-07 04:22:42.778 | INFO     | __main__:log_activity:2078 - تم تسجيل النشاط: admin - create - document:1
2025-06-07 04:22:42.781 | INFO     | __main__:add_document:1001 - تم إضافة وثيقة جديدة: ااا (ID: 1) بواسطة admin
2025-06-07 04:22:43.105 | DEBUG    | __main__:before_request:4554 - طلب: GET http://localhost:5000/documents/1
2025-06-07 04:22:43.365 | DEBUG    | __main__:before_request:4554 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:22:56.753 | DEBUG    | __main__:before_request:4554 - طلب: GET http://localhost:5000/documents/1/a4-label
2025-06-07 04:22:56.758 | INFO     | __main__:document_a4_label:3743 - إنشاء ملصق A4 للوثيقة 1
2025-06-07 04:22:56.767 | ERROR    | __main__:create_document_a4_label:2351 - خطأ في إنشاء ملصق A4: 'int' object has no attribute 'full_name'
2025-06-07 04:22:57.085 | DEBUG    | __main__:before_request:4554 - طلب: GET http://localhost:5000/documents/1
2025-06-07 04:22:57.349 | DEBUG    | __main__:before_request:4554 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:23:04.052 | DEBUG    | __main__:before_request:4554 - طلب: GET http://localhost:5000/documents/1/qr
2025-06-07 04:23:27.541 | DEBUG    | __main__:before_request:4554 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:23:57.234 | DEBUG    | __main__:before_request:4554 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:24:28.342 | DEBUG    | __main__:before_request:4554 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:24:58.039 | DEBUG    | __main__:before_request:4554 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:25:28.349 | DEBUG    | __main__:before_request:4554 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:25:58.044 | DEBUG    | __main__:before_request:4554 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:26:26.407 | DEBUG    | __main__:before_request:4554 - طلب: GET http://localhost:5000/documents
2025-06-07 04:26:26.654 | DEBUG    | __main__:before_request:4554 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:26:30.364 | DEBUG    | __main__:before_request:4554 - طلب: GET http://localhost:5000/documents/1
2025-06-07 04:26:30.818 | DEBUG    | __main__:before_request:4554 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:26:41.608 | DEBUG    | __main__:before_request:4554 - طلب: GET http://localhost:5000/documents
2025-06-07 04:26:42.037 | DEBUG    | __main__:before_request:4554 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:26:47.733 | DEBUG    | __main__:before_request:4554 - طلب: GET http://localhost:5000/documents
2025-06-07 04:26:48.151 | DEBUG    | __main__:before_request:4554 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:26:50.222 | DEBUG    | __main__:before_request:4554 - طلب: GET http://localhost:5000/documents/add
2025-06-07 04:26:50.624 | DEBUG    | __main__:before_request:4554 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:27:21.050 | DEBUG    | __main__:before_request:4554 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:27:51.358 | DEBUG    | __main__:before_request:4554 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:28:21.041 | DEBUG    | __main__:before_request:4554 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:28:51.348 | DEBUG    | __main__:before_request:4554 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:29:21.038 | DEBUG    | __main__:before_request:4554 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:29:51.359 | DEBUG    | __main__:before_request:4554 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:30:23.095 | INFO     | __main__:init_database:3551 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 04:30:23.098 | INFO     | __main__:init_database:3552 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 04:30:23.105 | INFO     | __main__:init_database:3556 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 04:30:23.131 | INFO     | __main__:init_database:3574 - تم إنشاء سجل النشاط الأولي
2025-06-07 04:30:23.134 | INFO     | __main__:<module>:4613 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 04:30:23.216 | DEBUG    | __main__:before_request:4594 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:30:23.240 | DEBUG    | __main__:before_request:4594 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 04:30:54.031 | INFO     | __main__:init_database:3631 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 04:30:54.032 | INFO     | __main__:init_database:3632 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 04:30:54.039 | INFO     | __main__:init_database:3636 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 04:30:54.067 | INFO     | __main__:init_database:3654 - تم إنشاء سجل النشاط الأولي
2025-06-07 04:30:54.068 | INFO     | __main__:<module>:4693 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 04:31:11.489 | INFO     | __main__:init_database:3635 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 04:31:11.519 | INFO     | __main__:init_database:3636 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 04:31:11.536 | INFO     | __main__:init_database:3640 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 04:31:11.571 | INFO     | __main__:init_database:3658 - تم إنشاء سجل النشاط الأولي
2025-06-07 04:31:11.579 | INFO     | __main__:<module>:4697 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 04:31:13.356 | DEBUG    | __main__:before_request:4678 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:31:13.626 | DEBUG    | __main__:before_request:4678 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 04:31:28.338 | DEBUG    | __main__:before_request:4678 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:31:28.657 | DEBUG    | __main__:before_request:4678 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 04:31:34.246 | DEBUG    | __main__:before_request:4678 - طلب: GET http://localhost:5000/documents
2025-06-07 04:31:34.571 | DEBUG    | __main__:before_request:4678 - طلب: GET http://localhost:5000/login?next=/documents
2025-06-07 04:31:37.248 | DEBUG    | __main__:before_request:4678 - طلب: POST http://localhost:5000/login?next=/documents
2025-06-07 04:31:37.606 | INFO     | __main__:login:848 - تم تسجيل دخول المستخدم: admin
2025-06-07 04:31:37.931 | DEBUG    | __main__:before_request:4678 - طلب: GET http://localhost:5000/documents
2025-06-07 04:31:38.187 | DEBUG    | __main__:before_request:4678 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:31:43.350 | DEBUG    | __main__:before_request:4678 - طلب: GET http://localhost:5000/documents
2025-06-07 04:31:43.763 | DEBUG    | __main__:before_request:4678 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:31:47.422 | DEBUG    | __main__:before_request:4678 - طلب: GET http://localhost:5000/reports
2025-06-07 04:31:47.882 | DEBUG    | __main__:before_request:4678 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:31:58.856 | INFO     | __main__:init_database:3520 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 04:31:58.857 | INFO     | __main__:init_database:3521 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 04:31:58.865 | INFO     | __main__:init_database:3525 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 04:31:58.901 | INFO     | __main__:init_database:3543 - تم إنشاء سجل النشاط الأولي
2025-06-07 04:31:58.907 | INFO     | __main__:<module>:4582 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 04:32:14.407 | INFO     | __main__:init_database:3520 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 04:32:14.408 | INFO     | __main__:init_database:3521 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 04:32:14.416 | INFO     | __main__:init_database:3525 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 04:32:14.439 | INFO     | __main__:init_database:3543 - تم إنشاء سجل النشاط الأولي
2025-06-07 04:32:14.442 | INFO     | __main__:<module>:4582 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 04:32:17.891 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:32:18.150 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 04:32:32.495 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/notifications
2025-06-07 04:32:32.816 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/login?next=/notifications
2025-06-07 04:32:35.058 | DEBUG    | __main__:before_request:4563 - طلب: POST http://localhost:5000/login?next=/notifications
2025-06-07 04:32:35.426 | INFO     | __main__:login:848 - تم تسجيل دخول المستخدم: admin
2025-06-07 04:32:35.759 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/notifications
2025-06-07 04:32:36.024 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:32:46.464 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/dashboard
2025-06-07 04:32:46.976 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:33:17.040 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:33:33.171 | INFO     | __main__:init_database:3529 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 04:33:33.173 | INFO     | __main__:init_database:3530 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 04:33:33.180 | INFO     | __main__:init_database:3534 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 04:33:33.205 | INFO     | __main__:init_database:3552 - تم إنشاء سجل النشاط الأولي
2025-06-07 04:33:33.207 | INFO     | __main__:<module>:4591 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 04:33:47.363 | DEBUG    | __main__:before_request:4572 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:33:47.629 | DEBUG    | __main__:before_request:4572 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 04:34:17.037 | DEBUG    | __main__:before_request:4572 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:34:17.348 | DEBUG    | __main__:before_request:4572 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 04:34:47.043 | DEBUG    | __main__:before_request:4572 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:34:47.356 | DEBUG    | __main__:before_request:4572 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 04:35:17.041 | DEBUG    | __main__:before_request:4572 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:35:17.361 | DEBUG    | __main__:before_request:4572 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 04:35:47.281 | DEBUG    | __main__:before_request:4572 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:35:47.620 | DEBUG    | __main__:before_request:4572 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 04:36:17.040 | DEBUG    | __main__:before_request:4572 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:36:17.362 | DEBUG    | __main__:before_request:4572 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 04:36:44.257 | INFO     | __main__:init_database:3569 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 04:36:44.265 | INFO     | __main__:init_database:3570 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 04:36:44.276 | INFO     | __main__:init_database:3574 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 04:36:44.301 | INFO     | __main__:init_database:3592 - تم إنشاء سجل النشاط الأولي
2025-06-07 04:36:44.311 | INFO     | __main__:<module>:4631 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 04:37:02.865 | INFO     | __main__:init_database:3569 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 04:37:02.866 | INFO     | __main__:init_database:3570 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 04:37:02.870 | INFO     | __main__:init_database:3574 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 04:37:02.890 | INFO     | __main__:init_database:3592 - تم إنشاء سجل النشاط الأولي
2025-06-07 04:37:02.890 | INFO     | __main__:<module>:4631 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 04:37:13.356 | DEBUG    | __main__:before_request:4612 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:37:13.632 | DEBUG    | __main__:before_request:4612 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 04:37:46.380 | INFO     | __main__:init_database:3569 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 04:37:46.381 | INFO     | __main__:init_database:3570 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 04:37:46.385 | INFO     | __main__:init_database:3574 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 04:37:46.402 | INFO     | __main__:init_database:3592 - تم إنشاء سجل النشاط الأولي
2025-06-07 04:37:46.403 | INFO     | __main__:<module>:4631 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 04:37:49.628 | INFO     | __main__:init_database:3569 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 04:37:49.630 | INFO     | __main__:init_database:3570 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 04:37:49.635 | INFO     | __main__:init_database:3574 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 04:37:49.658 | INFO     | __main__:init_database:3592 - تم إنشاء سجل النشاط الأولي
2025-06-07 04:37:49.659 | INFO     | __main__:<module>:4631 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 04:38:13.040 | DEBUG    | __main__:before_request:4612 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:38:13.365 | DEBUG    | __main__:before_request:4612 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 04:38:23.311 | INFO     | __main__:init_database:3569 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 04:38:23.312 | INFO     | __main__:init_database:3570 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 04:38:23.319 | INFO     | __main__:init_database:3574 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 04:38:23.351 | INFO     | __main__:init_database:3592 - تم إنشاء سجل النشاط الأولي
2025-06-07 04:38:23.355 | INFO     | __main__:<module>:4631 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 04:38:26.439 | INFO     | __main__:init_database:3569 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 04:38:26.440 | INFO     | __main__:init_database:3570 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 04:38:26.448 | INFO     | __main__:init_database:3574 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 04:38:26.475 | INFO     | __main__:init_database:3592 - تم إنشاء سجل النشاط الأولي
2025-06-07 04:38:26.477 | INFO     | __main__:<module>:4631 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 04:38:39.696 | DEBUG    | __main__:before_request:4612 - طلب: GET http://localhost:5000/
2025-06-07 04:38:40.017 | DEBUG    | __main__:before_request:4612 - طلب: GET http://localhost:5000/login
2025-06-07 04:39:00.062 | DEBUG    | __main__:before_request:4612 - طلب: GET http://localhost:5000/
2025-06-07 04:39:00.379 | DEBUG    | __main__:before_request:4612 - طلب: GET http://localhost:5000/login
2025-06-07 04:39:05.619 | DEBUG    | __main__:before_request:4612 - طلب: POST http://localhost:5000/login
2025-06-07 04:39:05.987 | INFO     | __main__:login:848 - تم تسجيل دخول المستخدم: admin
2025-06-07 04:39:06.318 | DEBUG    | __main__:before_request:4612 - طلب: GET http://localhost:5000/dashboard
2025-06-07 04:39:06.576 | DEBUG    | __main__:before_request:4612 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:39:12.293 | DEBUG    | __main__:before_request:4612 - طلب: GET http://localhost:5000/documents
2025-06-07 04:39:12.656 | DEBUG    | __main__:before_request:4612 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:39:14.749 | DEBUG    | __main__:before_request:4612 - طلب: GET http://localhost:5000/documents/add
2025-06-07 04:39:15.010 | DEBUG    | __main__:before_request:4612 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:39:44.941 | DEBUG    | __main__:before_request:4612 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:40:15.354 | DEBUG    | __main__:before_request:4612 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:40:44.932 | DEBUG    | __main__:before_request:4612 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:41:15.343 | DEBUG    | __main__:before_request:4612 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:41:45.039 | DEBUG    | __main__:before_request:4612 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:42:15.355 | DEBUG    | __main__:before_request:4612 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:42:45.838 | INFO     | __main__:init_database:3568 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 04:42:45.840 | INFO     | __main__:init_database:3569 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 04:42:45.849 | INFO     | __main__:init_database:3573 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 04:42:45.877 | INFO     | __main__:init_database:3591 - تم إنشاء سجل النشاط الأولي
2025-06-07 04:42:45.881 | INFO     | __main__:<module>:4630 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 04:42:45.956 | DEBUG    | __main__:before_request:4611 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:42:45.983 | DEBUG    | __main__:before_request:4611 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 04:43:07.491 | INFO     | __main__:init_database:3577 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 04:43:07.494 | INFO     | __main__:init_database:3578 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 04:43:07.501 | INFO     | __main__:init_database:3582 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 04:43:07.596 | INFO     | __main__:init_database:3600 - تم إنشاء سجل النشاط الأولي
2025-06-07 04:43:07.597 | INFO     | __main__:<module>:4639 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 04:43:27.294 | INFO     | __main__:init_database:3580 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 04:43:27.297 | INFO     | __main__:init_database:3581 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 04:43:27.304 | INFO     | __main__:init_database:3585 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 04:43:27.328 | INFO     | __main__:init_database:3603 - تم إنشاء سجل النشاط الأولي
2025-06-07 04:43:27.332 | INFO     | __main__:<module>:4642 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 04:43:46.353 | DEBUG    | __main__:before_request:4623 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:43:46.622 | DEBUG    | __main__:before_request:4623 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 04:44:15.714 | INFO     | __main__:init_database:3582 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 04:44:15.716 | INFO     | __main__:init_database:3583 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 04:44:15.723 | INFO     | __main__:init_database:3587 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 04:44:15.747 | INFO     | __main__:init_database:3605 - تم إنشاء سجل النشاط الأولي
2025-06-07 04:44:15.750 | INFO     | __main__:<module>:4644 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 04:44:47.359 | DEBUG    | __main__:before_request:4625 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:44:47.628 | DEBUG    | __main__:before_request:4625 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 04:45:12.788 | INFO     | __main__:init_database:3587 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 04:45:12.788 | INFO     | __main__:init_database:3588 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 04:45:12.793 | INFO     | __main__:init_database:3592 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 04:45:12.815 | INFO     | __main__:init_database:3610 - تم إنشاء سجل النشاط الأولي
2025-06-07 04:45:12.816 | INFO     | __main__:<module>:4649 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 04:45:45.353 | INFO     | __main__:init_database:3587 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 04:45:45.355 | INFO     | __main__:init_database:3588 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 04:45:45.362 | INFO     | __main__:init_database:3592 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 04:45:45.384 | INFO     | __main__:init_database:3610 - تم إنشاء سجل النشاط الأولي
2025-06-07 04:45:45.386 | INFO     | __main__:<module>:4649 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 04:45:48.357 | DEBUG    | __main__:before_request:4630 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:45:48.627 | DEBUG    | __main__:before_request:4630 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 04:47:05.603 | INFO     | __main__:init_database:3587 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 04:47:05.604 | INFO     | __main__:init_database:3588 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 04:47:05.608 | INFO     | __main__:init_database:3592 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 04:47:05.628 | INFO     | __main__:init_database:3610 - تم إنشاء سجل النشاط الأولي
2025-06-07 04:47:05.628 | INFO     | __main__:<module>:4649 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 04:47:08.843 | INFO     | __main__:init_database:3587 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 04:47:08.844 | INFO     | __main__:init_database:3588 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 04:47:08.850 | INFO     | __main__:init_database:3592 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 04:47:08.878 | INFO     | __main__:init_database:3610 - تم إنشاء سجل النشاط الأولي
2025-06-07 04:47:08.879 | INFO     | __main__:<module>:4649 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 04:47:39.966 | DEBUG    | __main__:before_request:4630 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:47:40.234 | DEBUG    | __main__:before_request:4630 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 04:48:01.634 | INFO     | __main__:init_database:3587 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 04:48:01.635 | INFO     | __main__:init_database:3588 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 04:48:01.641 | INFO     | __main__:init_database:3592 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 04:48:01.661 | INFO     | __main__:init_database:3610 - تم إنشاء سجل النشاط الأولي
2025-06-07 04:48:01.662 | INFO     | __main__:<module>:4649 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 04:48:04.994 | INFO     | __main__:init_database:3587 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 04:48:04.995 | INFO     | __main__:init_database:3588 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 04:48:05.002 | INFO     | __main__:init_database:3592 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 04:48:05.027 | INFO     | __main__:init_database:3610 - تم إنشاء سجل النشاط الأولي
2025-06-07 04:48:05.028 | INFO     | __main__:<module>:4649 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 04:48:16.678 | DEBUG    | __main__:before_request:4630 - طلب: GET http://localhost:5000/
2025-06-07 04:48:17.014 | DEBUG    | __main__:before_request:4630 - طلب: GET http://localhost:5000/login
2025-06-07 04:48:21.175 | DEBUG    | __main__:before_request:4630 - طلب: POST http://localhost:5000/login
2025-06-07 04:48:21.542 | INFO     | __main__:login:848 - تم تسجيل دخول المستخدم: admin
2025-06-07 04:48:21.862 | DEBUG    | __main__:before_request:4630 - طلب: GET http://localhost:5000/dashboard
2025-06-07 04:48:22.128 | DEBUG    | __main__:before_request:4630 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:48:25.769 | DEBUG    | __main__:before_request:4630 - طلب: GET http://localhost:5000/documents
2025-06-07 04:48:26.422 | DEBUG    | __main__:before_request:4630 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:48:28.085 | DEBUG    | __main__:before_request:4630 - طلب: GET http://localhost:5000/documents/add
2025-06-07 04:48:28.681 | DEBUG    | __main__:before_request:4630 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:48:58.337 | DEBUG    | __main__:before_request:4630 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:49:26.098 | DEBUG    | __main__:before_request:4630 - طلب: GET http://localhost:5000/documents/add
2025-06-07 04:49:26.362 | DEBUG    | __main__:before_request:4630 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:49:57.039 | DEBUG    | __main__:before_request:4630 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:50:27.359 | DEBUG    | __main__:before_request:4630 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:50:57.240 | INFO     | __main__:init_database:3587 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 04:50:57.241 | INFO     | __main__:init_database:3588 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 04:50:57.249 | INFO     | __main__:init_database:3592 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 04:50:57.368 | INFO     | __main__:init_database:3610 - تم إنشاء سجل النشاط الأولي
2025-06-07 04:50:57.375 | INFO     | __main__:<module>:4649 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 04:51:16.478 | DEBUG    | __main__:before_request:4630 - طلب: GET http://localhost:5000/documents
2025-06-07 04:51:16.739 | DEBUG    | __main__:before_request:4630 - طلب: GET http://localhost:5000/login?next=/documents
2025-06-07 04:51:31.260 | DEBUG    | __main__:before_request:4630 - طلب: GET http://localhost:5000/documents/add
2025-06-07 04:51:31.579 | DEBUG    | __main__:before_request:4630 - طلب: GET http://localhost:5000/login?next=/documents/add
2025-06-07 04:51:33.009 | DEBUG    | __main__:before_request:4630 - طلب: GET http://localhost:5000/documents
2025-06-07 04:51:33.328 | DEBUG    | __main__:before_request:4630 - طلب: GET http://localhost:5000/login?next=/documents
2025-06-07 04:51:35.689 | DEBUG    | __main__:before_request:4630 - طلب: POST http://localhost:5000/login?next=/documents
2025-06-07 04:51:36.044 | INFO     | __main__:login:848 - تم تسجيل دخول المستخدم: admin
2025-06-07 04:51:36.378 | DEBUG    | __main__:before_request:4630 - طلب: GET http://localhost:5000/documents
2025-06-07 04:51:36.805 | DEBUG    | __main__:before_request:4630 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:52:07.358 | DEBUG    | __main__:before_request:4630 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:52:37.046 | DEBUG    | __main__:before_request:4630 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:53:22.081 | INFO     | __main__:init_database:3583 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 04:53:22.082 | INFO     | __main__:init_database:3584 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 04:53:22.089 | INFO     | __main__:init_database:3588 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 04:53:22.110 | INFO     | __main__:init_database:3606 - تم إنشاء سجل النشاط الأولي
2025-06-07 04:53:22.111 | INFO     | __main__:<module>:4645 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 04:53:37.358 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:53:37.619 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 04:53:59.946 | INFO     | __main__:init_database:3583 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 04:53:59.946 | INFO     | __main__:init_database:3584 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 04:53:59.952 | INFO     | __main__:init_database:3588 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 04:53:59.974 | INFO     | __main__:init_database:3606 - تم إنشاء سجل النشاط الأولي
2025-06-07 04:53:59.976 | INFO     | __main__:<module>:4645 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 04:54:07.043 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:54:07.357 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 04:54:13.092 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/
2025-06-07 04:54:13.481 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/login
2025-06-07 04:54:17.137 | DEBUG    | __main__:before_request:4626 - طلب: POST http://localhost:5000/login
2025-06-07 04:54:17.500 | INFO     | __main__:login:848 - تم تسجيل دخول المستخدم: admin
2025-06-07 04:54:17.827 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/dashboard
2025-06-07 04:54:18.120 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:54:21.493 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/documents
2025-06-07 04:54:21.748 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:54:24.561 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/documents/add
2025-06-07 04:54:24.820 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:54:37.364 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:54:54.811 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:55:07.347 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:55:24.812 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:55:33.712 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/documents/add
2025-06-07 04:55:33.718 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/login?next=/documents/add
2025-06-07 04:55:55.347 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:56:08.038 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:56:13.781 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/documents
2025-06-07 04:56:14.047 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:56:15.696 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/documents/add
2025-06-07 04:56:16.205 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:56:45.817 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:57:09.359 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:57:16.050 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:57:46.356 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:58:10.046 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:58:16.378 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:58:46.037 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:59:11.347 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:59:16.041 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 04:59:46.350 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 05:00:12.042 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 05:00:47.349 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 05:01:13.045 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 05:01:16.359 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 05:01:46.363 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 05:02:13.098 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 05:02:47.360 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 05:03:13.052 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 05:03:48.352 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 05:04:13.043 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 05:04:49.356 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 05:05:13.041 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 05:05:50.353 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 05:06:13.038 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 05:06:51.361 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 05:07:13.046 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 05:07:26.642 | INFO     | __main__:init_database:3554 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 05:07:26.643 | INFO     | __main__:init_database:3555 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 05:07:26.647 | INFO     | __main__:init_database:3559 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 05:07:26.668 | INFO     | __main__:init_database:3577 - تم إنشاء سجل النشاط الأولي
2025-06-07 05:07:26.669 | INFO     | __main__:<module>:4616 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 05:07:52.354 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 05:08:13.044 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 05:08:31.180 | INFO     | __main__:init_database:3554 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 05:08:31.189 | INFO     | __main__:init_database:3555 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 05:08:31.218 | INFO     | __main__:init_database:3559 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 05:08:31.376 | INFO     | __main__:init_database:3577 - تم إنشاء سجل النشاط الأولي
2025-06-07 05:08:31.435 | INFO     | __main__:<module>:4616 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 05:08:47.652 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/
2025-06-07 05:08:47.917 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/dashboard
2025-06-07 05:08:48.425 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 05:08:53.356 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 05:08:54.977 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/documents
2025-06-07 05:08:55.513 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 05:08:56.933 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/documents/add
2025-06-07 05:08:57.393 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 05:09:13.041 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 05:09:27.408 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 05:09:40.914 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/
2025-06-07 05:09:40.917 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/login
2025-06-07 05:09:52.831 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/incoming
2025-06-07 05:09:53.307 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 05:09:54.044 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 05:09:55.281 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/incoming/add
2025-06-07 05:09:55.542 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 05:10:00.804 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/documents
2025-06-07 05:10:01.227 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 05:10:02.393 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/documents/add
2025-06-07 05:10:02.794 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 05:10:13.066 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 05:10:33.355 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 05:10:48.208 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/documents/add
2025-06-07 05:10:48.673 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 05:10:55.054 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 05:11:13.380 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 05:11:18.371 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 05:11:48.737 | INFO     | __main__:init_database:3557 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 05:11:48.738 | INFO     | __main__:init_database:3558 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 05:11:48.743 | INFO     | __main__:init_database:3562 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 05:11:48.743 | INFO     | __main__:<module>:4602 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 05:11:49.361 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 05:11:56.057 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 05:12:13.351 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 05:12:19.050 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 05:12:25.848 | INFO     | __main__:init_database:3557 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 05:12:25.848 | INFO     | __main__:init_database:3558 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 05:12:25.853 | INFO     | __main__:init_database:3562 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 05:12:25.855 | INFO     | __main__:<module>:4602 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 05:12:48.434 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/documents/add
2025-06-07 05:12:48.441 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/login?next=/documents/add
2025-06-07 05:12:49.464 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 05:12:57.047 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 05:13:01.227 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/
2025-06-07 05:13:01.494 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/dashboard
2025-06-07 05:13:01.909 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 05:13:07.424 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/documents
2025-06-07 05:13:07.687 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 05:13:09.305 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/documents/add
2025-06-07 05:13:09.767 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 05:13:13.045 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 05:13:40.344 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 05:14:10.051 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 05:14:13.353 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 05:14:39.471 | DEBUG    | __main__:before_request:4626 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 11:46:35.369 | INFO     | __main__:init_database:3537 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 11:46:35.372 | INFO     | __main__:init_database:3538 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 11:46:35.376 | INFO     | __main__:init_database:3542 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 11:46:35.377 | INFO     | __main__:<module>:4582 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 11:47:08.807 | INFO     | __main__:init_database:3537 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 11:47:08.807 | INFO     | __main__:init_database:3538 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 11:47:08.812 | INFO     | __main__:init_database:3542 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 11:47:08.813 | INFO     | __main__:<module>:4582 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 11:47:26.851 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/documents/add
2025-06-07 11:47:26.859 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/
2025-06-07 11:47:27.077 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/login
2025-06-07 11:47:27.185 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/login?next=/documents/add
2025-06-07 11:47:27.929 | WARNING  | __main__:not_found_error:4534 - صفحة غير موجودة: http://localhost:5000/favicon.ico
2025-06-07 11:47:30.724 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/login?next=/reports/generate
2025-06-07 11:47:34.200 | DEBUG    | __main__:before_request:4563 - طلب: POST http://localhost:5000/login
2025-06-07 11:47:37.017 | DEBUG    | __main__:before_request:4563 - طلب: POST http://localhost:5000/login
2025-06-07 11:47:37.548 | INFO     | __main__:login:857 - تم تسجيل دخول المستخدم: admin
2025-06-07 11:47:37.877 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/dashboard
2025-06-07 11:47:38.174 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 11:47:42.900 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/documents
2025-06-07 11:47:43.180 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 11:47:45.326 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/documents/add
2025-06-07 11:47:45.376 | ERROR    | __main__:internal_error:4541 - خطأ داخلي في الخادم: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-06-07 11:47:45.610 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 11:48:06.497 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/documents/add
2025-06-07 11:48:06.545 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/login?next=/documents/add
2025-06-07 11:48:10.682 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/documents/add
2025-06-07 11:48:10.713 | ERROR    | __main__:internal_error:4541 - خطأ داخلي في الخادم: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-06-07 11:48:10.929 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 11:48:13.180 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/dashboard
2025-06-07 11:48:13.635 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 11:48:21.750 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/documents/add
2025-06-07 11:48:21.761 | ERROR    | __main__:internal_error:4541 - خطأ داخلي في الخادم: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-06-07 11:48:22.264 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 11:48:52.259 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 11:49:22.338 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 11:49:52.642 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 11:50:22.351 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 11:50:52.647 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 11:51:22.340 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 11:51:22.985 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 11:51:23.772 | INFO     | __main__:init_database:3537 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 11:51:23.772 | INFO     | __main__:init_database:3538 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 11:51:23.777 | INFO     | __main__:init_database:3542 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 11:51:23.778 | INFO     | __main__:<module>:4582 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 11:51:52.334 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 11:52:25.763 | INFO     | __main__:init_database:3538 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 11:52:25.767 | INFO     | __main__:init_database:3539 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 11:52:25.774 | INFO     | __main__:init_database:3543 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 11:52:25.775 | INFO     | __main__:<module>:4583 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 11:52:53.655 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 11:53:02.644 | INFO     | __main__:init_database:3538 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 11:53:02.644 | INFO     | __main__:init_database:3539 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 11:53:02.649 | INFO     | __main__:init_database:3543 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 11:53:02.651 | INFO     | __main__:<module>:4583 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 11:53:14.938 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/
2025-06-07 11:53:15.278 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/dashboard
2025-06-07 11:53:15.988 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 11:53:18.649 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/documents
2025-06-07 11:53:18.914 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 11:53:20.468 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/documents/add
2025-06-07 11:53:20.487 | ERROR    | __main__:internal_error:4541 - خطأ داخلي في الخادم: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-06-07 11:53:21.025 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 11:53:51.437 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 11:53:54.657 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 11:53:58.843 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/dashboard
2025-06-07 11:53:59.405 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 11:54:01.516 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/documents
2025-06-07 11:54:02.078 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 11:54:04.204 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/documents/add
2025-06-07 11:54:04.213 | ERROR    | __main__:internal_error:4541 - خطأ داخلي في الخادم: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-06-07 11:54:04.736 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 11:54:35.332 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 11:54:55.659 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 11:55:05.343 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 11:55:35.695 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 11:55:53.054 | INFO     | __main__:init_database:3545 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 11:55:53.054 | INFO     | __main__:init_database:3546 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 11:55:53.060 | INFO     | __main__:init_database:3550 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 11:55:53.061 | INFO     | __main__:<module>:4590 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 11:55:56.735 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 11:56:05.649 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 11:56:32.903 | INFO     | __main__:init_database:3545 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 11:56:32.903 | INFO     | __main__:init_database:3546 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 11:56:32.908 | INFO     | __main__:init_database:3550 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 11:56:32.908 | INFO     | __main__:<module>:4590 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 11:56:35.348 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 11:56:46.326 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/
2025-06-07 11:56:46.590 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/dashboard
2025-06-07 11:56:47.248 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 11:56:50.333 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/documents
2025-06-07 11:56:50.627 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 11:56:52.458 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/documents/add
2025-06-07 11:56:52.482 | ERROR    | __main__:internal_error:4541 - خطأ داخلي في الخادم: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-06-07 11:56:52.718 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 11:56:57.652 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 11:57:05.343 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 11:57:23.031 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 11:57:28.654 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/documents/add
2025-06-07 11:57:28.674 | ERROR    | __main__:internal_error:4541 - خطأ داخلي في الخادم: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-06-07 11:57:29.208 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 11:57:58.352 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 11:57:59.649 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 11:58:19.249 | INFO     | __main__:init_database:3535 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 11:58:19.250 | INFO     | __main__:init_database:3536 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 11:58:19.255 | INFO     | __main__:init_database:3540 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 11:58:19.256 | INFO     | __main__:<module>:4580 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 11:58:29.343 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 11:58:58.445 | INFO     | __main__:init_database:3535 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 11:58:58.446 | INFO     | __main__:init_database:3536 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 11:58:58.451 | INFO     | __main__:init_database:3540 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 11:58:58.452 | INFO     | __main__:<module>:4580 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 11:58:59.647 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 11:58:59.910 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 11:59:13.065 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/
2025-06-07 11:59:13.397 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/dashboard
2025-06-07 11:59:14.270 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 11:59:16.647 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/documents
2025-06-07 11:59:17.015 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 11:59:18.740 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/documents/add
2025-06-07 11:59:18.781 | ERROR    | __main__:internal_error:4541 - خطأ داخلي في الخادم: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-06-07 11:59:19.046 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 11:59:29.655 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 11:59:49.354 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 11:59:59.659 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 12:00:00.353 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 12:00:19.653 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 12:00:29.343 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 12:00:34.526 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/documents
2025-06-07 12:00:34.792 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 12:00:36.790 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/documents/add
2025-06-07 12:00:36.802 | ERROR    | __main__:internal_error:4541 - خطأ داخلي في الخادم: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-06-07 12:00:37.294 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 12:00:39.060 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/dashboard
2025-06-07 12:00:39.563 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 12:00:59.341 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 12:01:01.660 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 12:01:09.263 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 12:01:13.811 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/outgoing/add
2025-06-07 12:01:14.337 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 12:01:17.388 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/outgoing
2025-06-07 12:01:17.983 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 12:01:20.763 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/outgoing/add
2025-06-07 12:01:21.127 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 12:01:25.464 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/outgoing
2025-06-07 12:01:25.758 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 12:01:56.643 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 12:02:00.350 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 12:02:02.644 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 12:02:26.348 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 12:02:56.647 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 12:03:01.333 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 12:03:04.298 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 12:03:23.441 | INFO     | __main__:init_database:3535 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 12:03:23.442 | INFO     | __main__:init_database:3536 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 12:03:23.449 | INFO     | __main__:init_database:3540 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 12:03:23.450 | INFO     | __main__:<module>:4580 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 12:03:26.507 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 12:03:26.600 | INFO     | __main__:init_database:3535 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 12:03:26.601 | INFO     | __main__:init_database:3536 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 12:03:26.607 | INFO     | __main__:init_database:3540 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 12:03:26.607 | INFO     | __main__:<module>:4580 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 12:03:26.851 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 12:03:27.137 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/dashboard
2025-06-07 12:03:37.482 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/documents/add
2025-06-07 12:03:37.503 | ERROR    | __main__:internal_error:4541 - خطأ داخلي في الخادم: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-06-07 12:03:40.407 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 12:03:56.649 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 12:04:02.345 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 12:04:04.644 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 12:04:10.108 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 12:04:27.017 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 12:04:31.611 | INFO     | __main__:init_database:3539 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 12:04:31.828 | INFO     | __main__:init_database:3540 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 12:04:32.083 | INFO     | __main__:init_database:3544 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 12:04:32.203 | INFO     | __main__:<module>:4584 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 12:04:40.112 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 12:04:56.659 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 12:05:03.344 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 12:05:05.657 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 12:05:10.148 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 12:05:15.138 | INFO     | __main__:init_database:3546 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 12:05:15.140 | INFO     | __main__:init_database:3547 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 12:05:15.145 | INFO     | __main__:init_database:3551 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 12:05:15.145 | INFO     | __main__:<module>:4591 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 12:05:40.656 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 12:05:49.445 | INFO     | __main__:init_database:3546 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 12:05:49.445 | INFO     | __main__:init_database:3547 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 12:05:49.450 | INFO     | __main__:init_database:3551 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 12:05:49.452 | INFO     | __main__:<module>:4591 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 12:05:53.530 | INFO     | __main__:init_database:3546 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 12:05:53.538 | INFO     | __main__:init_database:3547 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 12:05:53.547 | INFO     | __main__:init_database:3551 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 12:05:53.548 | INFO     | __main__:<module>:4591 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 12:05:57.342 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 12:06:04.648 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 12:06:06.342 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 12:06:10.658 | DEBUG    | __main__:before_request:4563 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 12:06:40.659 | DEBUG    | __main__:before_request:4572 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 12:06:40.921 | DEBUG    | __main__:before_request:4572 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 12:06:49.924 | INFO     | __main__:init_database:3546 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 12:06:49.925 | INFO     | __main__:init_database:3547 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 12:06:49.934 | INFO     | __main__:init_database:3551 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 12:06:49.934 | INFO     | __main__:<module>:4591 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 12:06:53.930 | INFO     | __main__:init_database:3546 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 12:06:53.931 | INFO     | __main__:init_database:3547 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 12:06:53.936 | INFO     | __main__:init_database:3551 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 12:06:53.937 | INFO     | __main__:<module>:4591 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 12:06:58.646 | DEBUG    | __main__:before_request:4572 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 12:06:58.912 | DEBUG    | __main__:before_request:4572 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 12:07:05.351 | DEBUG    | __main__:before_request:4572 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 12:07:05.677 | DEBUG    | __main__:before_request:4572 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 12:07:07.343 | DEBUG    | __main__:before_request:4572 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 12:07:07.666 | DEBUG    | __main__:before_request:4572 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 12:07:10.051 | DEBUG    | __main__:before_request:4572 - طلب: GET http://localhost:5000/
2025-06-07 12:07:10.392 | DEBUG    | __main__:before_request:4572 - طلب: GET http://localhost:5000/login
2025-06-07 12:07:10.658 | DEBUG    | __main__:before_request:4572 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 12:07:10.666 | DEBUG    | __main__:before_request:4572 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 12:07:27.913 | DEBUG    | __main__:before_request:4572 - طلب: POST http://localhost:5000/login
2025-06-07 12:07:28.465 | INFO     | __main__:login:861 - تم تسجيل دخول المستخدم: admin
2025-06-07 12:07:28.478 | DEBUG    | __main__:before_request:4572 - طلب: GET http://localhost:5000/dashboard
2025-06-07 12:07:29.176 | DEBUG    | __main__:before_request:4572 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 12:07:30.813 | DEBUG    | __main__:before_request:4572 - طلب: GET http://localhost:5000/documents
2025-06-07 12:07:31.408 | DEBUG    | __main__:before_request:4572 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 12:07:32.586 | DEBUG    | __main__:before_request:4572 - طلب: GET http://localhost:5000/documents/add
2025-06-07 12:07:33.145 | DEBUG    | __main__:before_request:4572 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 12:07:59.348 | DEBUG    | __main__:before_request:4572 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 12:08:03.158 | DEBUG    | __main__:before_request:4572 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 12:08:06.335 | DEBUG    | __main__:before_request:4572 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 12:08:08.655 | DEBUG    | __main__:before_request:4572 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 12:08:09.369 | DEBUG    | __main__:before_request:4572 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 12:08:33.149 | DEBUG    | __main__:before_request:4572 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 12:09:35.332 | INFO     | __main__:init_database:3546 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 12:09:35.333 | INFO     | __main__:init_database:3547 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 12:09:35.337 | INFO     | __main__:init_database:3551 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 12:09:35.338 | INFO     | __main__:<module>:4591 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 12:09:52.651 | DEBUG    | __main__:before_request:4572 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 12:09:52.917 | DEBUG    | __main__:before_request:4572 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 12:45:07.736 | INFO     | __main__:init_database:3554 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 12:45:07.737 | INFO     | __main__:init_database:3555 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 12:45:07.741 | INFO     | __main__:init_database:3559 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 12:45:07.742 | INFO     | __main__:<module>:4599 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 12:45:09.651 | DEBUG    | __main__:before_request:4580 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 12:45:09.914 | DEBUG    | __main__:before_request:4580 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 12:46:09.354 | DEBUG    | __main__:before_request:4580 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 12:46:09.675 | DEBUG    | __main__:before_request:4580 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 12:46:25.593 | DEBUG    | __main__:before_request:4580 - طلب: GET http://localhost:5000/
2025-06-07 12:46:25.915 | DEBUG    | __main__:before_request:4580 - طلب: GET http://localhost:5000/login
2025-06-07 12:46:28.662 | DEBUG    | __main__:before_request:4580 - طلب: POST http://localhost:5000/login
2025-06-07 12:46:29.084 | INFO     | __main__:login:861 - تم تسجيل دخول المستخدم: admin
2025-06-07 12:46:29.406 | DEBUG    | __main__:before_request:4580 - طلب: GET http://localhost:5000/dashboard
2025-06-07 12:46:30.015 | DEBUG    | __main__:before_request:4580 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 12:46:31.890 | DEBUG    | __main__:before_request:4580 - طلب: GET http://localhost:5000/documents
2025-06-07 12:46:32.197 | DEBUG    | __main__:before_request:4580 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 12:46:33.849 | DEBUG    | __main__:before_request:4580 - طلب: GET http://localhost:5000/documents/add
2025-06-07 12:46:34.110 | DEBUG    | __main__:before_request:4580 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 12:47:04.053 | DEBUG    | __main__:before_request:4580 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 12:47:09.672 | DEBUG    | __main__:before_request:4580 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 12:56:29.582 | INFO     | __main__:init_database:3547 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 12:56:29.583 | INFO     | __main__:init_database:3548 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 12:56:29.587 | INFO     | __main__:init_database:3552 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 12:56:29.588 | INFO     | __main__:<module>:4592 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 12:56:43.827 | DEBUG    | __main__:before_request:4573 - طلب: GET http://localhost:5000/
2025-06-07 12:56:44.076 | DEBUG    | __main__:before_request:4573 - طلب: GET http://localhost:5000/login
2025-06-07 12:57:09.343 | DEBUG    | __main__:before_request:4573 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 12:57:09.651 | DEBUG    | __main__:before_request:4573 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 12:57:09.658 | DEBUG    | __main__:before_request:4573 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 12:57:09.914 | DEBUG    | __main__:before_request:4573 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 12:58:09.336 | DEBUG    | __main__:before_request:4573 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 12:58:09.646 | DEBUG    | __main__:before_request:4573 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 12:58:09.653 | DEBUG    | __main__:before_request:4573 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 12:58:09.909 | DEBUG    | __main__:before_request:4573 - طلب: GET http://localhost:5000/login?next=/api/notifications/unread-count
2025-06-07 12:58:20.269 | DEBUG    | __main__:before_request:4573 - طلب: POST http://localhost:5000/login
2025-06-07 12:58:20.642 | INFO     | __main__:login:857 - تم تسجيل دخول المستخدم: admin
2025-06-07 12:58:20.654 | DEBUG    | __main__:before_request:4573 - طلب: GET http://localhost:5000/dashboard
2025-06-07 12:58:21.396 | DEBUG    | __main__:before_request:4573 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 12:58:22.421 | DEBUG    | __main__:before_request:4573 - طلب: GET http://localhost:5000/documents
2025-06-07 12:58:23.001 | DEBUG    | __main__:before_request:4573 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 12:58:23.839 | DEBUG    | __main__:before_request:4573 - طلب: GET http://localhost:5000/documents/add
2025-06-07 12:58:24.381 | DEBUG    | __main__:before_request:4573 - طلب: GET http://localhost:5000/api/notifications/unread-count
2025-06-07 12:53:16.164 | INFO     | __main__:init_database:3578 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 12:53:16.165 | INFO     | __main__:init_database:3579 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 12:53:16.169 | INFO     | __main__:init_database:3583 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 12:53:16.170 | INFO     | __main__:<module>:4679 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 12:53:45.535 | INFO     | __main__:login:857 - تم تسجيل دخول المستخدم: admin
2025-06-07 12:55:38.756 | INFO     | app:login:857 - تم تسجيل دخول المستخدم: admin
2025-06-07 13:03:19.299 | INFO     | __main__:init_database:3619 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 13:03:19.300 | INFO     | __main__:init_database:3620 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 13:03:19.308 | INFO     | __main__:init_database:3624 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 13:03:19.309 | INFO     | __main__:<module>:4713 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 13:03:47.267 | INFO     | __main__:login:867 - تم تسجيل دخول المستخدم: admin
2025-06-07 13:04:50.607 | INFO     | __main__:init_database:3619 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 13:04:50.607 | INFO     | __main__:init_database:3620 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 13:04:50.615 | INFO     | __main__:init_database:3624 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 13:04:50.616 | INFO     | __main__:<module>:4713 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 13:05:44.342 | INFO     | __main__:login:867 - تم تسجيل دخول المستخدم: admin
2025-06-07 13:05:50.589 | ERROR    | __main__:internal_error:4673 - خطأ داخلي: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-06-07 13:09:41.987 | ERROR    | __main__:internal_error:4673 - خطأ داخلي: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-06-07 13:11:05.711 | INFO     | __main__:ocr_upload:4297 - تم حفظ الملف مؤقتاً: C:\Users\<USER>\OneDrive\Desktop\M\uploads\temp\20250607_131105_Screenshot_2025-06-07_114836.png
2025-06-07 13:11:05.712 | INFO     | __main__:ocr_upload:4308 - معالجة صورة: .png
2025-06-07 13:11:05.778 | INFO     | __main__:extract_text_from_image:3058 - بدء معالجة الصورة: C:\Users\<USER>\OneDrive\Desktop\M\uploads\temp\20250607_131105_Screenshot_2025-06-07_114836.png
2025-06-07 13:11:06.635 | DEBUG    | __main__:extract_text_from_image:3110 - فشل في عربي + إنجليزي - فقرات: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 13:11:06.690 | DEBUG    | __main__:extract_text_from_image:3110 - فشل في عربي + إنجليزي - عمود واحد: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 13:11:06.741 | DEBUG    | __main__:extract_text_from_image:3110 - فشل في عربي + إنجليزي - صفحة كاملة: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 13:11:06.786 | DEBUG    | __main__:extract_text_from_image:3110 - فشل في إنجليزي - فقرات: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 13:11:06.836 | DEBUG    | __main__:extract_text_from_image:3110 - فشل في إنجليزي - كلمة واحدة: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 13:11:06.883 | DEBUG    | __main__:extract_text_from_image:3110 - فشل في إنجليزي - سطر واحد: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 13:11:06.933 | DEBUG    | __main__:extract_text_from_image:3110 - فشل في عربي - فقرات: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 13:11:06.988 | DEBUG    | __main__:extract_text_from_image:3110 - فشل في عربي - عمود واحد: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 13:11:07.030 | DEBUG    | __main__:extract_text_from_image:3110 - فشل في نص متناثر: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 13:11:07.065 | DEBUG    | __main__:extract_text_from_image:3110 - فشل في نص متناثر مع OSD: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 13:11:07.108 | DEBUG    | __main__:extract_text_from_image:3110 - فشل في عربي + إنجليزي - فقرات: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 13:11:07.161 | DEBUG    | __main__:extract_text_from_image:3110 - فشل في عربي + إنجليزي - عمود واحد: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 13:11:07.205 | DEBUG    | __main__:extract_text_from_image:3110 - فشل في عربي + إنجليزي - صفحة كاملة: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 13:11:07.251 | DEBUG    | __main__:extract_text_from_image:3110 - فشل في إنجليزي - فقرات: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 13:11:07.301 | DEBUG    | __main__:extract_text_from_image:3110 - فشل في إنجليزي - كلمة واحدة: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 13:11:07.345 | DEBUG    | __main__:extract_text_from_image:3110 - فشل في إنجليزي - سطر واحد: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 13:11:07.389 | DEBUG    | __main__:extract_text_from_image:3110 - فشل في عربي - فقرات: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 13:11:07.439 | DEBUG    | __main__:extract_text_from_image:3110 - فشل في عربي - عمود واحد: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 13:11:07.491 | DEBUG    | __main__:extract_text_from_image:3110 - فشل في نص متناثر: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 13:11:07.537 | DEBUG    | __main__:extract_text_from_image:3110 - فشل في نص متناثر مع OSD: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 13:11:07.578 | DEBUG    | __main__:extract_text_from_image:3110 - فشل في عربي + إنجليزي - فقرات: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 13:11:07.641 | DEBUG    | __main__:extract_text_from_image:3110 - فشل في عربي + إنجليزي - عمود واحد: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 13:11:07.688 | DEBUG    | __main__:extract_text_from_image:3110 - فشل في عربي + إنجليزي - صفحة كاملة: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 13:11:07.727 | DEBUG    | __main__:extract_text_from_image:3110 - فشل في إنجليزي - فقرات: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 13:11:07.768 | DEBUG    | __main__:extract_text_from_image:3110 - فشل في إنجليزي - كلمة واحدة: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 13:11:07.805 | DEBUG    | __main__:extract_text_from_image:3110 - فشل في إنجليزي - سطر واحد: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 13:11:07.853 | DEBUG    | __main__:extract_text_from_image:3110 - فشل في عربي - فقرات: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 13:11:07.890 | DEBUG    | __main__:extract_text_from_image:3110 - فشل في عربي - عمود واحد: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 13:11:07.923 | DEBUG    | __main__:extract_text_from_image:3110 - فشل في نص متناثر: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 13:11:07.966 | DEBUG    | __main__:extract_text_from_image:3110 - فشل في نص متناثر مع OSD: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 13:11:08.013 | ERROR    | __main__:extract_text_from_image:3128 - فشل في التكوين البسيط: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-06-07 13:11:08.014 | WARNING  | __main__:extract_text_from_image:3130 - لم يتم العثور على نص في الصورة مع جميع التكوينات
2025-06-07 13:11:08.016 | INFO     | __main__:ocr_upload:4325 - تم حذف الملف المؤقت: C:\Users\<USER>\OneDrive\Desktop\M\uploads\temp\20250607_131105_Screenshot_2025-06-07_114836.png
2025-06-07 13:11:08.017 | WARNING  | __main__:ocr_upload:4352 - لم يتم العثور على نص في الملف
2025-06-07 13:16:20.136 | INFO     | __main__:init_database:3633 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 13:16:20.137 | INFO     | __main__:init_database:3634 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 13:16:20.144 | INFO     | __main__:init_database:3638 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 13:16:20.145 | INFO     | __main__:<module>:4727 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 13:17:15.868 | ERROR    | __main__:internal_error:4673 - خطأ داخلي: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-06-07 13:18:07.340 | INFO     | __main__:init_database:3633 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 13:18:07.340 | INFO     | __main__:init_database:3634 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 13:18:07.348 | INFO     | __main__:init_database:3638 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 13:18:07.355 | INFO     | __main__:<module>:4727 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 13:21:09.228 | INFO     | __main__:init_database:3633 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 13:21:09.229 | INFO     | __main__:init_database:3634 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 13:21:09.235 | INFO     | __main__:init_database:3638 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 13:21:09.235 | INFO     | __main__:<module>:4727 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 13:21:31.877 | INFO     | __main__:login:867 - تم تسجيل دخول المستخدم: admin
2025-06-07 13:28:58.153 | INFO     | __main__:init_database:3791 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 13:28:58.155 | INFO     | __main__:init_database:3792 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 13:28:58.163 | INFO     | __main__:init_database:3796 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 13:28:58.164 | INFO     | __main__:<module>:4885 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 13:29:49.505 | INFO     | __main__:login:873 - تم تسجيل دخول المستخدم: admin
2025-06-07 13:31:00.979 | INFO     | __main__:init_database:3791 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 13:31:00.981 | INFO     | __main__:init_database:3792 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 13:31:00.989 | INFO     | __main__:init_database:3796 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 13:31:00.989 | INFO     | __main__:<module>:4885 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 13:31:45.169 | INFO     | __main__:init_database:3791 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 13:31:45.172 | INFO     | __main__:init_database:3792 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 13:31:45.187 | INFO     | __main__:init_database:3796 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 13:31:45.193 | INFO     | __main__:<module>:4885 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 13:32:10.959 | INFO     | __main__:login:873 - تم تسجيل دخول المستخدم: admin
2025-06-07 13:39:11.971 | INFO     | __main__:init_database:3838 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 13:39:11.972 | INFO     | __main__:init_database:3839 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 13:39:11.978 | INFO     | __main__:init_database:3843 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 13:39:11.979 | INFO     | __main__:<module>:4932 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 13:40:41.495 | INFO     | __main__:init_database:3838 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 13:40:41.496 | INFO     | __main__:init_database:3839 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 13:40:41.501 | INFO     | __main__:init_database:3843 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 13:40:41.502 | INFO     | __main__:<module>:4932 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 13:40:49.052 | INFO     | __main__:login:873 - تم تسجيل دخول المستخدم: admin
2025-06-07 13:41:12.728 | INFO     | __main__:init_database:3838 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 13:41:12.729 | INFO     | __main__:init_database:3839 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 13:41:12.733 | INFO     | __main__:init_database:3843 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 13:41:12.734 | INFO     | __main__:<module>:4932 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 13:47:02.914 | INFO     | __main__:init_database:3834 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 13:47:02.915 | INFO     | __main__:init_database:3835 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 13:47:02.922 | INFO     | __main__:init_database:3839 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 13:47:02.924 | INFO     | __main__:<module>:4928 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 13:53:05.069 | INFO     | __main__:init_database:3834 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 13:53:05.070 | INFO     | __main__:init_database:3835 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 13:53:05.078 | INFO     | __main__:init_database:3839 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 13:53:05.079 | INFO     | __main__:<module>:4928 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 13:53:27.717 | INFO     | __main__:login:873 - تم تسجيل دخول المستخدم: admin
2025-06-07 13:54:15.815 | INFO     | __main__:init_database:3834 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 13:54:15.816 | INFO     | __main__:init_database:3835 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 13:54:15.823 | INFO     | __main__:init_database:3839 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 13:54:15.824 | INFO     | __main__:<module>:4928 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 13:55:03.455 | INFO     | __main__:init_database:3834 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 13:55:03.456 | INFO     | __main__:init_database:3835 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 13:55:03.464 | INFO     | __main__:init_database:3839 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 13:55:03.464 | INFO     | __main__:<module>:4928 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 13:55:21.920 | INFO     | __main__:login:873 - تم تسجيل دخول المستخدم: admin
2025-06-07 14:01:39.835 | INFO     | __main__:init_database:3806 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 14:01:39.836 | INFO     | __main__:init_database:3807 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 14:01:39.841 | INFO     | __main__:init_database:3811 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 14:01:39.843 | INFO     | __main__:<module>:4900 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 14:02:05.478 | INFO     | __main__:login:873 - تم تسجيل دخول المستخدم: admin
2025-06-07 14:33:05.760 | INFO     | __main__:init_database:3806 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 14:33:05.761 | INFO     | __main__:init_database:3807 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 14:33:05.765 | INFO     | __main__:init_database:3811 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 14:33:05.767 | INFO     | __main__:<module>:4900 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 14:03:27.925 | INFO     | __main__:login:873 - تم تسجيل دخول المستخدم: admin
2025-06-07 14:04:35.966 | INFO     | __main__:login:873 - تم تسجيل دخول المستخدم: admin
2025-06-07 14:16:33.113 | INFO     | __main__:init_database:3809 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 14:16:33.114 | INFO     | __main__:init_database:3810 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 14:16:33.119 | INFO     | __main__:init_database:3814 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 14:16:33.123 | INFO     | __main__:<module>:4903 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 14:17:02.948 | INFO     | __main__:login:873 - تم تسجيل دخول المستخدم: admin
2025-06-07 14:18:52.558 | INFO     | __main__:login:873 - تم تسجيل دخول المستخدم: admin
2025-06-07 14:20:14.081 | INFO     | __main__:init_database:3809 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 14:20:14.082 | INFO     | __main__:init_database:3810 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 14:20:14.087 | INFO     | __main__:init_database:3814 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 14:20:14.088 | INFO     | __main__:<module>:4903 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 14:20:37.893 | INFO     | __main__:login:873 - تم تسجيل دخول المستخدم: admin
2025-06-07 14:20:54.584 | INFO     | __main__:init_database:3809 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 14:20:54.585 | INFO     | __main__:init_database:3810 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 14:20:54.589 | INFO     | __main__:init_database:3814 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 14:20:54.590 | INFO     | __main__:<module>:4903 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 14:22:47.511 | INFO     | __main__:login:873 - تم تسجيل دخول المستخدم: admin
2025-06-07 14:24:39.548 | INFO     | __main__:init_database:3834 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 14:24:39.551 | INFO     | __main__:init_database:3835 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 14:24:39.556 | INFO     | __main__:init_database:3839 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 14:24:39.556 | INFO     | __main__:<module>:4928 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 14:25:00.551 | INFO     | __main__:login:873 - تم تسجيل دخول المستخدم: admin
2025-06-07 14:25:58.384 | INFO     | __main__:init_database:3834 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 14:25:58.384 | INFO     | __main__:init_database:3835 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 14:25:58.390 | INFO     | __main__:init_database:3839 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 14:25:58.390 | INFO     | __main__:<module>:4928 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 14:26:09.286 | INFO     | __main__:login:873 - تم تسجيل دخول المستخدم: admin
2025-06-07 14:29:41.866 | INFO     | __main__:init_database:3834 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 14:29:41.867 | INFO     | __main__:init_database:3835 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 14:29:41.876 | INFO     | __main__:init_database:3839 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 14:29:41.878 | INFO     | __main__:<module>:4928 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 14:30:11.063 | INFO     | __main__:init_database:3834 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 14:30:11.064 | INFO     | __main__:init_database:3835 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 14:30:11.069 | INFO     | __main__:init_database:3839 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 14:30:11.069 | INFO     | __main__:<module>:4928 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 14:30:35.852 | INFO     | __main__:login:873 - تم تسجيل دخول المستخدم: admin
2025-06-07 14:31:56.252 | INFO     | __main__:init_database:3834 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 14:31:56.253 | INFO     | __main__:init_database:3835 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 14:31:56.257 | INFO     | __main__:init_database:3839 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 14:31:56.258 | INFO     | __main__:<module>:4928 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 14:35:39.694 | INFO     | __main__:init_database:3834 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 14:35:39.695 | INFO     | __main__:init_database:3835 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 14:35:39.699 | INFO     | __main__:init_database:3839 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 14:35:39.700 | INFO     | __main__:<module>:4928 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 14:36:58.840 | INFO     | __main__:init_database:3834 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 14:36:58.841 | INFO     | __main__:init_database:3835 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 14:36:58.848 | INFO     | __main__:init_database:3839 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 14:36:58.849 | INFO     | __main__:<module>:4928 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 14:38:28.253 | INFO     | __main__:init_database:3834 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 14:38:28.254 | INFO     | __main__:init_database:3835 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 14:38:28.261 | INFO     | __main__:init_database:3839 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 14:38:28.262 | INFO     | __main__:<module>:4928 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 14:38:37.774 | INFO     | __main__:login:873 - تم تسجيل دخول المستخدم: admin
2025-06-07 14:42:05.289 | INFO     | __main__:init_database:3834 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 14:42:05.291 | INFO     | __main__:init_database:3835 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 14:42:05.298 | INFO     | __main__:init_database:3839 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 14:42:05.298 | INFO     | __main__:<module>:4928 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 14:43:04.464 | INFO     | __main__:init_database:3834 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 14:43:04.465 | INFO     | __main__:init_database:3835 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 14:43:04.469 | INFO     | __main__:init_database:3839 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 14:43:04.470 | INFO     | __main__:<module>:4928 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 14:43:31.601 | INFO     | __main__:init_database:3834 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 14:43:31.602 | INFO     | __main__:init_database:3835 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 14:43:31.607 | INFO     | __main__:init_database:3839 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 14:43:31.608 | INFO     | __main__:<module>:4928 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 14:55:29.939 | INFO     | __main__:init_database:3891 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 14:55:29.940 | INFO     | __main__:init_database:3892 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 14:55:29.946 | INFO     | __main__:init_database:3896 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 14:55:29.947 | INFO     | __main__:<module>:4985 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 14:58:13.048 | INFO     | __main__:init_database:3891 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 14:58:13.050 | INFO     | __main__:init_database:3892 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 14:58:13.055 | INFO     | __main__:init_database:3896 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 14:58:13.056 | INFO     | __main__:<module>:4985 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 14:59:25.024 | INFO     | __main__:init_database:3891 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 14:59:25.025 | INFO     | __main__:init_database:3892 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 14:59:25.029 | INFO     | __main__:init_database:3896 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 14:59:25.030 | INFO     | __main__:<module>:4985 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 14:59:43.965 | INFO     | __main__:login:873 - تم تسجيل دخول المستخدم: admin
2025-06-07 15:00:22.277 | INFO     | __main__:add_incoming:1273 - تم إضافة كتاب وارد جديد: IN-2025-0001
2025-06-07 15:01:13.286 | INFO     | __main__:add_outgoing:1441 - تم إضافة كتاب صادر جديد: OUT-2025-0001
2025-06-07 15:06:11.906 | ERROR    | __main__:generate_report:1690 - خطأ في توليد التقرير: 'builtin_function_or_method' object is not iterable
2025-06-07 15:06:24.697 | ERROR    | __main__:generate_report:1690 - خطأ في توليد التقرير: 'builtin_function_or_method' object is not iterable
2025-06-07 15:06:41.782 | ERROR    | __main__:generate_report:1690 - خطأ في توليد التقرير: 'builtin_function_or_method' object is not iterable
2025-06-07 15:07:04.798 | ERROR    | __main__:generate_report:1690 - خطأ في توليد التقرير: 'dict object' has no attribute 'generated_at'
2025-06-07 15:07:59.822 | INFO     | __main__:login:873 - تم تسجيل دخول المستخدم: admin
2025-06-07 15:46:15.821 | INFO     | __main__:init_database:3891 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 15:46:15.821 | INFO     | __main__:init_database:3892 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 15:46:15.826 | INFO     | __main__:init_database:3896 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 15:46:15.826 | INFO     | __main__:<module>:4985 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 15:47:04.411 | INFO     | __main__:init_database:3891 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 15:47:04.411 | INFO     | __main__:init_database:3892 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 15:47:04.419 | INFO     | __main__:init_database:3896 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 15:47:04.420 | INFO     | __main__:<module>:4985 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 15:51:56.799 | INFO     | __main__:init_database:3900 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 15:51:56.799 | INFO     | __main__:init_database:3901 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 15:51:56.805 | INFO     | __main__:init_database:3905 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 15:51:56.806 | INFO     | __main__:<module>:4994 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 15:54:17.526 | INFO     | __main__:init_database:3900 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 15:54:17.526 | INFO     | __main__:init_database:3901 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 15:54:17.531 | INFO     | __main__:init_database:3905 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 15:54:17.532 | INFO     | __main__:<module>:4994 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 15:54:50.715 | INFO     | __main__:init_database:3900 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 15:54:50.716 | INFO     | __main__:init_database:3901 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 15:54:50.723 | INFO     | __main__:init_database:3905 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 15:54:50.724 | INFO     | __main__:<module>:4994 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 15:26:18.538 | INFO     | __main__:login:873 - تم تسجيل دخول المستخدم: admin
2025-06-07 15:58:00.748 | INFO     | __main__:init_database:3900 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 15:58:00.749 | INFO     | __main__:init_database:3901 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 15:58:00.754 | INFO     | __main__:init_database:3905 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 15:58:00.754 | INFO     | __main__:<module>:4994 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 15:58:43.418 | INFO     | __main__:init_database:3900 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 15:58:43.420 | INFO     | __main__:init_database:3901 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 15:58:43.424 | INFO     | __main__:init_database:3905 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 15:58:43.425 | INFO     | __main__:<module>:4994 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 15:35:15.986 | INFO     | __main__:login:873 - تم تسجيل دخول المستخدم: admin
2025-06-07 16:22:27.347 | INFO     | __main__:init_database:3903 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 16:22:27.347 | INFO     | __main__:init_database:3904 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 16:22:27.352 | INFO     | __main__:init_database:3908 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 16:22:27.353 | INFO     | __main__:<module>:4997 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 16:22:56.744 | INFO     | __main__:init_database:3903 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 16:22:56.745 | INFO     | __main__:init_database:3904 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 16:22:56.751 | INFO     | __main__:init_database:3908 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 16:22:56.752 | INFO     | __main__:<module>:4997 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-07 16:25:05.961 | INFO     | __main__:init_database:3903 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-07 16:25:05.962 | INFO     | __main__:init_database:3904 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-07 16:25:05.967 | INFO     | __main__:init_database:3908 - إجمالي أنواع الوثائق في النظام: 24
2025-06-07 16:25:05.967 | INFO     | __main__:<module>:4997 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-09 12:44:58.582 | INFO     | __main__:init_database:6592 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-09 12:44:58.584 | INFO     | __main__:init_database:6593 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-09 12:44:58.590 | INFO     | __main__:init_database:6597 - إجمالي أنواع الوثائق في النظام: 24
2025-06-09 12:44:58.590 | INFO     | __main__:<module>:7651 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-09 12:45:30.765 | INFO     | __main__:login:875 - تم تسجيل دخول المستخدم: admin
2025-06-09 12:45:31.017 | INFO     | __main__:dashboard:1003 - تم تحميل لوحة التحكم المحسنة للمستخدم: admin في 0.211 ثانية
2025-06-09 12:45:31.633 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 12:46:00.088 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 12:46:30.037 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 12:46:30.438 | ERROR    | __main__:notifications:1541 - خطأ في تحميل الإشعارات: notifications/index.html
2025-06-09 12:46:30.475 | ERROR    | __main__:internal_error:7611 - خطأ داخلي: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-06-09 12:46:30.655 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 12:46:39.683 | ERROR    | __main__:notifications:1541 - خطأ في تحميل الإشعارات: notifications/index.html
2025-06-09 12:46:39.703 | ERROR    | __main__:internal_error:7611 - خطأ داخلي: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-06-09 12:46:39.886 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 12:46:47.476 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 12:46:52.703 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 12:47:22.709 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 12:47:36.605 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 12:47:42.192 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 12:47:46.179 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 12:47:57.674 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 12:48:14.001 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 12:48:38.948 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 12:48:44.146 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 12:48:49.814 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 12:49:02.462 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 12:49:06.557 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 12:49:22.278 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 12:49:32.536 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 12:49:38.210 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 12:49:52.346 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 12:50:01.278 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 12:50:25.929 | ERROR    | __main__:notifications:1541 - خطأ في تحميل الإشعارات: notifications/index.html
2025-06-09 12:50:25.938 | ERROR    | __main__:internal_error:7611 - خطأ داخلي: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-06-09 12:50:26.081 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 12:50:56.082 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 12:51:26.082 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 12:51:56.084 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 12:52:26.085 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 12:52:56.080 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 12:53:26.088 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 12:53:56.085 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 12:54:26.090 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 12:54:56.078 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 12:55:26.092 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 12:55:56.078 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 12:56:26.078 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 12:56:56.080 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 12:57:26.082 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 12:57:56.083 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 12:58:26.083 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 12:58:56.084 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 12:59:26.087 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 12:59:56.077 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:00:26.089 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:00:56.078 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:01:26.077 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:01:56.077 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:02:26.078 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:02:56.078 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:03:26.079 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:03:56.082 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:04:26.083 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:04:56.083 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:05:26.081 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:05:56.079 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:06:26.077 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:06:28.605 | ERROR    | __main__:notifications:1541 - خطأ في تحميل الإشعارات: notifications/index.html
2025-06-09 13:06:28.618 | ERROR    | __main__:internal_error:7611 - خطأ داخلي: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-06-09 13:06:28.770 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:06:41.446 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:07:11.451 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:07:42.115 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:07:42.245 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:08:13.122 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:08:42.260 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:08:42.623 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:09:12.636 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:09:40.575 | ERROR    | __main__:notifications:1541 - خطأ في تحميل الإشعارات: notifications/index.html
2025-06-09 13:09:40.586 | ERROR    | __main__:internal_error:7611 - خطأ داخلي: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-06-09 13:09:40.877 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:10:10.881 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:10:18.280 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:10:48.283 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:11:18.299 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:11:18.447 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:11:48.449 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:12:14.085 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:12:20.967 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:12:24.122 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:12:35.994 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:12:45.628 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:12:48.097 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:13:04.383 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:13:07.369 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:13:09.755 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:13:16.062 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:13:29.879 | ERROR    | __main__:generate_report:4272 - خطأ في توليد التقرير: 'builtin_function_or_method' object is not iterable
2025-06-09 13:13:29.989 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:13:59.991 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:14:20.983 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:14:50.998 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:15:21.070 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:15:51.116 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:16:21.120 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:16:51.118 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:17:21.113 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:17:51.118 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:18:31.161 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:19:31.119 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:20:31.124 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:21:31.124 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:22:31.124 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:23:31.112 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:24:31.118 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:25:31.121 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:26:31.125 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:27:31.116 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:28:31.113 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:29:31.114 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:30:31.119 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:31:31.116 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:32:31.123 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:33:31.114 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:34:31.124 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:35:31.113 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:35:50.175 | INFO     | __main__:init_database:6764 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-09 13:35:50.175 | INFO     | __main__:init_database:6765 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-09 13:35:50.181 | INFO     | __main__:init_database:6769 - إجمالي أنواع الوثائق في النظام: 24
2025-06-09 13:35:50.182 | INFO     | __main__:<module>:8543 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-09 13:36:31.116 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:37:31.118 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:37:40.240 | INFO     | __main__:login:875 - تم تسجيل دخول المستخدم: admin
2025-06-09 13:37:40.281 | INFO     | __main__:dashboard:1003 - تم تحميل لوحة التحكم المحسنة للمستخدم: admin في 0.024 ثانية
2025-06-09 13:37:40.843 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:37:58.172 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:38:21.829 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:38:28.786 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:38:31.119 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:38:35.993 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:39:05.487 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:39:10.472 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:39:12.543 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:39:16.422 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:39:21.752 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:39:29.902 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:39:31.293 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:39:33.623 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:39:40.512 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:39:44.097 | INFO     | __main__:dashboard:1003 - تم تحميل لوحة التحكم المحسنة للمستخدم: admin في 0.014 ثانية
2025-06-09 13:39:44.554 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:40:04.411 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:40:31.113 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:40:34.114 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:40:44.799 | INFO     | __main__:add_document_logic:3501 - ✅ تم حفظ الملف: 1749465644_report_incoming_20250607.pdf
2025-06-09 13:40:44.870 | INFO     | __main__:add_document_logic:3590 - ✅ تم إضافة وثيقة جديدة مع الحفظ التلقائي: بغداد
2025-06-09 13:40:45.205 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:41:08.259 | INFO     | __main__:document_a4_label:6799 - إنشاء ملصق A4 للوثيقة 1
2025-06-09 13:41:08.260 | ERROR    | __main__:create_document_a4_label:5329 - خطأ في إنشاء ملصق HTML: 'int' object has no attribute 'full_name'
2025-06-09 13:41:08.663 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:41:22.064 | INFO     | __main__:document_a4_label:6799 - إنشاء ملصق A4 للوثيقة 1
2025-06-09 13:41:22.065 | ERROR    | __main__:create_document_a4_label:5329 - خطأ في إنشاء ملصق HTML: 'int' object has no attribute 'full_name'
2025-06-09 13:41:22.467 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:41:31.133 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:41:31.898 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:41:34.758 | INFO     | __main__:document_a4_label:6799 - إنشاء ملصق A4 للوثيقة 1
2025-06-09 13:41:34.759 | ERROR    | __main__:create_document_a4_label:5329 - خطأ في إنشاء ملصق HTML: 'int' object has no attribute 'full_name'
2025-06-09 13:41:35.122 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:42:06.435 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:42:31.123 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:42:36.120 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:43:00.644 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:43:24.229 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:43:31.114 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:43:54.126 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:44:06.338 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:44:14.728 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:44:31.115 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:44:44.423 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:45:15.429 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:45:31.126 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:45:35.098 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:46:01.371 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:46:07.135 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:46:31.117 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:46:37.006 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:46:53.178 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:46:57.686 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:47:10.721 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:47:18.271 | INFO     | __main__:edit_document:3724 - تم تعديل الوثيقة: بغداد بواسطة admin
2025-06-09 13:47:18.862 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:47:27.596 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:47:29.897 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:47:31.120 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:47:53.896 | INFO     | __main__:add_incoming:3830 - تم إضافة كتاب وارد جديد: IN-2025-0001
2025-06-09 13:47:54.482 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:47:58.047 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:47:59.912 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:48:28.340 | INFO     | __main__:add_outgoing:3998 - تم إضافة كتاب صادر جديد: OUT-2025-0001
2025-06-09 13:48:28.921 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:48:31.130 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:48:32.967 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:48:42.922 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:48:55.526 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:49:05.234 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:49:07.246 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:49:13.062 | INFO     | __main__:add_document_logic:3590 - ✅ تم إضافة وثيقة جديدة مع الحفظ التلقائي: بغداد
2025-06-09 13:49:13.642 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:49:16.593 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:49:21.473 | INFO     | __main__:delete_document:3755 - تم حذف الوثيقة: بغداد بواسطة admin
2025-06-09 13:49:22.055 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:49:25.196 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:49:31.124 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:49:54.892 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:49:56.398 | INFO     | __main__:add_document_logic:3590 - ✅ تم إضافة وثيقة جديدة مع الحفظ التلقائي: دائرة المنظمات
2025-06-09 13:49:56.837 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:50:06.358 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:50:22.278 | INFO     | __main__:sign_document:7413 - تم توقيع معتمد للوثيقة 3 بواسطة admin
2025-06-09 13:50:24.885 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:50:31.121 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:50:37.691 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:51:03.222 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:51:17.586 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:51:23.394 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:51:31.116 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:51:42.894 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:51:53.365 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:52:18.218 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:52:31.123 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:52:41.161 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:52:47.887 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:52:49.943 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:53:12.695 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:53:20.706 | INFO     | __main__:view_signatures:7437 - عرض توقيعات الوثيقة 3 - عدد التوقيعات: 1
2025-06-09 13:53:21.386 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:53:27.535 | INFO     | __main__:verify_signature:7482 - بدء التحقق من التوقيع 1
2025-06-09 13:53:27.576 | WARNING  | __main__:verify_signature:7500 - التوقيع 1 غير صحيح
2025-06-09 13:53:27.887 | INFO     | __main__:view_signatures:7437 - عرض توقيعات الوثيقة 3 - عدد التوقيعات: 1
2025-06-09 13:53:28.151 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:53:31.117 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:53:58.321 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:54:28.116 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:54:31.120 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:54:58.424 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:55:21.630 | INFO     | __main__:verify_signature:7482 - بدء التحقق من التوقيع 1
2025-06-09 13:55:21.648 | WARNING  | __main__:verify_signature:7500 - التوقيع 1 غير صحيح
2025-06-09 13:55:21.965 | INFO     | __main__:view_signatures:7437 - عرض توقيعات الوثيقة 3 - عدد التوقيعات: 1
2025-06-09 13:55:22.277 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:55:31.116 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:55:37.438 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:55:43.469 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:56:00.668 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:56:05.086 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:56:16.504 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:56:21.752 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:56:26.515 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:56:31.120 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:56:57.330 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:56:57.651 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:57:10.641 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:57:31.113 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:57:41.123 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:57:48.388 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:58:19.122 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:58:31.120 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:58:49.512 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:59:19.120 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:59:31.114 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 13:59:49.429 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:00:19.109 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:00:31.111 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:00:49.482 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:01:19.123 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:01:31.115 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:02:06.334 | ERROR    | app:create_document_a4_label:5774 - خطأ في إنشاء ملصق HTML: 'NoneType' object has no attribute 'strftime'
2025-06-09 14:02:20.423 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:02:26.178 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:02:31.114 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:02:55.872 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:03:21.423 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:03:25.866 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:03:31.116 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:03:52.280 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:03:56.038 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:03:56.112 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:04:19.423 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:04:21.109 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:04:26.118 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:04:49.422 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:04:51.118 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:04:56.117 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:05:26.425 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:05:31.121 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:05:31.123 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:05:56.426 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:06:31.113 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:06:31.113 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:06:31.436 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:07:31.116 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:07:31.116 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:07:31.437 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:08:31.124 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:08:31.125 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:08:31.446 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:09:31.114 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:09:31.116 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:09:31.423 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:10:31.120 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:10:31.425 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:10:31.776 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:11:04.228 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:11:14.760 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:11:31.825 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:11:32.121 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:11:32.373 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:11:34.120 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:11:45.426 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:12:04.120 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:12:15.423 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:12:31.124 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:12:31.127 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:12:31.433 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:12:34.125 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:12:45.438 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:13:04.109 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:13:15.428 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:13:31.115 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:13:31.115 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:13:31.423 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:13:34.110 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:13:45.431 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:14:04.110 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:14:15.429 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:14:31.113 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:14:31.114 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:14:31.424 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:14:34.116 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:14:45.427 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:15:31.124 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:15:31.435 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:15:31.701 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:15:31.746 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:15:32.013 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:16:31.113 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:16:31.116 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:16:31.423 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:16:31.689 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:16:31.738 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:17:31.111 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:17:31.115 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:17:31.424 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:17:31.693 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:17:31.740 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:18:31.116 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:18:31.116 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:18:31.437 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:18:31.703 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:18:31.749 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:19:31.113 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:19:31.117 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:19:31.424 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:19:31.740 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:19:31.994 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:20:31.125 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:20:31.128 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:20:31.433 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:20:31.699 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:20:31.749 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:21:31.122 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:21:31.123 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:21:31.444 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:21:31.695 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:21:31.758 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:22:31.115 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:22:31.120 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:22:31.426 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:22:31.692 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:22:31.738 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:23:31.116 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:23:31.117 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:23:31.438 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:23:31.689 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:23:31.752 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:24:31.116 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:24:31.120 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:24:31.437 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:24:31.704 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:24:31.751 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:25:31.130 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:25:31.132 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:25:31.447 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:25:31.714 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:25:31.763 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:26:31.126 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:26:31.126 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:26:31.446 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:26:31.713 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:26:31.760 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:27:31.125 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:27:31.125 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:27:31.434 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:27:31.701 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:27:31.748 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:28:31.114 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:28:31.115 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:28:31.435 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:28:31.673 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:28:31.753 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:29:31.123 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:29:31.124 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:29:31.435 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:29:31.703 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:29:31.750 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:30:31.118 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:30:31.120 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:30:31.441 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:30:31.691 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:30:31.754 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:31:31.118 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:31:31.122 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:31:31.440 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:31:31.691 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:31:31.754 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:32:31.121 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:32:31.121 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:32:31.441 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:32:31.690 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:32:31.752 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:33:31.123 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:33:31.127 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:33:31.434 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:33:31.700 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:33:31.747 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:34:31.125 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:34:31.127 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:34:31.436 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:34:31.698 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:34:31.745 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:35:31.124 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:35:31.125 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:35:31.433 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:35:31.702 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:35:31.750 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:36:31.126 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:36:31.126 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:36:31.450 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:36:31.782 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-09 14:36:31.919 | ERROR    | __main__:api_unread_notifications_count:3254 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-27 19:38:38.649 | ERROR    | __main__:log_security_event:268 - خطأ في تسجيل الحدث الأمني: 'NoneType' object has no attribute 'is_authenticated'
2025-06-27 19:38:38.989 | ERROR    | __main__:log_security_event:268 - خطأ في تسجيل الحدث الأمني: 'NoneType' object has no attribute 'is_authenticated'
2025-06-27 19:38:39.063 | INFO     | __main__:init_database:7673 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-27 19:38:39.064 | INFO     | __main__:init_database:7674 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-27 19:38:39.070 | INFO     | __main__:init_database:7678 - إجمالي أنواع الوثائق في النظام: 24
2025-06-27 19:38:39.071 | INFO     | __main__:<module>:9499 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-27 19:41:06.452 | ERROR    | __main__:log_security_event:268 - خطأ في تسجيل الحدث الأمني: 'NoneType' object has no attribute 'is_authenticated'
2025-06-27 19:41:06.803 | ERROR    | __main__:log_security_event:268 - خطأ في تسجيل الحدث الأمني: 'NoneType' object has no attribute 'is_authenticated'
2025-06-27 19:41:06.864 | INFO     | __main__:init_database:7673 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-27 19:41:06.865 | INFO     | __main__:init_database:7674 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-27 19:41:06.871 | INFO     | __main__:init_database:7678 - إجمالي أنواع الوثائق في النظام: 24
2025-06-27 19:41:06.871 | INFO     | __main__:<module>:9499 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-27 19:41:29.929 | WARNING  | __main__:log_security_event:263 - SECURITY EVENT: FAILED_LOGIN_ATTEMPT - Failed login for user admin - User: None - IP: 127.0.0.1
2025-06-27 19:41:29.930 | WARNING  | __main__:log_security_event:263 - SECURITY EVENT: FAILED_LOGIN - Failed login attempt for username: admin - User: None - IP: 127.0.0.1
2025-06-27 19:41:40.206 | WARNING  | __main__:log_security_event:263 - SECURITY EVENT: FAILED_LOGIN_ATTEMPT - Failed login for user admin - User: None - IP: 127.0.0.1
2025-06-27 19:41:40.207 | WARNING  | __main__:log_security_event:263 - SECURITY EVENT: FAILED_LOGIN - Failed login attempt for username: admin - User: None - IP: 127.0.0.1
2025-06-27 19:42:10.084 | WARNING  | __main__:log_security_event:263 - SECURITY EVENT: FAILED_LOGIN_ATTEMPT - Failed login for user admin - User: None - IP: 127.0.0.1
2025-06-27 19:42:10.086 | WARNING  | __main__:log_security_event:263 - SECURITY EVENT: FAILED_LOGIN - Failed login attempt for username: admin - User: None - IP: 127.0.0.1
2025-06-27 19:42:28.672 | WARNING  | __main__:log_security_event:263 - SECURITY EVENT: FAILED_LOGIN_ATTEMPT - Failed login for user admin - User: None - IP: 127.0.0.1
2025-06-27 19:42:28.673 | WARNING  | __main__:log_security_event:263 - SECURITY EVENT: FAILED_LOGIN - Failed login attempt for username: admin - User: None - IP: 127.0.0.1
2025-06-27 19:43:32.351 | ERROR    | __main__:log_security_event:268 - خطأ في تسجيل الحدث الأمني: 'NoneType' object has no attribute 'is_authenticated'
2025-06-27 19:43:32.709 | ERROR    | __main__:log_security_event:268 - خطأ في تسجيل الحدث الأمني: 'NoneType' object has no attribute 'is_authenticated'
2025-06-27 19:43:32.794 | INFO     | __main__:init_database:7673 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-27 19:43:32.795 | INFO     | __main__:init_database:7674 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-27 19:43:32.799 | INFO     | __main__:init_database:7678 - إجمالي أنواع الوثائق في النظام: 24
2025-06-27 19:43:32.801 | INFO     | __main__:<module>:9499 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-27 19:43:42.806 | WARNING  | __main__:log_security_event:263 - SECURITY EVENT: FAILED_LOGIN_ATTEMPT - Failed login for user admin - User: None - IP: 127.0.0.1
2025-06-27 19:43:42.812 | WARNING  | __main__:log_security_event:263 - SECURITY EVENT: FAILED_LOGIN - Failed login attempt for username: admin - User: None - IP: 127.0.0.1
2025-06-27 19:43:51.018 | WARNING  | __main__:log_security_event:263 - SECURITY EVENT: FAILED_LOGIN_ATTEMPT - Failed login for user admin - User: None - IP: 127.0.0.1
2025-06-27 19:43:51.033 | WARNING  | __main__:log_security_event:263 - SECURITY EVENT: FAILED_LOGIN - Failed login attempt for username: admin - User: None - IP: 127.0.0.1
2025-06-27 19:44:25.150 | INFO     | __main__:login:1116 - تم تسجيل دخول المستخدم: admin من IP: 127.0.0.1
2025-06-27 19:44:25.151 | WARNING  | __main__:log_security_event:263 - SECURITY EVENT: SUCCESSFUL_LOGIN - User admin logged in successfully - User: 1 - IP: 127.0.0.1
2025-06-27 19:44:25.243 | INFO     | __main__:dashboard:1266 - تم تحميل لوحة التحكم المحسنة للمستخدم: admin في 0.070 ثانية
2025-06-27 19:44:25.439 | ERROR    | __main__:api_unread_notifications_count:3517 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-27 19:44:41.534 | ERROR    | __main__:api_unread_notifications_count:3517 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-27 19:45:11.551 | ERROR    | __main__:api_unread_notifications_count:3517 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-27 19:45:11.621 | ERROR    | __main__:api_unread_notifications_count:3517 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-27 19:45:29.123 | INFO     | __main__:dashboard:1266 - تم تحميل لوحة التحكم المحسنة للمستخدم: admin في 0.023 ثانية
2025-06-27 19:45:29.284 | ERROR    | __main__:api_unread_notifications_count:3517 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-27 19:45:45.347 | ERROR    | __main__:api_unread_notifications_count:3517 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-27 19:45:53.485 | ERROR    | __main__:api_unread_notifications_count:3517 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-27 19:46:01.682 | ERROR    | __main__:api_unread_notifications_count:3517 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-27 19:46:03.736 | ERROR    | __main__:api_unread_notifications_count:3517 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-27 19:46:05.338 | ERROR    | __main__:api_unread_notifications_count:3517 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-27 19:46:12.794 | ERROR    | __main__:api_unread_notifications_count:3517 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-27 19:46:14.674 | ERROR    | __main__:api_unread_notifications_count:3517 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-27 19:46:28.578 | ERROR    | __main__:maintenance:8217 - خطأ في تحميل صفحة الصيانة: name 'get_system_performance_analysis' is not defined
2025-06-27 19:46:28.628 | ERROR    | __main__:maintenance:8217 - خطأ في تحميل صفحة الصيانة: name 'get_system_performance_analysis' is not defined
2025-06-27 19:46:28.808 | ERROR    | __main__:internal_error:9459 - خطأ داخلي: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-06-27 19:46:28.906 | ERROR    | __main__:internal_error:9459 - خطأ داخلي: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-06-27 19:46:44.691 | ERROR    | __main__:api_unread_notifications_count:3517 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-27 19:47:20.842 | ERROR    | __main__:log_security_event:268 - خطأ في تسجيل الحدث الأمني: 'NoneType' object has no attribute 'is_authenticated'
2025-06-27 19:47:21.286 | ERROR    | __main__:log_security_event:268 - خطأ في تسجيل الحدث الأمني: 'NoneType' object has no attribute 'is_authenticated'
2025-06-27 19:47:21.397 | INFO     | __main__:init_database:7673 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-27 19:47:21.397 | INFO     | __main__:init_database:7674 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-27 19:47:21.409 | INFO     | __main__:init_database:7678 - إجمالي أنواع الوثائق في النظام: 24
2025-06-27 19:47:21.410 | INFO     | __main__:<module>:9499 - بدء تشغيل نظام إدارة الأرشيف العام
2025-06-27 19:47:29.446 | WARNING  | __main__:log_security_event:263 - SECURITY EVENT: FAILED_LOGIN_ATTEMPT - Failed login for user admin - User: None - IP: 127.0.0.1
2025-06-27 19:47:29.447 | WARNING  | __main__:log_security_event:263 - SECURITY EVENT: FAILED_LOGIN - Failed login attempt for username: admin - User: None - IP: 127.0.0.1
2025-06-27 19:47:37.905 | INFO     | __main__:login:1116 - تم تسجيل دخول المستخدم: admin من IP: 127.0.0.1
2025-06-27 19:47:37.906 | WARNING  | __main__:log_security_event:263 - SECURITY EVENT: SUCCESSFUL_LOGIN - User admin logged in successfully - User: 1 - IP: 127.0.0.1
2025-06-27 19:47:38.094 | ERROR    | __main__:api_unread_notifications_count:3517 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-27 19:47:45.538 | ERROR    | __main__:api_unread_notifications_count:3517 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-27 19:47:56.937 | ERROR    | __main__:api_unread_notifications_count:3517 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-27 19:47:58.815 | ERROR    | __main__:maintenance:8217 - خطأ في تحميل صفحة الصيانة: name 'get_system_performance_analysis' is not defined
2025-06-27 19:47:58.910 | ERROR    | __main__:internal_error:9459 - خطأ داخلي: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-06-27 19:47:59.339 | INFO     | __main__:dashboard:1266 - تم تحميل لوحة التحكم المحسنة للمستخدم: admin في 0.076 ثانية
2025-06-27 19:47:59.782 | ERROR    | __main__:api_unread_notifications_count:3517 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-27 19:48:19.134 | ERROR    | __main__:maintenance:8217 - خطأ في تحميل صفحة الصيانة: name 'get_system_performance_analysis' is not defined
2025-06-27 19:48:19.153 | ERROR    | __main__:internal_error:9459 - خطأ داخلي: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-06-27 19:48:19.364 | ERROR    | __main__:api_unread_notifications_count:3517 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-27 19:48:27.531 | ERROR    | __main__:api_unread_notifications_count:3517 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-27 19:48:28.331 | INFO     | __main__:dashboard:1266 - تم تحميل لوحة التحكم المحسنة للمستخدم: admin في 0.028 ثانية
2025-06-27 19:48:28.548 | ERROR    | __main__:api_unread_notifications_count:3517 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-27 19:48:41.100 | ERROR    | __main__:maintenance:8217 - خطأ في تحميل صفحة الصيانة: name 'get_system_performance_analysis' is not defined
2025-06-27 19:48:41.135 | ERROR    | __main__:internal_error:9459 - خطأ داخلي: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-06-27 19:48:41.323 | ERROR    | __main__:api_unread_notifications_count:3517 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-27 19:48:57.600 | ERROR    | __main__:api_unread_notifications_count:3517 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-27 19:49:11.333 | ERROR    | __main__:api_unread_notifications_count:3517 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-27 19:49:27.537 | ERROR    | __main__:api_unread_notifications_count:3517 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-27 19:49:41.334 | ERROR    | __main__:api_unread_notifications_count:3517 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-27 19:49:57.545 | ERROR    | __main__:api_unread_notifications_count:3517 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-27 19:50:11.334 | ERROR    | __main__:api_unread_notifications_count:3517 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-27 19:50:27.545 | ERROR    | __main__:api_unread_notifications_count:3517 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-27 19:50:41.323 | ERROR    | __main__:api_unread_notifications_count:3517 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-27 19:50:57.536 | ERROR    | __main__:api_unread_notifications_count:3517 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-27 19:51:11.323 | ERROR    | __main__:api_unread_notifications_count:3517 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-27 19:51:27.548 | ERROR    | __main__:api_unread_notifications_count:3517 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-27 19:51:41.328 | ERROR    | __main__:api_unread_notifications_count:3517 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-27 19:52:11.322 | ERROR    | __main__:api_unread_notifications_count:3517 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-27 19:52:18.552 | ERROR    | __main__:api_unread_notifications_count:3517 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-27 19:52:41.325 | ERROR    | __main__:api_unread_notifications_count:3517 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-27 19:53:11.534 | ERROR    | __main__:api_unread_notifications_count:3517 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-27 19:53:18.542 | ERROR    | __main__:api_unread_notifications_count:3517 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-27 19:53:41.537 | ERROR    | __main__:api_unread_notifications_count:3517 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-27 19:54:18.537 | ERROR    | __main__:api_unread_notifications_count:3517 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-27 19:54:18.545 | ERROR    | __main__:api_unread_notifications_count:3517 - خطأ في API عدد الإشعارات: type object 'ActivityLog' has no attribute 'timestamp'
2025-06-27 20:00:12.931 | ERROR    | __main__:log_security_event:268 - خطأ في تسجيل الحدث الأمني: 'NoneType' object has no attribute 'is_authenticated'
2025-06-27 20:00:13.352 | ERROR    | __main__:log_security_event:268 - خطأ في تسجيل الحدث الأمني: 'NoneType' object has no attribute 'is_authenticated'
2025-06-27 20:00:13.420 | INFO     | __main__:init_database:7673 - تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية
2025-06-27 20:00:13.421 | INFO     | __main__:init_database:7674 - تم إنشاء 24 نوع وثيقة افتراضي
2025-06-27 20:00:13.426 | INFO     | __main__:init_database:7678 - إجمالي أنواع الوثائق في النظام: 24
2025-06-27 20:00:13.427 | INFO     | __main__:<module>:9832 - بدء تشغيل نظام إدارة الأرشيف العام
