"""
نظام إدارة الأرشيف العام - تطبيق ويب
Archive Management System - Web Application

نظام متكامل لإدارة الوثائق والأرشيف في المؤسسات
Comprehensive system for document and archive management in organizations

المطور: فريق تطوير الأنظمة
Developer: Systems Development Team
"""

import os
from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, send_file, make_response, session
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager, UserMixin, login_user, logout_user, login_required, current_user
from flask_bcrypt import Bcrypt
from flask_wtf import FlaskForm
from wtforms import StringField, PasswordField, SelectField, TextAreaField, FileField, DateField, SubmitField
from wtforms.validators import DataRequired, Email, Length, ValidationError
from flask_wtf.file import FileAllowed
from werkzeug.utils import secure_filename
from flask_babel import Babel, gettext, ngettext, lazy_gettext, get_locale
from flask_mail import Mail, Message
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, timedelta
import secrets
from pathlib import Path
from loguru import logger
import json
from io import BytesIO
import base64
from sqlalchemy import func, extract
import os
import zipfile
import shutil
import qrcode
from barcode import Code128
from barcode.writer import ImageWriter
import time
from PIL import Image, ImageDraw, ImageFont
import io
import pytesseract
import cv2
import numpy as np
import json
import time

# Security imports
from flask_wtf.csrf import CSRFProtect, validate_csrf
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
from flask_talisman import Talisman
import bleach
import re
from cryptography.fernet import Fernet
import bcrypt as bcrypt_lib
from itsdangerous import URLSafeTimedSerializer, BadSignature, SignatureExpired

# إعداد المسارات
BASE_DIR = Path(__file__).parent
UPLOAD_FOLDER = BASE_DIR / 'uploads'
UPLOAD_FOLDER.mkdir(exist_ok=True)
(UPLOAD_FOLDER / 'incoming').mkdir(exist_ok=True)
(UPLOAD_FOLDER / 'outgoing').mkdir(exist_ok=True)
(UPLOAD_FOLDER / 'documents').mkdir(exist_ok=True)
(UPLOAD_FOLDER / 'profiles').mkdir(exist_ok=True)

# إنشاء التطبيق
app = Flask(__name__)

# إعدادات التطبيق الأساسية
app.config['SECRET_KEY'] = secrets.token_hex(32)  # Increased key length for better security
app.config['SQLALCHEMY_DATABASE_URI'] = f'sqlite:///{BASE_DIR}/archive_system.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Security configurations
app.config['WTF_CSRF_ENABLED'] = True
app.config['WTF_CSRF_TIME_LIMIT'] = 3600  # 1 hour
app.config['SESSION_COOKIE_SECURE'] = True
app.config['SESSION_COOKIE_HTTPONLY'] = True
app.config['SESSION_COOKIE_SAMESITE'] = 'Lax'
app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(hours=2)
app.config['MAX_CONTENT_LENGTH'] = 50 * 1024 * 1024  # 50MB max file size
app.config['SQLALCHEMY_ENGINE_OPTIONS'] = {
    'pool_pre_ping': True,
    'pool_recycle': 300,
    'connect_args': {
        'check_same_thread': False,
        'timeout': 10
    }
}

# إعداد Babel للترجمة
app.config['LANGUAGES'] = {
    'ar': 'العربية',
    'en': 'English'
}
app.config['BABEL_DEFAULT_LOCALE'] = 'ar'
app.config['BABEL_DEFAULT_TIMEZONE'] = 'UTC'

# إعداد البريد الإلكتروني
app.config['MAIL_SERVER'] = 'smtp.gmail.com'
app.config['MAIL_PORT'] = 587
app.config['MAIL_USE_TLS'] = True
app.config['MAIL_USERNAME'] = '<EMAIL>'  # يجب تغييرها
app.config['MAIL_PASSWORD'] = 'your-app-password'     # يجب تغييرها
app.config['MAIL_DEFAULT_SENDER'] = '<EMAIL>'
app.config['UPLOAD_FOLDER'] = str(UPLOAD_FOLDER)
app.config['MAX_CONTENT_LENGTH'] = 50 * 1024 * 1024  # 50MB max file size

# تهيئة الإضافات
db = SQLAlchemy(app)
bcrypt = Bcrypt(app)
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة'
login_manager.login_message_category = 'info'

# Security extensions initialization
csrf = CSRFProtect(app)
limiter = Limiter(
    key_func=get_remote_address,
    default_limits=["200 per day", "50 per hour"],
    storage_uri="memory://"
)
limiter.init_app(app)

# Talisman for security headers
talisman = Talisman(
    app,
    force_https=False,  # Set to True in production
    strict_transport_security=True,
    content_security_policy={
        'default-src': "'self'",
        'script-src': "'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com",
        'style-src': "'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://fonts.googleapis.com",
        'font-src': "'self' https://fonts.gstatic.com https://cdn.jsdelivr.net",
        'img-src': "'self' data: https:",
        'connect-src': "'self'",
        'frame-ancestors': "'none'"
    }
)

# Encryption key for sensitive data
encryption_key = Fernet.generate_key()
cipher_suite = Fernet(encryption_key)

# Serializer for secure tokens
serializer = URLSafeTimedSerializer(app.config['SECRET_KEY'])

# تهيئة Babel والبريد الإلكتروني
babel = Babel()
babel.init_app(app)
mail = Mail(app)

def get_locale():
    """تحديد اللغة المطلوبة"""
    # التحقق من اللغة المحددة في الجلسة
    if 'language' in session:
        return session['language']

    # التحقق من اللغة المفضلة في المتصفح
    return request.accept_languages.best_match(app.config['LANGUAGES'].keys()) or 'ar'

# تسجيل دالة تحديد اللغة
babel.locale_selector_func = get_locale

# إضافة فلاتر Jinja2
@app.template_filter('from_json')
def from_json_filter(value):
    """تحويل JSON string إلى Python object"""
    try:
        return json.loads(value)
    except:
        return {}

# إعداد السجلات
logger.add("logs/archive_system.log", rotation="1 MB", retention="30 days")

# Security helper functions
def sanitize_input(text):
    """تنظيف المدخلات من XSS"""
    if not text:
        return text

    allowed_tags = ['p', 'br', 'strong', 'em', 'u', 'ol', 'ul', 'li']
    allowed_attributes = {}

    return bleach.clean(text, tags=allowed_tags, attributes=allowed_attributes, strip=True)

def encrypt_sensitive_data(data):
    """تشفير البيانات الحساسة"""
    try:
        if isinstance(data, str):
            data = data.encode('utf-8')
        return cipher_suite.encrypt(data).decode('utf-8')
    except Exception as e:
        logger.error(f"خطأ في تشفير البيانات: {e}")
        return None

def decrypt_sensitive_data(encrypted_data):
    """فك تشفير البيانات الحساسة"""
    try:
        if isinstance(encrypted_data, str):
            encrypted_data = encrypted_data.encode('utf-8')
        return cipher_suite.decrypt(encrypted_data).decode('utf-8')
    except Exception as e:
        logger.error(f"خطأ في فك تشفير البيانات: {e}")
        return None

def validate_file_upload(file):
    """التحقق من أمان الملف المرفوع"""
    if not file or not file.filename:
        return False, "لم يتم اختيار ملف"

    # Check file extension
    allowed_extensions = {'pdf', 'doc', 'docx', 'txt', 'jpg', 'jpeg', 'png', 'gif'}
    file_ext = file.filename.rsplit('.', 1)[1].lower() if '.' in file.filename else ''

    if file_ext not in allowed_extensions:
        return False, f"نوع الملف {file_ext} غير مسموح"

    # Check file size (already handled by Flask config, but double-check)
    file.seek(0, 2)  # Seek to end
    file_size = file.tell()
    file.seek(0)  # Reset to beginning

    if file_size > 50 * 1024 * 1024:  # 50MB
        return False, "حجم الملف كبير جداً (الحد الأقصى 50 ميجابايت)"

    # Check for malicious content in filename
    dangerous_patterns = [
        r'\.\./', r'\.\.\\', r'<script', r'javascript:', r'vbscript:',
        r'onload=', r'onerror=', r'<iframe', r'<object', r'<embed'
    ]

    filename_lower = file.filename.lower()
    for pattern in dangerous_patterns:
        if re.search(pattern, filename_lower):
            return False, "اسم الملف يحتوي على محتوى مشبوه"

    return True, "الملف آمن"

def log_security_event(event_type, details, user_id=None, ip_address=None):
    """تسجيل الأحداث الأمنية"""
    try:
        if not user_id and current_user.is_authenticated:
            user_id = current_user.id

        if not ip_address:
            ip_address = request.remote_addr

        security_log = {
            'timestamp': datetime.utcnow().isoformat(),
            'event_type': event_type,
            'details': details,
            'user_id': user_id,
            'ip_address': ip_address,
            'user_agent': request.headers.get('User-Agent', '')
        }

        logger.warning(f"SECURITY EVENT: {event_type} - {details} - User: {user_id} - IP: {ip_address}")

        # In production, you might want to send this to a security monitoring system

    except Exception as e:
        logger.error(f"خطأ في تسجيل الحدث الأمني: {e}")

def check_password_strength(password):
    """فحص قوة كلمة المرور"""
    if len(password) < 8:
        return False, "كلمة المرور يجب أن تكون 8 أحرف على الأقل"

    if not re.search(r'[A-Z]', password):
        return False, "كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل"

    if not re.search(r'[a-z]', password):
        return False, "كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل"

    if not re.search(r'\d', password):
        return False, "كلمة المرور يجب أن تحتوي على رقم واحد على الأقل"

    if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
        return False, "كلمة المرور يجب أن تحتوي على رمز خاص واحد على الأقل"

    return True, "كلمة المرور قوية"

# إضافة دوال مساعدة للقوالب
@app.template_global()
def get_current_date():
    """إرجاع التاريخ الحالي"""
    return datetime.now().strftime('%Y-%m-%d')

@app.template_global()
def get_current_year():
    """إرجاع السنة الحالية"""
    return datetime.now().year

@app.template_global()
def format_date(date, format='%Y/%m/%d'):
    """تنسيق التاريخ"""
    if date:
        return date.strftime(format)
    return ''

@app.template_global()
def generate_auto_number(prefix='DOC'):
    """توليد رقم تلقائي"""
    year = datetime.now().year
    day_of_year = datetime.now().timetuple().tm_yday
    return f"{prefix}-{year}-{day_of_year:04d}"

# دوال معالجة الملفات
def allowed_file(filename):
    """التحقق من نوع الملف المسموح"""
    ALLOWED_EXTENSIONS = {'pdf', 'doc', 'docx', 'txt', 'jpg', 'jpeg', 'png', 'gif'}
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def get_file_type(filename):
    """تحديد نوع الملف"""
    if not filename:
        return 'unknown'
    extension = filename.rsplit('.', 1)[1].lower() if '.' in filename else ''

    file_types = {
        'pdf': 'pdf',
        'doc': 'document',
        'docx': 'document',
        'txt': 'text',
        'jpg': 'image',
        'jpeg': 'image',
        'png': 'image',
        'gif': 'image'
    }

    return file_types.get(extension, 'unknown')

def save_file(file, folder_name):
    """حفظ الملف وإرجاع معلوماته - محسن للأمان"""
    try:
        # Security validation
        is_valid, error_message = validate_file_upload(file)
        if not is_valid:
            log_security_event('INVALID_FILE_UPLOAD', error_message)
            return None

        if not file or not allowed_file(file.filename):
            log_security_event('DISALLOWED_FILE_TYPE', f"File type not allowed: {file.filename}")
            return None

        # Secure filename processing
        filename = secure_filename(file.filename)
        if not filename:
            log_security_event('INVALID_FILENAME', f"Invalid filename: {file.filename}")
            return None

        # Add timestamp and random component for uniqueness
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S_')
        random_suffix = secrets.token_hex(8)
        unique_filename = f"{timestamp}{random_suffix}_{filename}"

        folder_path = UPLOAD_FOLDER / folder_name
        folder_path.mkdir(exist_ok=True)

        file_path = folder_path / unique_filename

        # Save file securely
        file.save(str(file_path))

        # Set secure file permissions (read/write for owner only)
        os.chmod(str(file_path), 0o600)

        file_info = {
            'original_name': file.filename,
            'saved_name': unique_filename,
            'file_path': str(file_path),
            'file_size': file_path.stat().st_size,
            'file_type': get_file_type(file.filename),
            'upload_time': datetime.utcnow().isoformat()
        }

        # Log successful upload
        logger.info(f"File uploaded successfully: {unique_filename} by user {current_user.id if current_user.is_authenticated else 'anonymous'}")

        return file_info

    except Exception as e:
        logger.error(f"خطأ في حفظ الملف: {e}")
        log_security_event('FILE_UPLOAD_ERROR', str(e))
        return None

def delete_file(file_path):
    """حذف الملف"""
    try:
        if file_path and os.path.exists(file_path):
            os.remove(file_path)
            return True
    except Exception as e:
        logger.error(f"خطأ في حذف الملف {file_path}: {e}")
    return False

@app.route('/download/<path:filename>')
@login_required
def download_file(filename):
    """تحميل الملف"""
    try:
        # البحث عن الملف في جميع المجلدات
        for folder in ['incoming', 'outgoing', 'documents', 'profiles']:
            file_path = UPLOAD_FOLDER / folder / filename
            if file_path.exists():
                return send_file(str(file_path), as_attachment=True)

        flash('الملف غير موجود', 'error')
        return redirect(request.referrer or url_for('dashboard'))

    except Exception as e:
        logger.error(f"خطأ في تحميل الملف {filename}: {e}")
        flash('حدث خطأ في تحميل الملف', 'error')
        return redirect(request.referrer or url_for('dashboard'))

@app.route('/documents/<int:doc_id>/download')
@login_required
def download_document_file(doc_id):
    """تحميل ملف الوثيقة"""
    try:
        document = Document.query.get_or_404(doc_id)

        if not document.file_name:
            flash('لا يوجد ملف مرفق بهذه الوثيقة', 'error')
            return redirect(url_for('view_document', id=doc_id))

        # البحث عن الملف
        file_path = UPLOAD_FOLDER / 'documents' / document.file_name
        if file_path.exists():
            return send_file(str(file_path), as_attachment=True, download_name=document.file_name)

        flash('الملف غير موجود', 'error')
        return redirect(url_for('view_document', id=doc_id))

    except Exception as e:
        logger.error(f"خطأ في تحميل ملف الوثيقة {doc_id}: {e}")
        flash('حدث خطأ في تحميل الملف', 'error')
        return redirect(url_for('view_document', id=doc_id))

# نماذج قاعدة البيانات
class User(UserMixin, db.Model):
    """نموذج المستخدم"""
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    full_name = db.Column(db.String(200), nullable=False)
    password_hash = db.Column(db.String(128), nullable=False)
    role = db.Column(db.String(20), default='employee')  # admin, employee, viewer
    department = db.Column(db.String(100))  # للتوافق مع النظام القديم
    department_id = db.Column(db.Integer, db.ForeignKey('departments.id'))  # ربط بجدول الأقسام
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_login = db.Column(db.DateTime)
    
    def set_password(self, password):
        """تشفير كلمة المرور بشكل آمن"""
        # Check password strength first
        is_strong, message = check_password_strength(password)
        if not is_strong:
            raise ValueError(f"كلمة المرور ضعيفة: {message}")

        # Use bcrypt with higher rounds for better security
        salt = bcrypt_lib.gensalt(rounds=12)
        self.password_hash = bcrypt_lib.hashpw(password.encode('utf-8'), salt).decode('utf-8')

        # Log password change
        log_security_event('PASSWORD_CHANGED', f"Password changed for user {self.username}")

    def check_password(self, password):
        """التحقق من كلمة المرور"""
        try:
            if not self.password_hash:
                return False

            # Try new bcrypt method first
            if self.password_hash.startswith('$2b$'):
                result = bcrypt_lib.checkpw(password.encode('utf-8'), self.password_hash.encode('utf-8'))
            else:
                # Fallback to old method for existing passwords
                result = bcrypt.check_password_hash(self.password_hash, password)

            if not result:
                log_security_event('FAILED_LOGIN_ATTEMPT', f"Failed login for user {self.username}")

            return result

        except Exception as e:
            logger.error(f"خطأ في التحقق من كلمة المرور: {e}")
            log_security_event('PASSWORD_CHECK_ERROR', f"Error checking password for user {self.username}: {e}")
            return False
    
    def is_admin(self):
        """التحقق من صلاحيات المدير"""
        return self.role == 'admin'

class Document(db.Model):
    """نموذج الوثيقة"""
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    document_type = db.Column(db.String(50), default='عام')
    status = db.Column(db.String(50), default='نشط')
    file_name = db.Column(db.String(255))
    file_path = db.Column(db.String(500))
    file_size = db.Column(db.Integer)

    qr_code = db.Column(db.String(100), unique=True)
    tags = db.Column(db.String(500))
    extracted_text = db.Column(db.Text)  # النص المستخرج بـ OCR
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    creator = db.relationship('User', backref=db.backref('documents', lazy=True))

class IncomingDocument(db.Model):
    """نموذج الكتب الواردة"""
    id = db.Column(db.Integer, primary_key=True)
    incoming_number = db.Column(db.String(50), unique=True, nullable=False)
    sender_name = db.Column(db.String(200), nullable=False)
    subject = db.Column(db.String(500), nullable=False)
    received_date = db.Column(db.DateTime, nullable=False)
    status = db.Column(db.String(50), default='جديد')
    priority = db.Column(db.String(20), default='عادي')
    notes = db.Column(db.Text)

    # معلومات الملف المرفق
    file_name = db.Column(db.String(255))
    file_path = db.Column(db.String(500))
    file_size = db.Column(db.Integer)
    file_type = db.Column(db.String(50))

    received_by = db.Column(db.Integer, db.ForeignKey('user.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    receiver = db.relationship('User', backref=db.backref('incoming_documents', lazy=True))

class OutgoingDocument(db.Model):
    """نموذج الكتب الصادرة"""
    id = db.Column(db.Integer, primary_key=True)
    outgoing_number = db.Column(db.String(50), unique=True, nullable=False)
    recipient_name = db.Column(db.String(200), nullable=False)
    subject = db.Column(db.String(500), nullable=False)
    sent_date = db.Column(db.DateTime, nullable=False)
    status = db.Column(db.String(50), default='مسودة')
    priority = db.Column(db.String(20), default='عادي')
    notes = db.Column(db.Text)

    # معلومات الملف المرفق
    file_name = db.Column(db.String(255))
    file_path = db.Column(db.String(500))
    file_size = db.Column(db.Integer)
    file_type = db.Column(db.String(50))

    prepared_by = db.Column(db.Integer, db.ForeignKey('user.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    preparer = db.relationship('User', backref=db.backref('outgoing_documents', lazy=True), foreign_keys=[prepared_by])

class SystemSettings(db.Model):
    """نموذج إعدادات النظام"""
    __tablename__ = 'system_settings'

    id = db.Column(db.Integer, primary_key=True)
    setting_key = db.Column(db.String(100), unique=True, nullable=False)
    setting_value = db.Column(db.Text)
    setting_type = db.Column(db.String(20), default='text')  # text, number, boolean, json
    description = db.Column(db.String(500))
    category = db.Column(db.String(50), default='general')
    is_editable = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    @classmethod
    def get_setting(cls, key, default=None):
        """الحصول على قيمة إعداد"""
        setting = cls.query.filter_by(setting_key=key).first()
        if setting:
            if setting.setting_type == 'boolean':
                return setting.setting_value.lower() == 'true'
            elif setting.setting_type == 'number':
                try:
                    return int(setting.setting_value)
                except:
                    return float(setting.setting_value)
            elif setting.setting_type == 'json':
                try:
                    return json.loads(setting.setting_value)
                except:
                    return {}
            return setting.setting_value
        return default

    @classmethod
    def set_setting(cls, key, value, setting_type='text', description='', category='general'):
        """تعيين قيمة إعداد"""
        setting = cls.query.filter_by(setting_key=key).first()
        if setting:
            setting.setting_value = str(value)
            setting.updated_at = datetime.utcnow()
        else:
            setting = cls(
                setting_key=key,
                setting_value=str(value),
                setting_type=setting_type,
                description=description,
                category=category
            )
            db.session.add(setting)
        db.session.commit()
        return setting

class ActivityLog(db.Model):
    """نموذج سجل العمليات"""
    __tablename__ = 'activity_logs'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    action = db.Column(db.String(50), nullable=False)  # create, update, delete, view
    entity_type = db.Column(db.String(50), nullable=False)  # document, incoming, outgoing, user
    entity_id = db.Column(db.Integer, nullable=False)
    entity_name = db.Column(db.String(200))
    details = db.Column(db.Text)  # JSON string with change details
    ip_address = db.Column(db.String(45))
    user_agent = db.Column(db.String(500))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    user = db.relationship('User', backref='activity_logs')

    def __repr__(self):
        return f'<ActivityLog {self.user.username}: {self.action} {self.entity_type}>'

class DeletedItem(db.Model):
    """نموذج سلة المهملات"""
    __tablename__ = 'deleted_items'

    id = db.Column(db.Integer, primary_key=True)
    original_id = db.Column(db.Integer, nullable=False)
    entity_type = db.Column(db.String(50), nullable=False)  # document, incoming, outgoing
    entity_data = db.Column(db.Text, nullable=False)  # JSON string with original data
    deleted_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    deleted_at = db.Column(db.DateTime, default=datetime.utcnow)
    can_restore = db.Column(db.Boolean, default=True)

    # العلاقات
    deleter = db.relationship('User', backref='deleted_items')

    def __repr__(self):
        return f'<DeletedItem {self.entity_type}:{self.original_id}>'

class PasswordReset(db.Model):
    """نموذج إعادة تعيين كلمة المرور"""
    __tablename__ = 'password_resets'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    token = db.Column(db.String(100), unique=True, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    expires_at = db.Column(db.DateTime, nullable=False)
    used = db.Column(db.Boolean, default=False)

    # العلاقات
    user = db.relationship('User', backref='password_resets')

    def is_expired(self):
        return datetime.utcnow() > self.expires_at

    def __repr__(self):
        return f'<PasswordReset {self.user.username}>'

class CalendarEvent(db.Model):
    """نموذج أحداث التقويم"""
    __tablename__ = 'calendar_events'

    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    start_date = db.Column(db.DateTime, nullable=False)
    end_date = db.Column(db.DateTime)
    all_day = db.Column(db.Boolean, default=False)
    color = db.Column(db.String(7), default='#3498db')  # Hex color

    # ربط بالوثائق والكتب
    document_id = db.Column(db.Integer, db.ForeignKey('document.id'))
    incoming_id = db.Column(db.Integer, db.ForeignKey('incoming_document.id'))
    outgoing_id = db.Column(db.Integer, db.ForeignKey('outgoing_document.id'))

    # معلومات المنشئ
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # العلاقات
    creator = db.relationship('User', backref='calendar_events')
    document = db.relationship('Document', backref='calendar_events')
    incoming_document = db.relationship('IncomingDocument', backref='calendar_events')
    outgoing_document = db.relationship('OutgoingDocument', backref='calendar_events')

    def to_dict(self):
        """تحويل الحدث إلى dictionary للـ JSON"""
        return {
            'id': self.id,
            'title': self.title,
            'description': self.description,
            'start': self.start_date.isoformat(),
            'end': self.end_date.isoformat() if self.end_date else None,
            'allDay': self.all_day,
            'color': self.color,
            'extendedProps': {
                'document_id': self.document_id,
                'incoming_id': self.incoming_id,
                'outgoing_id': self.outgoing_id,
                'created_by': self.created_by
            }
        }

    def __repr__(self):
        return f'<CalendarEvent {self.title}>'

class Notification(db.Model):
    """نموذج الإشعارات"""
    __tablename__ = 'notifications'

    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(200), nullable=False)
    message = db.Column(db.Text, nullable=False)
    type = db.Column(db.String(50), default='info')  # info, success, warning, error

    # المستخدم المستهدف
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)

    # حالة الإشعار
    is_read = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    read_at = db.Column(db.DateTime)

    # ربط بالكيانات
    entity_type = db.Column(db.String(50))  # document, incoming, outgoing, event
    entity_id = db.Column(db.Integer)

    # العلاقات
    user = db.relationship('User', backref='notifications')

    def mark_as_read(self):
        """تمييز الإشعار كمقروء"""
        self.is_read = True
        self.read_at = datetime.utcnow()
        db.session.commit()

    def __repr__(self):
        return f'<Notification {self.title}>'

class Department(db.Model):
    """نموذج الأقسام"""
    __tablename__ = 'departments'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    description = db.Column(db.Text)
    manager_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    manager = db.relationship('User', backref='managed_department', foreign_keys=[manager_id])
    employees = db.relationship('User', backref='department_obj', foreign_keys='User.department_id')

    def __repr__(self):
        return f'<Department {self.name}>'

class DocumentType(db.Model):
    """نموذج أنواع الوثائق"""
    __tablename__ = 'document_types'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    description = db.Column(db.Text)
    color = db.Column(db.String(7), default='#3498db')  # Hex color
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def __repr__(self):
        return f'<DocumentType {self.name}>'

class DigitalSignature(db.Model):
    """نموذج التوقيعات الرقمية المتدرجة"""
    __tablename__ = 'digital_signatures'

    id = db.Column(db.Integer, primary_key=True)
    document_id = db.Column(db.Integer, db.ForeignKey('document.id'), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    signature_hash = db.Column(db.String(256), nullable=False)
    signature_data = db.Column(db.Text, nullable=False)  # JSON data
    signature_type = db.Column(db.String(20), nullable=False, default='normal')  # normal, certified
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)
    is_valid = db.Column(db.Boolean, default=True)
    algorithm = db.Column(db.String(50), default='HMAC-SHA256')
    notes = db.Column(db.Text)  # ملاحظات التوقيع

    # العلاقات
    document = db.relationship('Document', backref='digital_signatures')
    user = db.relationship('User', backref='digital_signatures')

    def verify_signature(self):
        """التحقق من صحة التوقيع"""
        try:
            import json
            signature_data = json.loads(self.signature_data)
            return verify_digital_signature(signature_data)
        except:
            return False

    def get_signature_type_display(self):
        """عرض نوع التوقيع بالعربية"""
        types = {
            'normal': 'توقيع عادي',
            'certified': 'توقيع معتمد'
        }
        return types.get(self.signature_type, 'غير محدد')

    def get_signature_level(self):
        """مستوى التوقيع"""
        if self.signature_type == 'certified':
            return 'عالي'
        return 'عادي'

    def __repr__(self):
        return f'<DigitalSignature {self.document_id}:{self.user_id}:{self.signature_type}>'

# نماذج الويب
class LoginForm(FlaskForm):
    """نموذج تسجيل الدخول"""
    username = StringField('اسم المستخدم', validators=[DataRequired(), Length(min=3, max=80)])
    password = PasswordField('كلمة المرور', validators=[DataRequired(), Length(min=6)])

class DocumentForm(FlaskForm):
    """نموذج إضافة وثيقة - مُعاد بناؤه من الصفر"""
    title = StringField('عنوان الوثيقة', validators=[
        DataRequired(message='عنوان الوثيقة مطلوب'),
        Length(min=3, max=200, message='عنوان الوثيقة يجب أن يكون بين 3 و 200 حرف')
    ], render_kw={'placeholder': 'أدخل عنوان الوثيقة', 'class': 'form-control'})

    description = TextAreaField('وصف الوثيقة', validators=[
        Length(max=1000, message='الوصف يجب ألا يتجاوز 1000 حرف')
    ], render_kw={'placeholder': 'أدخل وصف مفصل للوثيقة (اختياري)', 'rows': 4, 'class': 'form-control'})

    document_type = SelectField('نوع الوثيقة', choices=[], validators=[
        DataRequired(message='يرجى اختيار نوع الوثيقة')
    ], render_kw={'class': 'form-select'})

    tags = StringField('الكلمات المفتاحية', validators=[
        Length(max=500, message='الكلمات المفتاحية يجب ألا تتجاوز 500 حرف')
    ], render_kw={'placeholder': 'أدخل الكلمات المفتاحية مفصولة بفواصل (اختياري)', 'class': 'form-control'})

    file = FileField('الملف المرفق', validators=[
        FileAllowed(['pdf', 'doc', 'docx', 'txt', 'jpg', 'jpeg', 'png', 'gif'],
                   'نوع الملف غير مدعوم. الأنواع المسموحة: PDF, DOC, DOCX, TXT, JPG, PNG, GIF')
    ], render_kw={'class': 'form-control'})

    # زر واحد فقط
    submit = SubmitField('حفظ الوثيقة', render_kw={'class': 'btn btn-success'})

class IncomingForm(FlaskForm):
    """نموذج الكتب الواردة"""
    incoming_number = StringField('رقم الوارد')
    sender_name = StringField('الجهة المرسلة', validators=[DataRequired(), Length(min=2, max=200)])
    subject = StringField('موضوع الكتاب', validators=[DataRequired(), Length(min=3, max=300)])
    received_date = DateField('تاريخ الاستلام', validators=[DataRequired()])
    priority = SelectField('الأولوية', choices=[
        ('عادي', 'عادي'),
        ('مهم', 'مهم'),
        ('عاجل', 'عاجل')
    ], validators=[DataRequired()])
    notes = TextAreaField('ملاحظات')
    file = FileField('الملف المرفق', validators=[FileAllowed(['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png', 'gif'], 'نوع الملف غير مدعوم')])

class OutgoingForm(FlaskForm):
    """نموذج الكتب الصادرة"""
    outgoing_number = StringField('رقم الصادر')
    recipient_name = StringField('الجهة المرسل إليها', validators=[DataRequired(), Length(min=2, max=200)])
    subject = StringField('موضوع الكتاب', validators=[DataRequired(), Length(min=3, max=300)])
    sent_date = DateField('تاريخ الإرسال', validators=[DataRequired()])
    priority = SelectField('الأولوية', choices=[
        ('عادي', 'عادي'),
        ('مهم', 'مهم'),
        ('عاجل', 'عاجل')
    ], validators=[DataRequired()])
    notes = TextAreaField('ملاحظات')
    file = FileField('الملف المرفق', validators=[FileAllowed(['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png', 'gif'], 'نوع الملف غير مدعوم')])

class ReportForm(FlaskForm):
    """نموذج التقارير"""
    report_type = SelectField('نوع التقرير', choices=[
        ('documents', 'تقرير الوثائق'),
        ('incoming', 'تقرير الكتب الواردة'),
        ('outgoing', 'تقرير الكتب الصادرة'),
        ('users', 'تقرير المستخدمين'),
        ('statistics', 'تقرير إحصائي شامل')
    ], validators=[DataRequired()])

    date_from = DateField('من تاريخ')
    date_to = DateField('إلى تاريخ')

    status_filter = SelectField('فلترة حسب الحالة', choices=[
        ('', 'جميع الحالات'),
        ('نشط', 'نشط'),
        ('مؤرشف', 'مؤرشف'),
        ('جديد', 'جديد'),
        ('قيد المراجعة', 'قيد المراجعة'),
        ('مكتمل', 'مكتمل'),
        ('مسودة', 'مسودة'),
        ('تم الإرسال', 'تم الإرسال')
    ])

    priority_filter = SelectField('فلترة حسب الأولوية', choices=[
        ('', 'جميع الأولويات'),
        ('عادي', 'عادي'),
        ('مهم', 'مهم'),
        ('عاجل', 'عاجل')
    ])

    user_filter = SelectField('فلترة حسب المستخدم', choices=[('', 'جميع المستخدمين')])

    export_format = SelectField('تنسيق التصدير', choices=[
        ('html', 'عرض في المتصفح'),
        ('pdf', 'ملف PDF'),
        ('excel', 'ملف Excel'),
        ('csv', 'ملف CSV')
    ], validators=[DataRequired()])

class SettingsForm(FlaskForm):
    """نموذج إعدادات النظام"""
    system_name = StringField('اسم النظام', validators=[DataRequired()])
    organization_name = StringField('اسم المؤسسة', validators=[DataRequired()])
    contact_email = StringField('البريد الإلكتروني للتواصل')
    contact_phone = StringField('رقم الهاتف')
    address = TextAreaField('العنوان')

    # إعدادات الأرشفة
    auto_archive_days = StringField('عدد أيام الأرشفة التلقائية')
    max_file_size = StringField('الحد الأقصى لحجم الملف (بالميجابايت)')
    allowed_extensions = StringField('امتدادات الملفات المسموحة')

    # إعدادات الأمان
    session_timeout = StringField('مهلة انتهاء الجلسة (بالدقائق)')
    password_min_length = StringField('الحد الأدنى لطول كلمة المرور')
    enable_two_factor = SelectField('تفعيل المصادقة الثنائية', choices=[
        ('false', 'معطل'),
        ('true', 'مفعل')
    ])

    # إعدادات النسخ الاحتياطي
    backup_frequency = SelectField('تكرار النسخ الاحتياطي', choices=[
        ('daily', 'يومي'),
        ('weekly', 'أسبوعي'),
        ('monthly', 'شهري')
    ])
    backup_retention_days = StringField('عدد أيام الاحتفاظ بالنسخ الاحتياطية')

class ForgotPasswordForm(FlaskForm):
    """نموذج نسيت كلمة المرور"""
    email = StringField('البريد الإلكتروني', validators=[DataRequired(), Email()])
    submit = SubmitField('إرسال رابط إعادة التعيين')

class ResetPasswordForm(FlaskForm):
    """نموذج إعادة تعيين كلمة المرور"""
    password = PasswordField('كلمة المرور الجديدة', validators=[DataRequired(), Length(min=6)])
    confirm_password = PasswordField('تأكيد كلمة المرور', validators=[DataRequired()])
    submit = SubmitField('تعيين كلمة المرور')

    def validate_confirm_password(self, field):
        if field.data != self.password.data:
            raise ValidationError('كلمات المرور غير متطابقة')

class LanguageForm(FlaskForm):
    """نموذج اختيار اللغة"""
    language = SelectField('اللغة / Language', choices=[
        ('ar', 'العربية'),
        ('en', 'English')
    ])
    submit = SubmitField('تغيير / Change')

class CalendarEventForm(FlaskForm):
    """نموذج إضافة حدث في التقويم"""
    title = StringField('عنوان الحدث', validators=[DataRequired()])
    description = TextAreaField('الوصف')
    start_date = DateField('تاريخ البداية', validators=[DataRequired()])
    start_time = StringField('وقت البداية', default='09:00')
    end_date = DateField('تاريخ النهاية')
    end_time = StringField('وقت النهاية', default='10:00')
    all_day = SelectField('طوال اليوم', choices=[('0', 'لا'), ('1', 'نعم')], default='0')
    color = SelectField('اللون', choices=[
        ('#3498db', 'أزرق'),
        ('#2ecc71', 'أخضر'),
        ('#e74c3c', 'أحمر'),
        ('#f39c12', 'برتقالي'),
        ('#9b59b6', 'بنفسجي'),
        ('#1abc9c', 'تركوازي')
    ], default='#3498db')

    # ربط بالوثائق (اختياري)
    document_id = SelectField('ربط بوثيقة', choices=[('', 'لا يوجد')])
    incoming_id = SelectField('ربط بكتاب وارد', choices=[('', 'لا يوجد')])
    outgoing_id = SelectField('ربط بكتاب صادر', choices=[('', 'لا يوجد')])

    submit = SubmitField('إضافة الحدث')

class UserForm(FlaskForm):
    """نموذج إضافة/تعديل مستخدم"""
    username = StringField('اسم المستخدم', validators=[DataRequired(), Length(min=3, max=50)])
    email = StringField('البريد الإلكتروني', validators=[DataRequired(), Email()])
    full_name = StringField('الاسم الكامل', validators=[Length(max=100)])
    password = PasswordField('كلمة المرور', validators=[Length(min=6)])
    department = StringField('القسم', validators=[Length(max=100)])
    role = SelectField('الدور', choices=[
        ('employee', 'موظف'),
        ('admin', 'مدير')
    ], default='employee')
    is_active = SelectField('الحالة', choices=[
        (True, 'نشط'),
        (False, 'غير نشط')
    ], coerce=bool, default=True)
    submit = SubmitField('حفظ')

class DepartmentForm(FlaskForm):
    """نموذج إضافة/تعديل قسم"""
    name = StringField('اسم القسم', validators=[DataRequired(), Length(min=2, max=100)])
    description = TextAreaField('الوصف')
    manager_id = SelectField('مدير القسم', choices=[('', 'لا يوجد')])
    is_active = SelectField('الحالة', choices=[
        ('True', 'نشط'),
        ('False', 'غير نشط')
    ], default='True')
    submit = SubmitField('حفظ')

class DocumentTypeForm(FlaskForm):
    """نموذج إضافة/تعديل نوع الوثيقة"""
    name = StringField('اسم نوع الوثيقة', validators=[DataRequired(), Length(min=2, max=100)])
    description = TextAreaField('الوصف')
    submit = SubmitField('حفظ')

@login_manager.user_loader
def load_user(user_id):
    """تحميل المستخدم"""
    return User.query.get(int(user_id))

# المسارات الرئيسية
@app.route('/')
def index():
    """الصفحة الرئيسية"""
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
@limiter.limit("10 per minute")  # Rate limiting for login attempts
def login():
    """تسجيل الدخول - محسن للأمان"""
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    
    form = LoginForm()
    if form.validate_on_submit():
        try:
            # Sanitize input
            username = sanitize_input(form.username.data.strip())
            password = form.password.data

            # Validate CSRF token
            validate_csrf(form.csrf_token.data)

            # Find user securely
            user = User.query.filter_by(username=username).first()

            if user and user.check_password(password) and user.is_active:
                # Successful login
                login_user(user, remember=False, duration=timedelta(hours=2))
                user.last_login = datetime.utcnow()

                # Clear any previous failed attempts
                session.pop('failed_login_attempts', None)
                session.pop('last_failed_attempt', None)

                db.session.commit()

                # تسجيل النشاط الآمن
                try:
                    activity = ActivityLog(
                        user_id=user.id,
                        action='login',
                        entity_type='user',
                        entity_id=user.id,
                        entity_name=user.full_name or user.username,
                        details=json.dumps({
                            'login_time': datetime.utcnow().isoformat(),
                            'success': True,
                            'ip_address': request.remote_addr
                        }),
                        ip_address=request.remote_addr,
                        user_agent=request.headers.get('User-Agent', '')[:500]
                    )
                    db.session.add(activity)
                    db.session.commit()
                except Exception as e:
                    logger.error(f"خطأ في تسجيل نشاط تسجيل الدخول: {e}")

                logger.info(f"تم تسجيل دخول المستخدم: {user.username} من IP: {request.remote_addr}")
                log_security_event('SUCCESSFUL_LOGIN', f"User {user.username} logged in successfully")

                # Secure redirect handling
                next_page = request.args.get('next')
                if next_page and next_page.startswith('/'):
                    return redirect(next_page)
                else:
                    return redirect(url_for('dashboard'))
            else:
                # Failed login attempt
                failed_attempts = session.get('failed_login_attempts', 0) + 1
                session['failed_login_attempts'] = failed_attempts
                session['last_failed_attempt'] = datetime.utcnow().isoformat()

                # Log failed attempt
                log_security_event('FAILED_LOGIN', f"Failed login attempt for username: {username}")

                if failed_attempts >= 5:
                    log_security_event('MULTIPLE_FAILED_LOGINS', f"Multiple failed login attempts for username: {username}")
                    flash('تم تجاوز عدد المحاولات المسموحة. يرجى المحاولة لاحقاً', 'error')
                else:
                    flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')

        except Exception as e:
            logger.error(f"خطأ في معالجة تسجيل الدخول: {e}")
            log_security_event('LOGIN_ERROR', f"Login processing error: {e}")
            flash('حدث خطأ في تسجيل الدخول', 'error')
    
    return render_template('login.html', form=form)

@app.route('/logout')
@login_required
def logout():
    """تسجيل الخروج"""
    # تسجيل النشاط قبل تسجيل الخروج
    try:
        activity = ActivityLog(
            user_id=current_user.id,
            action='logout',
            entity_type='user',
            entity_id=current_user.id,
            entity_name=current_user.full_name or current_user.username,
            details='{"logout_time": "' + datetime.utcnow().isoformat() + '"}',
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent', '')[:500]
        )
        db.session.add(activity)
        db.session.commit()
    except Exception as e:
        logger.error(f"خطأ في تسجيل نشاط تسجيل الخروج: {e}")

    logger.info(f"تم تسجيل خروج المستخدم: {current_user.username}")
    logout_user()
    flash('تم تسجيل الخروج بنجاح', 'success')
    return redirect(url_for('login'))

@app.route('/dashboard')
@login_required
def dashboard():
    """ENHANCED: لوحة التحكم المحسنة مع مراقبة الأداء والمستخدمين النشطين"""
    try:
        print("🚀 DASHBOARD: Loading enhanced dashboard...")
        start_time = time.time()

        # CRITICAL: Performance monitoring
        performance_data = {
            'load_start': datetime.now(),
            'queries_count': 0,
            'cache_hits': 0,
            'errors': []
        }

        # إحصائيات محسنة مع استعلامات منفصلة ومراقبة الأداء
        # ENHANCED: Basic statistics with performance monitoring
        stats = {
            'total_documents': Document.query.count(),
            'total_incoming': IncomingDocument.query.count(),
            'total_outgoing': OutgoingDocument.query.count(),
            'total_users': User.query.count(),
        }
        performance_data['queries_count'] += 4

        # CRITICAL: Active Users Statistics
        stats['active_users'] = get_active_users_statistics()

        # ENHANCED: System Health Check
        stats['system_health'] = perform_system_health_check()

        # الوثائق الحديثة مع حقول محددة فقط
        stats['recent_documents'] = Document.query.with_entities(
            Document.id, Document.title, Document.document_type, Document.created_at
        ).order_by(Document.created_at.desc()).limit(5).all()

        stats['recent_incoming'] = IncomingDocument.query.with_entities(
            IncomingDocument.id, IncomingDocument.subject, IncomingDocument.sender_name, IncomingDocument.created_at
        ).order_by(IncomingDocument.created_at.desc()).limit(5).all()

        stats['recent_outgoing'] = OutgoingDocument.query.with_entities(
            OutgoingDocument.id, OutgoingDocument.subject, OutgoingDocument.recipient_name, OutgoingDocument.created_at
        ).order_by(OutgoingDocument.created_at.desc()).limit(5).all()

        performance_data['queries_count'] += 3

        # ENHANCED: Additional statistics for comprehensive dashboard
        try:
            # Document types statistics
            stats['document_types_stats'] = db.session.query(
                Document.document_type,
                db.func.count(Document.id).label('count')
            ).group_by(Document.document_type).limit(10).all()

            # Monthly statistics
            stats['monthly_stats'] = db.session.query(
                db.func.strftime('%Y-%m', Document.created_at).label('month'),
                db.func.count(Document.id).label('count')
            ).group_by(db.func.strftime('%Y-%m', Document.created_at)).order_by('month').limit(6).all()

            performance_data['queries_count'] += 2

        except Exception as stats_error:
            performance_data['errors'].append(f"Additional stats error: {stats_error}")
            stats['document_types_stats'] = []
            stats['monthly_stats'] = []

    except Exception as e:
        logger.error(f"خطأ في تحميل لوحة التحكم: {e}")
        # في حالة الخطأ، استخدم قيم افتراضية
        stats = {
            'total_documents': 0,
            'total_incoming': 0,
            'total_outgoing': 0,
            'recent_documents': [],
            'recent_incoming': [],
            'recent_outgoing': []
        }

    # Calculate final performance metrics
    load_time = time.time() - start_time
    performance_data['load_time'] = round(load_time, 3)
    performance_data['load_end'] = datetime.now()

    print(f"✅ DASHBOARD: Enhanced dashboard loaded in {load_time:.3f}s with {performance_data['queries_count']} queries")

    if performance_data['errors']:
        print(f"⚠️ DASHBOARD: {len(performance_data['errors'])} errors occurred")

    # Add performance data to stats
    stats['performance'] = performance_data

    logger.info(f"تم تحميل لوحة التحكم المحسنة للمستخدم: {current_user.username} في {load_time:.3f} ثانية")

    return render_template('dashboard.html', stats=stats)

def get_active_users_statistics():
    """CRITICAL: Get real-time active users statistics"""
    try:
        print("🔄 DASHBOARD: Getting active users statistics...")

        # Define time thresholds
        now = datetime.utcnow()
        last_hour = now - timedelta(hours=1)
        last_day = now - timedelta(days=1)
        last_week = now - timedelta(weeks=1)

        # Get active users data
        active_data = {
            'online_now': 0,  # Users active in last 5 minutes
            'last_hour': 0,   # Users active in last hour
            'last_day': 0,    # Users active in last day
            'last_week': 0,   # Users active in last week
            'total_sessions': 0,
            'recent_logins': []
        }

        # Count users by activity periods
        try:
            # Users active in last hour
            active_data['last_hour'] = User.query.filter(
                User.last_login >= last_hour
            ).count()

            # Users active in last day
            active_data['last_day'] = User.query.filter(
                User.last_login >= last_day
            ).count()

            # Users active in last week
            active_data['last_week'] = User.query.filter(
                User.last_login >= last_week
            ).count()

            # Recent login activities
            active_data['recent_logins'] = User.query.filter(
                User.last_login >= last_day
            ).order_by(User.last_login.desc()).limit(10).all()

            print(f"✅ DASHBOARD: Active users - Hour: {active_data['last_hour']}, Day: {active_data['last_day']}, Week: {active_data['last_week']}")

        except Exception as e:
            print(f"❌ DASHBOARD: Error getting active users: {e}")

        return active_data

    except Exception as e:
        print(f"❌ DASHBOARD: Critical error in active users statistics: {e}")
        return {
            'online_now': 0,
            'last_hour': 0,
            'last_day': 0,
            'last_week': 0,
            'total_sessions': 0,
            'recent_logins': [],
            'error': str(e)
        }

def perform_system_health_check():
    """CRITICAL: Perform comprehensive system health check"""
    try:
        print("🔄 DASHBOARD: Performing system health check...")

        health_data = {
            'status': 'healthy',
            'checks': {},
            'warnings': [],
            'errors': [],
            'timestamp': datetime.now()
        }

        # Database connectivity check
        try:
            db.session.execute('SELECT 1')
            health_data['checks']['database'] = 'healthy'
        except Exception as e:
            health_data['checks']['database'] = 'error'
            health_data['errors'].append(f"Database error: {e}")
            health_data['status'] = 'critical'

        # File system check
        try:
            upload_dir = app.config.get('UPLOAD_FOLDER', 'uploads')
            if os.path.exists(upload_dir) and os.access(upload_dir, os.W_OK):
                health_data['checks']['file_system'] = 'healthy'
            else:
                health_data['checks']['file_system'] = 'warning'
                health_data['warnings'].append("Upload directory not writable")
        except Exception as e:
            health_data['checks']['file_system'] = 'error'
            health_data['errors'].append(f"File system error: {e}")

        # Memory usage check (basic)
        try:
            import psutil
            memory_percent = psutil.virtual_memory().percent
            if memory_percent > 90:
                health_data['checks']['memory'] = 'critical'
                health_data['errors'].append(f"High memory usage: {memory_percent}%")
                health_data['status'] = 'warning'
            elif memory_percent > 75:
                health_data['checks']['memory'] = 'warning'
                health_data['warnings'].append(f"Memory usage: {memory_percent}%")
            else:
                health_data['checks']['memory'] = 'healthy'
            health_data['memory_usage'] = memory_percent
        except ImportError:
            health_data['checks']['memory'] = 'unknown'
            health_data['warnings'].append("psutil not available for memory monitoring")
        except Exception as e:
            health_data['checks']['memory'] = 'error'
            health_data['errors'].append(f"Memory check error: {e}")

        # Set overall status
        if health_data['errors']:
            health_data['status'] = 'critical'
        elif health_data['warnings']:
            health_data['status'] = 'warning'

        print(f"✅ DASHBOARD: System health check completed - Status: {health_data['status']}")

        return health_data

    except Exception as e:
        print(f"❌ DASHBOARD: Critical error in system health check: {e}")
        return {
            'status': 'error',
            'checks': {},
            'warnings': [],
            'errors': [f"Health check failed: {e}"],
            'timestamp': datetime.now()
        }

def get_report_templates():
    """CRITICAL: Get available report templates for dynamic report creation"""
    try:
        print("🔄 REPORTS: Loading report templates...")

        templates = [
            {
                'id': 'documents_summary',
                'name': 'تقرير ملخص الوثائق',
                'name_en': 'Documents Summary Report',
                'description': 'تقرير شامل عن جميع الوثائق في النظام',
                'description_en': 'Comprehensive report of all documents in the system',
                'category': 'documents',
                'formats': ['browser', 'csv', 'excel'],
                'parameters': ['date_range', 'document_type', 'status'],
                'auto_generate': True
            },
            {
                'id': 'activity_report',
                'name': 'تقرير الأنشطة',
                'name_en': 'Activity Report',
                'description': 'تقرير مفصل عن أنشطة المستخدمين',
                'description_en': 'Detailed report of user activities',
                'category': 'activity',
                'formats': ['browser', 'csv', 'excel'],
                'parameters': ['date_range', 'user', 'activity_type'],
                'auto_generate': True
            },
            {
                'id': 'incoming_documents',
                'name': 'تقرير الوثائق الواردة',
                'name_en': 'Incoming Documents Report',
                'description': 'تقرير شامل عن الوثائق الواردة',
                'description_en': 'Comprehensive report of incoming documents',
                'category': 'incoming',
                'formats': ['browser', 'csv', 'excel'],
                'parameters': ['date_range', 'sender', 'priority'],
                'auto_generate': True
            },
            {
                'id': 'outgoing_documents',
                'name': 'تقرير الوثائق الصادرة',
                'name_en': 'Outgoing Documents Report',
                'description': 'تقرير شامل عن الوثائق الصادرة',
                'description_en': 'Comprehensive report of outgoing documents',
                'category': 'outgoing',
                'formats': ['browser', 'csv', 'excel'],
                'parameters': ['date_range', 'recipient', 'priority'],
                'auto_generate': True
            },
            {
                'id': 'users_report',
                'name': 'تقرير المستخدمين',
                'name_en': 'Users Report',
                'description': 'تقرير عن المستخدمين وأنشطتهم',
                'description_en': 'Report on users and their activities',
                'category': 'users',
                'formats': ['browser', 'csv', 'excel'],
                'parameters': ['date_range', 'department', 'role'],
                'auto_generate': False
            },
            {
                'id': 'monthly_statistics',
                'name': 'الإحصائيات الشهرية',
                'name_en': 'Monthly Statistics',
                'description': 'إحصائيات شهرية شاملة للنظام',
                'description_en': 'Comprehensive monthly system statistics',
                'category': 'statistics',
                'formats': ['browser', 'excel'],
                'parameters': ['month', 'year'],
                'auto_generate': True
            },
            {
                'id': 'document_types_analysis',
                'name': 'تحليل أنواع الوثائق',
                'name_en': 'Document Types Analysis',
                'description': 'تحليل مفصل لأنواع الوثائق واستخدامها',
                'description_en': 'Detailed analysis of document types and usage',
                'category': 'analysis',
                'formats': ['browser', 'excel'],
                'parameters': ['date_range'],
                'auto_generate': True
            }
        ]

        print(f"✅ REPORTS: Loaded {len(templates)} report templates")
        return templates

    except Exception as e:
        print(f"❌ REPORTS: Error loading report templates: {e}")
        return []

def get_recent_reports():
    """Get recent report generation history"""
    try:
        print("🔄 REPORTS: Getting recent reports...")

        # This would typically come from a reports history table
        # For now, return mock data
        recent = [
            {
                'id': 1,
                'name': 'تقرير الوثائق الشهري',
                'type': 'documents_summary',
                'generated_at': datetime.now() - timedelta(hours=2),
                'generated_by': current_user.username,
                'format': 'excel',
                'status': 'completed',
                'file_size': '2.5 MB'
            },
            {
                'id': 2,
                'name': 'تقرير الأنشطة اليومي',
                'type': 'activity_report',
                'generated_at': datetime.now() - timedelta(hours=6),
                'generated_by': current_user.username,
                'format': 'csv',
                'status': 'completed',
                'file_size': '1.2 MB'
            }
        ]

        print(f"✅ REPORTS: Found {len(recent)} recent reports")
        return recent

    except Exception as e:
        print(f"❌ REPORTS: Error getting recent reports: {e}")
        return []

def get_report_statistics():
    """Get report generation statistics"""
    try:
        print("🔄 REPORTS: Getting report statistics...")

        stats = {
            'total_generated': 156,  # This would come from database
            'this_month': 23,
            'this_week': 8,
            'today': 2,
            'most_popular': 'documents_summary',
            'formats_usage': {
                'excel': 45,
                'csv': 30,
                'browser': 25
            },
            'avg_generation_time': 2.3  # seconds
        }

        print(f"✅ REPORTS: Statistics loaded - {stats['total_generated']} total reports")
        return stats

    except Exception as e:
        print(f"❌ REPORTS: Error getting report statistics: {e}")
        return {}

def get_activity_based_reports():
    """CRITICAL: Get activity-based report suggestions"""
    try:
        print("🔄 REPORTS: Getting activity-based reports...")

        # Analyze recent system activity to suggest relevant reports
        suggestions = []

        # Check for high document activity
        recent_docs = Document.query.filter(
            Document.created_at >= datetime.now() - timedelta(days=7)
        ).count()

        if recent_docs > 10:
            suggestions.append({
                'type': 'documents_summary',
                'reason': f'تم إنشاء {recent_docs} وثيقة في الأسبوع الماضي',
                'reason_en': f'{recent_docs} documents created in the last week',
                'priority': 'high',
                'auto_generate': True
            })

        # Check for high incoming activity
        recent_incoming = IncomingDocument.query.filter(
            IncomingDocument.created_at >= datetime.now() - timedelta(days=7)
        ).count()

        if recent_incoming > 5:
            suggestions.append({
                'type': 'incoming_documents',
                'reason': f'تم استلام {recent_incoming} وثيقة واردة في الأسبوع الماضي',
                'reason_en': f'{recent_incoming} incoming documents received in the last week',
                'priority': 'medium',
                'auto_generate': True
            })

        print(f"✅ REPORTS: Generated {len(suggestions)} activity-based suggestions")
        return suggestions

    except Exception as e:
        print(f"❌ REPORTS: Error getting activity-based reports: {e}")
        return []

def generate_documents_report(start_date, end_date, export_format):
    """CRITICAL: Generate comprehensive documents report with multiple export formats"""
    try:
        print(f"🔄 REPORTS: Generating documents report ({export_format})...")

        # Query documents with filters
        query = Document.query

        if start_date:
            query = query.filter(Document.created_at >= start_date)
        if end_date:
            query = query.filter(Document.created_at <= end_date)

        documents = query.order_by(Document.created_at.desc()).all()

        report_data = {
            'title': 'تقرير الوثائق الشامل',
            'title_en': 'Comprehensive Documents Report',
            'generated_at': datetime.now(),
            'date_range': f"{start_date} إلى {end_date}" if start_date and end_date else "جميع التواريخ",
            'total_count': len(documents),
            'documents': documents,
            'statistics': {
                'by_type': {},
                'by_status': {},
                'by_month': {}
            }
        }

        # Calculate statistics
        for doc in documents:
            # By type
            doc_type = doc.document_type or 'غير محدد'
            report_data['statistics']['by_type'][doc_type] = report_data['statistics']['by_type'].get(doc_type, 0) + 1

            # By status
            status = doc.status or 'غير محدد'
            report_data['statistics']['by_status'][status] = report_data['statistics']['by_status'].get(status, 0) + 1

            # By month
            month_key = doc.created_at.strftime('%Y-%m')
            report_data['statistics']['by_month'][month_key] = report_data['statistics']['by_month'].get(month_key, 0) + 1

        print(f"✅ REPORTS: Documents report generated - {len(documents)} documents")

        # Export based on format
        if export_format == 'csv':
            return export_documents_csv(report_data)
        elif export_format == 'excel':
            return export_documents_excel(report_data)
        else:  # browser
            return render_template('reports/documents_report.html', report_data=report_data)

    except Exception as e:
        print(f"❌ REPORTS: Error generating documents report: {e}")
        flash('حدث خطأ في إنشاء تقرير الوثائق', 'error')
        return redirect(url_for('reports'))

def export_documents_csv(report_data):
    """Export documents report as CSV"""
    try:
        import csv
        import io

        output = io.StringIO()
        writer = csv.writer(output)

        # Write headers
        writer.writerow(['ID', 'العنوان', 'النوع', 'الحالة', 'تاريخ الإنشاء', 'المنشئ'])

        # Write data
        for doc in report_data['documents']:
            writer.writerow([
                doc.id,
                doc.title,
                doc.document_type,
                doc.status,
                doc.created_at.strftime('%Y-%m-%d %H:%M'),
                doc.creator.username if doc.creator else 'غير محدد'
            ])

        output.seek(0)

        return Response(
            output.getvalue(),
            mimetype='text/csv',
            headers={'Content-Disposition': f'attachment; filename=documents_report_{datetime.now().strftime("%Y%m%d")}.csv'}
        )

    except Exception as e:
        print(f"❌ REPORTS: Error exporting CSV: {e}")
        flash('حدث خطأ في تصدير التقرير', 'error')
        return redirect(url_for('reports'))

def export_documents_excel(report_data):
    """Export documents report as Excel"""
    try:
        import pandas as pd
        import io

        # Prepare data for Excel
        data = []
        for doc in report_data['documents']:
            data.append({
                'ID': doc.id,
                'العنوان': doc.title,
                'النوع': doc.document_type,
                'الحالة': doc.status,
                'تاريخ الإنشاء': doc.created_at.strftime('%Y-%m-%d %H:%M'),
                'المنشئ': doc.creator.username if doc.creator else 'غير محدد',
                'الوصف': doc.description or '',
                'الكلمات المفتاحية': doc.tags or ''
            })

        df = pd.DataFrame(data)

        # Create Excel file in memory
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='الوثائق', index=False)

            # Add statistics sheet
            stats_data = []
            for doc_type, count in report_data['statistics']['by_type'].items():
                stats_data.append({'النوع': doc_type, 'العدد': count})

            stats_df = pd.DataFrame(stats_data)
            stats_df.to_excel(writer, sheet_name='إحصائيات الأنواع', index=False)

        output.seek(0)

        return Response(
            output.getvalue(),
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            headers={'Content-Disposition': f'attachment; filename=documents_report_{datetime.now().strftime("%Y%m%d")}.xlsx'}
        )

    except Exception as e:
        print(f"❌ REPORTS: Error exporting Excel: {e}")
        flash('حدث خطأ في تصدير التقرير كملف Excel', 'error')
        return redirect(url_for('reports'))

# CALENDAR & NOTIFICATIONS MODULE (High Priority)
@app.route('/calendar')
@login_required
def calendar():
    """ENHANCED: نظام التقويم المتكامل مع الإشعارات"""
    try:
        print("🚀 CALENDAR: Loading integrated calendar system...")

        # Get calendar events
        calendar_events = get_calendar_events()

        # Get upcoming deadlines
        upcoming_deadlines = get_upcoming_deadlines()

        # Get notification settings
        notification_settings = get_user_notification_settings()

        # Get document expiration alerts
        expiration_alerts = get_document_expiration_alerts()

        return render_template('calendar/index.html',
                             calendar_events=calendar_events,
                             upcoming_deadlines=upcoming_deadlines,
                             notification_settings=notification_settings,
                             expiration_alerts=expiration_alerts)

    except Exception as e:
        logger.error(f"خطأ في تحميل التقويم: {e}")
        flash('حدث خطأ في تحميل التقويم', 'error')
        return render_template('calendar/index.html',
                             calendar_events=[],
                             upcoming_deadlines=[],
                             notification_settings={},
                             expiration_alerts=[])

@app.route('/notifications')
@login_required
def notifications():
    """ENHANCED: نظام الإشعارات الشامل"""
    try:
        print("🚀 NOTIFICATIONS: Loading comprehensive notification system...")

        # Get user notifications
        user_notifications = get_user_notifications()

        # Get system alerts
        system_alerts = get_system_alerts()

        # Get notification statistics
        notification_stats = get_notification_statistics()

        return render_template('notifications/index.html',
                             user_notifications=user_notifications,
                             system_alerts=system_alerts,
                             notification_stats=notification_stats)

    except Exception as e:
        logger.error(f"خطأ في تحميل الإشعارات: {e}")
        flash('حدث خطأ في تحميل الإشعارات', 'error')
        return render_template('notifications/index.html',
                             user_notifications=[],
                             system_alerts=[],
                             notification_stats={})

def get_calendar_events():
    """CRITICAL: Get calendar events with document integration"""
    try:
        print("🔄 CALENDAR: Getting calendar events...")

        events = []

        # Document creation events
        recent_docs = Document.query.filter(
            Document.created_at >= datetime.now() - timedelta(days=30)
        ).order_by(Document.created_at.desc()).limit(50).all()

        for doc in recent_docs:
            events.append({
                'id': f'doc_{doc.id}',
                'title': f'إنشاء وثيقة: {doc.title}',
                'title_en': f'Document Created: {doc.title}',
                'start': doc.created_at.isoformat(),
                'type': 'document_created',
                'color': '#28a745',
                'description': f'تم إنشاء الوثيقة: {doc.title}',
                'url': url_for('view_document', id=doc.id)
            })

        # Incoming document events
        recent_incoming = IncomingDocument.query.filter(
            IncomingDocument.created_at >= datetime.now() - timedelta(days=30)
        ).order_by(IncomingDocument.created_at.desc()).limit(30).all()

        for inc_doc in recent_incoming:
            events.append({
                'id': f'inc_{inc_doc.id}',
                'title': f'وثيقة واردة: {inc_doc.subject}',
                'title_en': f'Incoming: {inc_doc.subject}',
                'start': inc_doc.created_at.isoformat(),
                'type': 'incoming_document',
                'color': '#007bff',
                'description': f'وثيقة واردة من: {inc_doc.sender_name}',
                'url': url_for('view_incoming_document', id=inc_doc.id)
            })

        # Add scheduled events (future feature)
        # This would come from a calendar_events table

        print(f"✅ CALENDAR: Loaded {len(events)} calendar events")
        return events

    except Exception as e:
        print(f"❌ CALENDAR: Error getting calendar events: {e}")
        return []

def get_upcoming_deadlines():
    """CRITICAL: Get upcoming deadlines and expiration dates"""
    try:
        print("🔄 CALENDAR: Getting upcoming deadlines...")

        deadlines = []
        now = datetime.now()

        # Check for documents that might have expiration dates
        # This would typically come from document metadata or a separate deadlines table

        # For now, create sample deadlines based on document creation dates
        old_docs = Document.query.filter(
            Document.created_at <= now - timedelta(days=90)
        ).order_by(Document.created_at.asc()).limit(10).all()

        for doc in old_docs:
            # Simulate review deadline (6 months after creation)
            review_date = doc.created_at + timedelta(days=180)
            days_until = (review_date - now).days

            if days_until <= 30 and days_until >= 0:  # Within 30 days
                priority = 'high' if days_until <= 7 else 'medium' if days_until <= 14 else 'low'

                deadlines.append({
                    'id': f'review_{doc.id}',
                    'title': f'مراجعة الوثيقة: {doc.title}',
                    'title_en': f'Document Review: {doc.title}',
                    'due_date': review_date,
                    'days_until': days_until,
                    'priority': priority,
                    'type': 'document_review',
                    'document_id': doc.id,
                    'description': f'موعد مراجعة الوثيقة: {doc.title}'
                })

        # Sort by urgency
        deadlines.sort(key=lambda x: x['days_until'])

        print(f"✅ CALENDAR: Found {len(deadlines)} upcoming deadlines")
        return deadlines

    except Exception as e:
        print(f"❌ CALENDAR: Error getting deadlines: {e}")
        return []

def get_document_expiration_alerts():
    """CRITICAL: Get document expiration alerts"""
    try:
        print("🔄 NOTIFICATIONS: Getting document expiration alerts...")

        alerts = []
        now = datetime.now()

        # Check for documents that haven't been accessed recently
        stale_docs = Document.query.filter(
            Document.created_at <= now - timedelta(days=365)  # 1 year old
        ).order_by(Document.created_at.asc()).limit(20).all()

        for doc in stale_docs:
            age_days = (now - doc.created_at).days

            alerts.append({
                'id': f'expire_{doc.id}',
                'title': f'وثيقة قديمة: {doc.title}',
                'title_en': f'Old Document: {doc.title}',
                'message': f'الوثيقة لم يتم الوصول إليها منذ {age_days} يوم',
                'message_en': f'Document not accessed for {age_days} days',
                'type': 'expiration',
                'priority': 'high' if age_days > 730 else 'medium',  # 2 years = high priority
                'document_id': doc.id,
                'created_at': now,
                'age_days': age_days
            })

        print(f"✅ NOTIFICATIONS: Found {len(alerts)} expiration alerts")
        return alerts

    except Exception as e:
        print(f"❌ NOTIFICATIONS: Error getting expiration alerts: {e}")
        return []

def get_user_notifications():
    """Get user-specific notifications"""
    try:
        print("🔄 NOTIFICATIONS: Getting user notifications...")

        notifications = []

        # Recent activity notifications
        recent_activities = ActivityLog.query.filter(
            ActivityLog.user_id == current_user.id,
            ActivityLog.created_at >= datetime.now() - timedelta(days=7)
        ).order_by(ActivityLog.created_at.desc()).limit(20).all()

        for activity in recent_activities:
            notifications.append({
                'id': f'activity_{activity.id}',
                'title': f'نشاط: {activity.action}',
                'title_en': f'Activity: {activity.action}',
                'message': activity.description,
                'type': 'activity',
                'priority': 'low',
                'created_at': activity.timestamp,
                'read': False  # This would come from a notifications table
            })

        # Document-related notifications
        user_docs = Document.query.filter(
            Document.created_by == current_user.id,
            Document.created_at >= datetime.now() - timedelta(days=30)
        ).count()

        if user_docs > 0:
            notifications.append({
                'id': 'user_docs_summary',
                'title': f'ملخص الوثائق',
                'title_en': 'Documents Summary',
                'message': f'لديك {user_docs} وثيقة تم إنشاؤها في الشهر الماضي',
                'message_en': f'You have {user_docs} documents created in the last month',
                'type': 'summary',
                'priority': 'medium',
                'created_at': datetime.now(),
                'read': False
            })

        print(f"✅ NOTIFICATIONS: Loaded {len(notifications)} user notifications")
        return notifications

    except Exception as e:
        print(f"❌ NOTIFICATIONS: Error getting user notifications: {e}")
        return []

def get_system_alerts():
    """Get system-wide alerts"""
    try:
        print("🔄 NOTIFICATIONS: Getting system alerts...")

        alerts = []

        # System health alerts
        health_data = perform_system_health_check()

        if health_data['status'] == 'critical':
            alerts.append({
                'id': 'system_health_critical',
                'title': 'تحذير النظام',
                'title_en': 'System Warning',
                'message': 'النظام يواجه مشاكل حرجة تحتاج لتدخل فوري',
                'message_en': 'System is experiencing critical issues requiring immediate attention',
                'type': 'system_health',
                'priority': 'critical',
                'created_at': datetime.now()
            })
        elif health_data['status'] == 'warning':
            alerts.append({
                'id': 'system_health_warning',
                'title': 'تنبيه النظام',
                'title_en': 'System Alert',
                'message': 'النظام يواجه بعض التحذيرات',
                'message_en': 'System has some warnings',
                'type': 'system_health',
                'priority': 'medium',
                'created_at': datetime.now()
            })

        # Storage alerts
        try:
            import shutil
            upload_folder = app.config.get('UPLOAD_FOLDER', 'uploads')
            if os.path.exists(upload_folder):
                total, used, free = shutil.disk_usage(upload_folder)
                free_percent = (free / total) * 100

                if free_percent < 10:  # Less than 10% free space
                    alerts.append({
                        'id': 'storage_low',
                        'title': 'تحذير مساحة التخزين',
                        'title_en': 'Storage Warning',
                        'message': f'مساحة التخزين منخفضة: {free_percent:.1f}% متبقية',
                        'message_en': f'Low storage space: {free_percent:.1f}% remaining',
                        'type': 'storage',
                        'priority': 'high',
                        'created_at': datetime.now()
                    })
        except Exception:
            pass

        print(f"✅ NOTIFICATIONS: Generated {len(alerts)} system alerts")
        return alerts

    except Exception as e:
        print(f"❌ NOTIFICATIONS: Error getting system alerts: {e}")
        return []

def get_user_notification_settings():
    """Get user notification preferences"""
    try:
        # This would typically come from a user_settings table
        # For now, return default settings
        settings = {
            'email_notifications': True,
            'browser_notifications': True,
            'document_expiration_alerts': True,
            'deadline_reminders': True,
            'activity_summaries': True,
            'system_alerts': True,
            'notification_frequency': 'daily',  # daily, weekly, immediate
            'languages': ['ar', 'en']
        }

        return settings

    except Exception as e:
        print(f"❌ NOTIFICATIONS: Error getting notification settings: {e}")
        return {}

def get_notification_statistics():
    """Get notification statistics"""
    try:
        stats = {
            'total_sent': 245,  # This would come from notifications log
            'this_week': 18,
            'unread_count': 5,
            'types': {
                'document_alerts': 12,
                'system_notifications': 6,
                'deadline_reminders': 8,
                'activity_summaries': 4
            },
            'delivery_rate': 98.5  # Percentage of successfully delivered notifications
        }

        return stats

    except Exception as e:
        print(f"❌ NOTIFICATIONS: Error getting notification statistics: {e}")
        return {}

@app.route('/api/notifications/mark-read/<notification_id>', methods=['POST'])
@login_required
def mark_notification_read(notification_id):
    """CRITICAL: Mark notification as read"""
    try:
        # This would update the notification status in database
        # For now, return success
        return jsonify({
            'success': True,
            'message': 'تم تحديد الإشعار كمقروء',
            'message_en': 'Notification marked as read'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/calendar/events')
@login_required
def api_calendar_events():
    """CRITICAL: API endpoint for calendar events (for FullCalendar.js)"""
    try:
        events = get_calendar_events()

        # Format events for FullCalendar
        formatted_events = []
        for event in events:
            formatted_events.append({
                'id': event['id'],
                'title': event['title'],
                'start': event['start'],
                'backgroundColor': event['color'],
                'borderColor': event['color'],
                'url': event.get('url', ''),
                'extendedProps': {
                    'description': event.get('description', ''),
                    'type': event['type']
                }
            })

        return jsonify(formatted_events)

    except Exception as e:
        logger.error(f"خطأ في API التقويم: {e}")
        return jsonify([]), 500

# DOCUMENT LIFECYCLE MANAGEMENT MODULE (Medium Priority)
@app.route('/documents/lifecycle')
@login_required
def document_lifecycle():
    """ENHANCED: إدارة دورة حياة الوثائق الشاملة"""
    if not current_user.is_admin():
        flash('ليس لديك صلاحية للوصول إلى إدارة دورة حياة الوثائق', 'error')
        return redirect(url_for('dashboard'))

    try:
        print("🚀 LIFECYCLE: Loading document lifecycle management...")

        # Get lifecycle statistics
        lifecycle_stats = get_document_lifecycle_statistics()

        # Get documents by lifecycle stage
        lifecycle_stages = get_documents_by_lifecycle_stage()

        # Get retention policies
        retention_policies = get_retention_policies()

        # Get archived documents
        archived_documents = get_archived_documents()

        return render_template('documents/lifecycle.html',
                             lifecycle_stats=lifecycle_stats,
                             lifecycle_stages=lifecycle_stages,
                             retention_policies=retention_policies,
                             archived_documents=archived_documents)

    except Exception as e:
        logger.error(f"خطأ في تحميل إدارة دورة حياة الوثائق: {e}")
        flash('حدث خطأ في تحميل إدارة دورة حياة الوثائق', 'error')
        return render_template('documents/lifecycle.html',
                             lifecycle_stats={},
                             lifecycle_stages={},
                             retention_policies=[],
                             archived_documents=[])

@app.route('/documents/<int:id>/lifecycle', methods=['GET', 'POST'])
@login_required
def manage_document_lifecycle(id):
    """CRITICAL: إدارة دورة حياة وثيقة محددة"""
    document = Document.query.get_or_404(id)

    if not current_user.is_admin() and document.created_by != current_user.id:
        flash('ليس لديك صلاحية لإدارة دورة حياة هذه الوثيقة', 'error')
        return redirect(url_for('view_document', id=id))

    if request.method == 'POST':
        try:
            action = request.form.get('action')
            print(f"🔄 LIFECYCLE: Processing action '{action}' for document {id}")

            if action == 'archive':
                return archive_document_manually(document)
            elif action == 'restore':
                return restore_document_from_archive(document)
            elif action == 'set_retention':
                return set_document_retention_policy(document)
            elif action == 'extend_lifecycle':
                return extend_document_lifecycle(document)
            elif action == 'mark_for_review':
                return mark_document_for_review(document)
            else:
                flash('إجراء غير صالح', 'error')

        except Exception as e:
            logger.error(f"خطأ في إدارة دورة حياة الوثيقة: {e}")
            flash('حدث خطأ في إدارة دورة حياة الوثيقة', 'error')

    # Get document lifecycle information
    lifecycle_info = get_document_lifecycle_info(document)

    return render_template('documents/lifecycle_manage.html',
                         document=document,
                         lifecycle_info=lifecycle_info)

def get_document_lifecycle_statistics():
    """CRITICAL: Get comprehensive document lifecycle statistics"""
    try:
        print("🔄 LIFECYCLE: Getting lifecycle statistics...")

        now = datetime.now()

        stats = {
            'total_documents': Document.query.count(),
            'active_documents': Document.query.filter(Document.status == 'نشط').count(),
            'archived_documents': Document.query.filter(Document.status == 'مؤرشف').count(),
            'draft_documents': Document.query.filter(Document.status == 'مسودة').count(),
            'under_review': Document.query.filter(Document.status == 'قيد المراجعة').count(),
            'expired_documents': 0,  # Will be calculated
            'documents_by_age': {
                'new': 0,      # < 30 days
                'recent': 0,   # 30-90 days
                'mature': 0,   # 90-365 days
                'old': 0,      # 1-2 years
                'very_old': 0  # > 2 years
            },
            'retention_compliance': {
                'compliant': 0,
                'non_compliant': 0,
                'needs_review': 0
            }
        }

        # Calculate documents by age
        all_docs = Document.query.all()
        for doc in all_docs:
            age_days = (now - doc.created_at).days

            if age_days < 30:
                stats['documents_by_age']['new'] += 1
            elif age_days < 90:
                stats['documents_by_age']['recent'] += 1
            elif age_days < 365:
                stats['documents_by_age']['mature'] += 1
            elif age_days < 730:  # 2 years
                stats['documents_by_age']['old'] += 1
            else:
                stats['documents_by_age']['very_old'] += 1

            # Check if document might be expired (simple logic)
            if age_days > 1095:  # 3 years
                stats['expired_documents'] += 1

        # Calculate retention compliance (simplified)
        stats['retention_compliance']['compliant'] = stats['active_documents'] + stats['archived_documents']
        stats['retention_compliance']['needs_review'] = stats['documents_by_age']['very_old']

        print(f"✅ LIFECYCLE: Statistics calculated - {stats['total_documents']} total documents")
        return stats

    except Exception as e:
        print(f"❌ LIFECYCLE: Error getting statistics: {e}")
        return {}

def get_documents_by_lifecycle_stage():
    """Get documents organized by lifecycle stage"""
    try:
        print("🔄 LIFECYCLE: Getting documents by lifecycle stage...")

        stages = {
            'creation': {
                'name': 'مرحلة الإنشاء',
                'name_en': 'Creation Stage',
                'documents': Document.query.filter(Document.status == 'مسودة').limit(10).all(),
                'count': Document.query.filter(Document.status == 'مسودة').count()
            },
            'active': {
                'name': 'مرحلة النشاط',
                'name_en': 'Active Stage',
                'documents': Document.query.filter(Document.status == 'نشط').limit(10).all(),
                'count': Document.query.filter(Document.status == 'نشط').count()
            },
            'review': {
                'name': 'مرحلة المراجعة',
                'name_en': 'Review Stage',
                'documents': Document.query.filter(Document.status == 'قيد المراجعة').limit(10).all(),
                'count': Document.query.filter(Document.status == 'قيد المراجعة').count()
            },
            'archive': {
                'name': 'مرحلة الأرشفة',
                'name_en': 'Archive Stage',
                'documents': Document.query.filter(Document.status == 'مؤرشف').limit(10).all(),
                'count': Document.query.filter(Document.status == 'مؤرشف').count()
            },
            'disposal': {
                'name': 'مرحلة التخلص',
                'name_en': 'Disposal Stage',
                'documents': Document.query.filter(Document.status == 'محذوف').limit(10).all(),
                'count': Document.query.filter(Document.status == 'محذوف').count()
            }
        }

        total_staged = sum(stage['count'] for stage in stages.values())
        print(f"✅ LIFECYCLE: Organized {total_staged} documents by lifecycle stages")
        return stages

    except Exception as e:
        print(f"❌ LIFECYCLE: Error getting documents by stage: {e}")
        return {}

def get_retention_policies():
    """CRITICAL: Get document retention policies"""
    try:
        print("🔄 LIFECYCLE: Getting retention policies...")

        # This would typically come from a retention_policies table
        # For now, return predefined policies
        policies = [
            {
                'id': 1,
                'name': 'سياسة الوثائق الإدارية',
                'name_en': 'Administrative Documents Policy',
                'document_types': ['وثائق إدارية', 'مذكرات', 'تعاميم'],
                'retention_period': 1095,  # 3 years in days
                'retention_period_text': '3 سنوات',
                'auto_archive': True,
                'auto_delete': False,
                'review_required': True,
                'created_at': datetime.now() - timedelta(days=30),
                'status': 'active'
            },
            {
                'id': 2,
                'name': 'سياسة الوثائق المالية',
                'name_en': 'Financial Documents Policy',
                'document_types': ['وثائق مالية', 'فواتير', 'ميزانيات'],
                'retention_period': 2555,  # 7 years in days
                'retention_period_text': '7 سنوات',
                'auto_archive': True,
                'auto_delete': False,
                'review_required': True,
                'created_at': datetime.now() - timedelta(days=60),
                'status': 'active'
            },
            {
                'id': 3,
                'name': 'سياسة الوثائق القانونية',
                'name_en': 'Legal Documents Policy',
                'document_types': ['وثائق قانونية', 'عقود', 'اتفاقيات'],
                'retention_period': 3650,  # 10 years in days
                'retention_period_text': '10 سنوات',
                'auto_archive': True,
                'auto_delete': False,
                'review_required': True,
                'created_at': datetime.now() - timedelta(days=90),
                'status': 'active'
            },
            {
                'id': 4,
                'name': 'سياسة المراسلات',
                'name_en': 'Correspondence Policy',
                'document_types': ['مراسلات', 'خطابات', 'برقيات'],
                'retention_period': 730,  # 2 years in days
                'retention_period_text': '2 سنة',
                'auto_archive': True,
                'auto_delete': True,
                'review_required': False,
                'created_at': datetime.now() - timedelta(days=15),
                'status': 'active'
            }
        ]

        print(f"✅ LIFECYCLE: Loaded {len(policies)} retention policies")
        return policies

    except Exception as e:
        print(f"❌ LIFECYCLE: Error getting retention policies: {e}")
        return []

def get_archived_documents():
    """Get archived documents with enhanced information"""
    try:
        print("🔄 LIFECYCLE: Getting archived documents...")

        archived_docs = Document.query.filter(
            Document.status == 'مؤرشف'
        ).order_by(Document.updated_at.desc()).limit(50).all()

        # Enhance with additional information
        enhanced_docs = []
        for doc in archived_docs:
            archive_info = {
                'document': doc,
                'archive_date': doc.updated_at,
                'archive_reason': 'أرشفة تلقائية',  # This would come from archive log
                'retention_expires': doc.created_at + timedelta(days=1095),  # Default 3 years
                'can_restore': True,
                'archive_size': doc.file_size if doc.file_size else 0,
                'days_archived': (datetime.now() - doc.updated_at).days if doc.updated_at else 0
            }
            enhanced_docs.append(archive_info)

        print(f"✅ LIFECYCLE: Found {len(enhanced_docs)} archived documents")
        return enhanced_docs

    except Exception as e:
        print(f"❌ LIFECYCLE: Error getting archived documents: {e}")
        return []

def archive_document_manually(document):
    """CRITICAL: Archive document manually with user control"""
    try:
        print(f"🔄 LIFECYCLE: Manually archiving document {document.id}")

        # Update document status
        document.status = 'مؤرشف'
        document.updated_at = datetime.utcnow()

        # Log the archive action
        log_activity('archive', 'document', document.id, document.title,
                    f'أرشفة يدوية للوثيقة: {document.title}')

        db.session.commit()

        print(f"✅ LIFECYCLE: Document {document.id} archived successfully")
        flash('تم أرشفة الوثيقة بنجاح', 'success')

        return redirect(url_for('view_document', id=document.id))

    except Exception as e:
        db.session.rollback()
        print(f"❌ LIFECYCLE: Error archiving document: {e}")
        flash('حدث خطأ في أرشفة الوثيقة', 'error')
        return redirect(url_for('view_document', id=document.id))

def restore_document_from_archive(document):
    """CRITICAL: Restore document from archive"""
    try:
        print(f"🔄 LIFECYCLE: Restoring document {document.id} from archive")

        # Update document status
        document.status = 'نشط'
        document.updated_at = datetime.utcnow()

        # Log the restore action
        log_activity('restore', 'document', document.id, document.title,
                    f'استعادة الوثيقة من الأرشيف: {document.title}')

        db.session.commit()

        print(f"✅ LIFECYCLE: Document {document.id} restored successfully")
        flash('تم استعادة الوثيقة من الأرشيف بنجاح', 'success')

        return redirect(url_for('view_document', id=document.id))

    except Exception as e:
        db.session.rollback()
        print(f"❌ LIFECYCLE: Error restoring document: {e}")
        flash('حدث خطأ في استعادة الوثيقة', 'error')
        return redirect(url_for('view_document', id=document.id))

def set_document_retention_policy(document):
    """Set retention policy for document"""
    try:
        retention_period = request.form.get('retention_period', type=int)
        retention_reason = request.form.get('retention_reason', '')

        print(f"🔄 LIFECYCLE: Setting retention policy for document {document.id}")

        # This would typically update a document_retention table
        # For now, we'll add it to the document's tags
        retention_tag = f"retention:{retention_period}days"

        if document.tags:
            # Remove existing retention tags
            tags = [tag.strip() for tag in document.tags.split(',') if not tag.strip().startswith('retention:')]
            tags.append(retention_tag)
            document.tags = ', '.join(tags)
        else:
            document.tags = retention_tag

        document.updated_at = datetime.utcnow()

        # Log the action
        log_activity('retention_set', 'document', document.id, document.title,
                    f'تحديد سياسة الاحتفاظ: {retention_period} يوم - {retention_reason}')

        db.session.commit()

        print(f"✅ LIFECYCLE: Retention policy set for document {document.id}")
        flash(f'تم تحديد سياسة الاحتفاظ: {retention_period} يوم', 'success')

        return redirect(url_for('view_document', id=document.id))

    except Exception as e:
        db.session.rollback()
        print(f"❌ LIFECYCLE: Error setting retention policy: {e}")
        flash('حدث خطأ في تحديد سياسة الاحتفاظ', 'error')
        return redirect(url_for('view_document', id=document.id))

def extend_document_lifecycle(document):
    """Extend document lifecycle"""
    try:
        extension_period = request.form.get('extension_period', type=int)
        extension_reason = request.form.get('extension_reason', '')

        print(f"🔄 LIFECYCLE: Extending lifecycle for document {document.id}")

        # Add extension tag
        extension_tag = f"extended:{extension_period}days"

        if document.tags:
            tags = [tag.strip() for tag in document.tags.split(',')]
            tags.append(extension_tag)
            document.tags = ', '.join(tags)
        else:
            document.tags = extension_tag

        document.updated_at = datetime.utcnow()

        # Log the action
        log_activity('lifecycle_extend', 'document', document.id, document.title,
                    f'تمديد دورة الحياة: {extension_period} يوم - {extension_reason}')

        db.session.commit()

        print(f"✅ LIFECYCLE: Lifecycle extended for document {document.id}")
        flash(f'تم تمديد دورة حياة الوثيقة: {extension_period} يوم', 'success')

        return redirect(url_for('view_document', id=document.id))

    except Exception as e:
        db.session.rollback()
        print(f"❌ LIFECYCLE: Error extending lifecycle: {e}")
        flash('حدث خطأ في تمديد دورة حياة الوثيقة', 'error')
        return redirect(url_for('view_document', id=document.id))

def mark_document_for_review(document):
    """Mark document for review"""
    try:
        review_reason = request.form.get('review_reason', '')
        review_date = request.form.get('review_date')

        print(f"🔄 LIFECYCLE: Marking document {document.id} for review")

        # Update document status
        document.status = 'قيد المراجعة'
        document.updated_at = datetime.utcnow()

        # Add review tag
        review_tag = f"review_scheduled:{review_date}" if review_date else "review_scheduled"

        if document.tags:
            tags = [tag.strip() for tag in document.tags.split(',')]
            tags.append(review_tag)
            document.tags = ', '.join(tags)
        else:
            document.tags = review_tag

        # Log the action
        log_activity('mark_review', 'document', document.id, document.title,
                    f'تحديد للمراجعة - {review_reason}')

        db.session.commit()

        print(f"✅ LIFECYCLE: Document {document.id} marked for review")
        flash('تم تحديد الوثيقة للمراجعة', 'success')

        return redirect(url_for('view_document', id=document.id))

    except Exception as e:
        db.session.rollback()
        print(f"❌ LIFECYCLE: Error marking for review: {e}")
        flash('حدث خطأ في تحديد الوثيقة للمراجعة', 'error')
        return redirect(url_for('view_document', id=document.id))

def get_document_lifecycle_info(document):
    """Get comprehensive lifecycle information for a document"""
    try:
        print(f"🔄 LIFECYCLE: Getting lifecycle info for document {document.id}")

        now = datetime.now()
        age_days = (now - document.created_at).days

        # Determine lifecycle stage
        if document.status == 'مسودة':
            stage = 'creation'
            stage_name = 'مرحلة الإنشاء'
        elif document.status == 'نشط':
            stage = 'active'
            stage_name = 'مرحلة النشاط'
        elif document.status == 'قيد المراجعة':
            stage = 'review'
            stage_name = 'مرحلة المراجعة'
        elif document.status == 'مؤرشف':
            stage = 'archived'
            stage_name = 'مرحلة الأرشفة'
        else:
            stage = 'unknown'
            stage_name = 'مرحلة غير محددة'

        # Extract retention information from tags
        retention_days = None
        extension_days = None
        review_scheduled = False

        if document.tags:
            tags = [tag.strip() for tag in document.tags.split(',')]
            for tag in tags:
                if tag.startswith('retention:'):
                    try:
                        retention_days = int(tag.split(':')[1].replace('days', ''))
                    except:
                        pass
                elif tag.startswith('extended:'):
                    try:
                        extension_days = int(tag.split(':')[1].replace('days', ''))
                    except:
                        pass
                elif tag.startswith('review_scheduled'):
                    review_scheduled = True

        # Calculate retention expiry
        retention_expires = None
        if retention_days:
            retention_expires = document.created_at + timedelta(days=retention_days)
            if extension_days:
                retention_expires += timedelta(days=extension_days)

        lifecycle_info = {
            'current_stage': stage,
            'current_stage_name': stage_name,
            'age_days': age_days,
            'age_text': f"{age_days} يوم",
            'retention_days': retention_days,
            'extension_days': extension_days,
            'retention_expires': retention_expires,
            'review_scheduled': review_scheduled,
            'can_archive': document.status in ['نشط', 'قيد المراجعة'],
            'can_restore': document.status == 'مؤرشف',
            'can_extend': document.status in ['نشط', 'مؤرشف'],
            'can_review': document.status in ['نشط', 'مؤرشف'],
            'actions_available': [],
            'warnings': [],
            'recommendations': []
        }

        # Add available actions
        if lifecycle_info['can_archive']:
            lifecycle_info['actions_available'].append('archive')
        if lifecycle_info['can_restore']:
            lifecycle_info['actions_available'].append('restore')
        if lifecycle_info['can_extend']:
            lifecycle_info['actions_available'].append('extend')
        if lifecycle_info['can_review']:
            lifecycle_info['actions_available'].append('review')

        # Add warnings and recommendations
        if age_days > 1095:  # 3 years
            lifecycle_info['warnings'].append('الوثيقة قديمة جداً وقد تحتاج للمراجعة')
        if retention_expires and retention_expires < now:
            lifecycle_info['warnings'].append('انتهت فترة الاحتفاظ بالوثيقة')

        if age_days > 365 and document.status == 'نشط':
            lifecycle_info['recommendations'].append('يُنصح بأرشفة الوثيقة')
        if not retention_days:
            lifecycle_info['recommendations'].append('يُنصح بتحديد سياسة احتفاظ للوثيقة')

        print(f"✅ LIFECYCLE: Lifecycle info generated for document {document.id}")
        return lifecycle_info

    except Exception as e:
        print(f"❌ LIFECYCLE: Error getting lifecycle info: {e}")
        return {}

# ENHANCED DOCUMENT MANAGEMENT MODULE (Medium Priority)
@app.route('/documents/enhanced')
@login_required
def enhanced_document_management():
    """ENHANCED: ميزات إدارة الوثائق المحسنة"""
    try:
        print("🚀 ENHANCED: Loading enhanced document management...")

        # Get enhanced statistics
        enhanced_stats = get_enhanced_document_statistics()

        # Get storage management data
        storage_data = get_storage_management_data()

        # Get document numbering system status
        numbering_system = get_document_numbering_system()

        # Get physical location tracking
        location_tracking = get_physical_location_tracking()

        return render_template('documents/enhanced.html',
                             enhanced_stats=enhanced_stats,
                             storage_data=storage_data,
                             numbering_system=numbering_system,
                             location_tracking=location_tracking)

    except Exception as e:
        logger.error(f"خطأ في تحميل الميزات المحسنة: {e}")
        flash('حدث خطأ في تحميل الميزات المحسنة', 'error')
        return render_template('documents/enhanced.html',
                             enhanced_stats={},
                             storage_data={},
                             numbering_system={},
                             location_tracking={})

@app.route('/documents/<int:id>/enhanced', methods=['GET', 'POST'])
@login_required
def enhanced_document_details(id):
    """CRITICAL: تفاصيل الوثيقة المحسنة مع الحقول الجديدة"""
    document = Document.query.get_or_404(id)

    if request.method == 'POST':
        try:
            action = request.form.get('action')
            print(f"🔄 ENHANCED: Processing action '{action}' for document {id}")

            if action == 'update_metadata':
                return update_enhanced_metadata(document)
            elif action == 'assign_storage':
                return assign_physical_storage(document)
            elif action == 'generate_number':
                return generate_document_number(document)
            elif action == 'update_location':
                return update_physical_location(document)
            else:
                flash('إجراء غير صالح', 'error')

        except Exception as e:
            logger.error(f"خطأ في معالجة الإجراء المحسن: {e}")
            flash('حدث خطأ في معالجة الإجراء', 'error')

    # Get enhanced document information
    enhanced_info = get_enhanced_document_info(document)

    return render_template('documents/enhanced_details.html',
                         document=document,
                         enhanced_info=enhanced_info)

def get_enhanced_document_statistics():
    """CRITICAL: Get enhanced document management statistics"""
    try:
        print("🔄 ENHANCED: Getting enhanced statistics...")

        stats = {
            'total_documents': Document.query.count(),
            'documents_with_numbers': 0,  # Will be calculated
            'documents_with_storage': 0,  # Will be calculated
            'documents_with_location': 0,  # Will be calculated
            'storage_utilization': {
                'total_capacity': 1000,  # This would come from configuration
                'used_capacity': 0,
                'available_capacity': 1000,
                'utilization_percentage': 0
            },
            'numbering_system': {
                'next_number': 1,
                'last_assigned': None,
                'format': 'DOC-{year}-{sequence:04d}',
                'total_assigned': 0
            },
            'physical_locations': {
                'total_cabinets': 0,
                'occupied_cabinets': 0,
                'total_shelves': 0,
                'occupied_shelves': 0
            },
            'metadata_completion': {
                'complete': 0,
                'partial': 0,
                'minimal': 0
            }
        }

        # Calculate enhanced statistics
        all_docs = Document.query.all()
        for doc in all_docs:
            # Check for document numbers in tags
            if doc.tags and 'doc_number:' in doc.tags:
                stats['documents_with_numbers'] += 1
                stats['numbering_system']['total_assigned'] += 1

            # Check for storage information in tags
            if doc.tags and ('storage:' in doc.tags or 'cabinet:' in doc.tags):
                stats['documents_with_storage'] += 1

            # Check for location information in tags
            if doc.tags and ('location:' in doc.tags or 'shelf:' in doc.tags):
                stats['documents_with_location'] += 1

            # Calculate metadata completion
            completion_score = 0
            if doc.title: completion_score += 1
            if doc.description: completion_score += 1
            if doc.document_type: completion_score += 1
            if doc.tags: completion_score += 1
            if doc.file_path: completion_score += 1

            if completion_score >= 4:
                stats['metadata_completion']['complete'] += 1
            elif completion_score >= 2:
                stats['metadata_completion']['partial'] += 1
            else:
                stats['metadata_completion']['minimal'] += 1

        # Calculate next document number
        current_year = datetime.now().year
        year_docs = [doc for doc in all_docs if doc.tags and f'doc_number:DOC-{current_year}' in doc.tags]
        stats['numbering_system']['next_number'] = len(year_docs) + 1

        # Calculate storage utilization (simplified)
        stats['storage_utilization']['used_capacity'] = stats['documents_with_storage']
        stats['storage_utilization']['available_capacity'] = stats['storage_utilization']['total_capacity'] - stats['storage_utilization']['used_capacity']
        if stats['storage_utilization']['total_capacity'] > 0:
            stats['storage_utilization']['utilization_percentage'] = (stats['storage_utilization']['used_capacity'] / stats['storage_utilization']['total_capacity']) * 100

        print(f"✅ ENHANCED: Statistics calculated - {stats['total_documents']} total documents")
        return stats

    except Exception as e:
        print(f"❌ ENHANCED: Error getting enhanced statistics: {e}")
        return {}

def get_storage_management_data():
    """CRITICAL: Get comprehensive storage management data"""
    try:
        print("🔄 ENHANCED: Getting storage management data...")

        storage_data = {
            'cabinets': [],
            'storage_locations': [],
            'capacity_analysis': {},
            'recommendations': []
        }

        # Generate sample cabinet data (this would come from a cabinets table)
        for i in range(1, 11):  # 10 cabinets
            cabinet = {
                'id': i,
                'name': f'خزانة {i}',
                'name_en': f'Cabinet {i}',
                'location': f'الطابق الأول - القسم {(i-1)//3 + 1}',
                'capacity': 100,
                'used': 0,
                'available': 100,
                'documents': [],
                'status': 'available'
            }

            # Count documents in this cabinet
            docs_in_cabinet = Document.query.filter(
                Document.tags.like(f'%cabinet:{i}%')
            ).all()

            cabinet['used'] = len(docs_in_cabinet)
            cabinet['available'] = cabinet['capacity'] - cabinet['used']
            cabinet['documents'] = docs_in_cabinet[:5]  # Show first 5

            if cabinet['used'] >= cabinet['capacity']:
                cabinet['status'] = 'full'
            elif cabinet['used'] >= cabinet['capacity'] * 0.8:
                cabinet['status'] = 'nearly_full'

            storage_data['cabinets'].append(cabinet)

        # Generate storage locations
        locations = [
            'الطابق الأول - القسم الإداري',
            'الطابق الأول - القسم المالي',
            'الطابق الثاني - القسم القانوني',
            'الطابق الثاني - الأرشيف العام',
            'الطابق الثالث - الوثائق التاريخية'
        ]

        for i, location in enumerate(locations, 1):
            location_data = {
                'id': i,
                'name': location,
                'cabinets_count': 2,
                'total_capacity': 200,
                'used_capacity': 0,
                'documents_count': 0
            }

            # Count documents in this location
            location_docs = Document.query.filter(
                Document.tags.like(f'%location:{i}%')
            ).count()

            location_data['documents_count'] = location_docs
            location_data['used_capacity'] = location_docs

            storage_data['storage_locations'].append(location_data)

        # Generate capacity analysis
        total_capacity = sum(cabinet['capacity'] for cabinet in storage_data['cabinets'])
        total_used = sum(cabinet['used'] for cabinet in storage_data['cabinets'])

        storage_data['capacity_analysis'] = {
            'total_capacity': total_capacity,
            'total_used': total_used,
            'total_available': total_capacity - total_used,
            'utilization_percentage': (total_used / total_capacity * 100) if total_capacity > 0 else 0,
            'projected_full_date': None,
            'growth_rate': 5  # Documents per month
        }

        # Generate recommendations
        if storage_data['capacity_analysis']['utilization_percentage'] > 80:
            storage_data['recommendations'].append({
                'type': 'warning',
                'message': 'مساحة التخزين تقترب من الامتلاء',
                'action': 'يُنصح بإضافة خزائن جديدة أو أرشفة الوثائق القديمة'
            })

        if storage_data['capacity_analysis']['utilization_percentage'] > 90:
            storage_data['recommendations'].append({
                'type': 'critical',
                'message': 'مساحة التخزين ممتلئة تقريباً',
                'action': 'إجراء فوري مطلوب لتوسيع مساحة التخزين'
            })

        print(f"✅ ENHANCED: Storage data generated - {len(storage_data['cabinets'])} cabinets")
        return storage_data

    except Exception as e:
        print(f"❌ ENHANCED: Error getting storage data: {e}")
        return {}

def get_document_numbering_system():
    """CRITICAL: Get document numbering system information"""
    try:
        print("🔄 ENHANCED: Getting document numbering system...")

        current_year = datetime.now().year

        # Count documents with numbers for current year
        numbered_docs_this_year = Document.query.filter(
            Document.tags.like(f'%doc_number:DOC-{current_year}%')
        ).count()

        numbering_system = {
            'enabled': True,
            'format': 'DOC-{year}-{sequence:04d}',
            'current_year': current_year,
            'next_sequence': numbered_docs_this_year + 1,
            'total_this_year': numbered_docs_this_year,
            'last_assigned': None,
            'auto_assign': True,
            'prefix_options': ['DOC', 'ARCH', 'ADMIN', 'FIN', 'LEGAL'],
            'statistics': {
                'total_assigned': 0,
                'by_year': {},
                'by_type': {}
            }
        }

        # Get last assigned number
        last_doc = Document.query.filter(
            Document.tags.like(f'%doc_number:DOC-{current_year}%')
        ).order_by(Document.created_at.desc()).first()

        if last_doc:
            # Extract number from tags
            for tag in last_doc.tags.split(','):
                if tag.strip().startswith('doc_number:'):
                    numbering_system['last_assigned'] = tag.strip().replace('doc_number:', '')
                    break

        # Calculate statistics by year
        for year in range(current_year - 4, current_year + 1):
            year_count = Document.query.filter(
                Document.tags.like(f'%doc_number:DOC-{year}%')
            ).count()
            numbering_system['statistics']['by_year'][str(year)] = year_count
            numbering_system['statistics']['total_assigned'] += year_count

        # Calculate statistics by document type
        doc_types = db.session.query(Document.document_type, db.func.count(Document.id)).filter(
            Document.tags.like('%doc_number:%')
        ).group_by(Document.document_type).all()

        for doc_type, count in doc_types:
            if doc_type:
                numbering_system['statistics']['by_type'][doc_type] = count

        print(f"✅ ENHANCED: Numbering system loaded - {numbering_system['total_this_year']} documents this year")
        return numbering_system

    except Exception as e:
        print(f"❌ ENHANCED: Error getting numbering system: {e}")
        return {}

def get_physical_location_tracking():
    """Get physical location tracking information"""
    try:
        print("🔄 ENHANCED: Getting physical location tracking...")

        location_tracking = {
            'enabled': True,
            'total_tracked': 0,
            'locations': [],
            'cabinets': [],
            'shelves': [],
            'tracking_statistics': {
                'fully_tracked': 0,
                'partially_tracked': 0,
                'not_tracked': 0
            },
            'recent_movements': []
        }

        # Count tracked documents
        tracked_docs = Document.query.filter(
            db.or_(
                Document.tags.like('%location:%'),
                Document.tags.like('%cabinet:%'),
                Document.tags.like('%shelf:%')
            )
        ).all()

        location_tracking['total_tracked'] = len(tracked_docs)

        # Analyze tracking completeness
        all_docs = Document.query.all()
        for doc in all_docs:
            tracking_score = 0
            if doc.tags:
                if 'location:' in doc.tags: tracking_score += 1
                if 'cabinet:' in doc.tags: tracking_score += 1
                if 'shelf:' in doc.tags: tracking_score += 1

            if tracking_score >= 3:
                location_tracking['tracking_statistics']['fully_tracked'] += 1
            elif tracking_score >= 1:
                location_tracking['tracking_statistics']['partially_tracked'] += 1
            else:
                location_tracking['tracking_statistics']['not_tracked'] += 1

        # Generate sample locations
        sample_locations = [
            {'id': 1, 'name': 'الطابق الأول - الإدارة', 'documents_count': 45},
            {'id': 2, 'name': 'الطابق الأول - المالية', 'documents_count': 32},
            {'id': 3, 'name': 'الطابق الثاني - القانونية', 'documents_count': 28},
            {'id': 4, 'name': 'الطابق الثاني - الأرشيف', 'documents_count': 156},
            {'id': 5, 'name': 'الطابق الثالث - التاريخية', 'documents_count': 89}
        ]

        location_tracking['locations'] = sample_locations

        print(f"✅ ENHANCED: Location tracking loaded - {location_tracking['total_tracked']} tracked documents")
        return location_tracking

    except Exception as e:
        print(f"❌ ENHANCED: Error getting location tracking: {e}")
        return {}

def update_enhanced_metadata(document):
    """CRITICAL: Update enhanced metadata fields"""
    try:
        print(f"🔄 ENHANCED: Updating enhanced metadata for document {document.id}")

        # Get form data
        document_number = request.form.get('document_number', '').strip()
        storage_number = request.form.get('storage_number', '').strip()
        cabinet_number = request.form.get('cabinet_number', '').strip()
        shelf_number = request.form.get('shelf_number', '').strip()
        physical_location = request.form.get('physical_location', '').strip()

        # Update tags with new metadata
        tags = []
        if document.tags:
            # Keep existing non-metadata tags
            existing_tags = [tag.strip() for tag in document.tags.split(',')]
            tags = [tag for tag in existing_tags if not any(
                tag.startswith(prefix) for prefix in ['doc_number:', 'storage:', 'cabinet:', 'shelf:', 'location:']
            )]

        # Add new metadata tags
        if document_number:
            tags.append(f'doc_number:{document_number}')
        if storage_number:
            tags.append(f'storage:{storage_number}')
        if cabinet_number:
            tags.append(f'cabinet:{cabinet_number}')
        if shelf_number:
            tags.append(f'shelf:{shelf_number}')
        if physical_location:
            tags.append(f'location:{physical_location}')

        # Update document
        document.tags = ', '.join(tags) if tags else ''
        document.updated_at = datetime.utcnow()

        # Log the action
        log_activity('metadata_update', 'document', document.id, document.title,
                    f'تحديث البيانات المحسنة للوثيقة: {document.title}')

        db.session.commit()

        print(f"✅ ENHANCED: Enhanced metadata updated for document {document.id}")
        flash('تم تحديث البيانات المحسنة بنجاح', 'success')

        return redirect(url_for('enhanced_document_details', id=document.id))

    except Exception as e:
        db.session.rollback()
        print(f"❌ ENHANCED: Error updating enhanced metadata: {e}")
        flash('حدث خطأ في تحديث البيانات المحسنة', 'error')
        return redirect(url_for('enhanced_document_details', id=document.id))

def assign_physical_storage(document):
    """Assign physical storage location to document"""
    try:
        cabinet_id = request.form.get('cabinet_id', type=int)
        shelf_id = request.form.get('shelf_id', type=int)
        location_notes = request.form.get('location_notes', '').strip()

        print(f"🔄 ENHANCED: Assigning physical storage for document {document.id}")

        # Update tags with storage information
        tags = []
        if document.tags:
            existing_tags = [tag.strip() for tag in document.tags.split(',')]
            tags = [tag for tag in existing_tags if not any(
                tag.startswith(prefix) for prefix in ['cabinet:', 'shelf:', 'storage_notes:']
            )]

        if cabinet_id:
            tags.append(f'cabinet:{cabinet_id}')
        if shelf_id:
            tags.append(f'shelf:{shelf_id}')
        if location_notes:
            tags.append(f'storage_notes:{location_notes}')

        document.tags = ', '.join(tags) if tags else ''
        document.updated_at = datetime.utcnow()

        # Log the action
        log_activity('storage_assign', 'document', document.id, document.title,
                    f'تخصيص موقع تخزين: خزانة {cabinet_id}, رف {shelf_id}')

        db.session.commit()

        print(f"✅ ENHANCED: Physical storage assigned for document {document.id}")
        flash('تم تخصيص موقع التخزين بنجاح', 'success')

        return redirect(url_for('enhanced_document_details', id=document.id))

    except Exception as e:
        db.session.rollback()
        print(f"❌ ENHANCED: Error assigning storage: {e}")
        flash('حدث خطأ في تخصيص موقع التخزين', 'error')
        return redirect(url_for('enhanced_document_details', id=document.id))

def generate_document_number(document):
    """CRITICAL: Generate automatic document number"""
    try:
        print(f"🔄 ENHANCED: Generating document number for document {document.id}")

        # Get numbering format
        prefix = request.form.get('prefix', 'DOC')
        current_year = datetime.now().year

        # Count existing documents with numbers for this year
        existing_count = Document.query.filter(
            Document.tags.like(f'%doc_number:{prefix}-{current_year}%')
        ).count()

        # Generate new number
        sequence = existing_count + 1
        document_number = f'{prefix}-{current_year}-{sequence:04d}'

        # Update tags
        tags = []
        if document.tags:
            existing_tags = [tag.strip() for tag in document.tags.split(',')]
            tags = [tag for tag in existing_tags if not tag.startswith('doc_number:')]

        tags.append(f'doc_number:{document_number}')

        document.tags = ', '.join(tags)
        document.updated_at = datetime.utcnow()

        # Log the action
        log_activity('number_generate', 'document', document.id, document.title,
                    f'إنشاء رقم وثيقة: {document_number}')

        db.session.commit()

        print(f"✅ ENHANCED: Document number generated: {document_number}")
        flash(f'تم إنشاء رقم الوثيقة: {document_number}', 'success')

        return redirect(url_for('enhanced_document_details', id=document.id))

    except Exception as e:
        db.session.rollback()
        print(f"❌ ENHANCED: Error generating document number: {e}")
        flash('حدث خطأ في إنشاء رقم الوثيقة', 'error')
        return redirect(url_for('enhanced_document_details', id=document.id))

def update_physical_location(document):
    """Update physical location information"""
    try:
        location_id = request.form.get('location_id', type=int)
        floor = request.form.get('floor', '').strip()
        room = request.form.get('room', '').strip()
        section = request.form.get('section', '').strip()

        print(f"🔄 ENHANCED: Updating physical location for document {document.id}")

        # Update tags with location information
        tags = []
        if document.tags:
            existing_tags = [tag.strip() for tag in document.tags.split(',')]
            tags = [tag for tag in existing_tags if not any(
                tag.startswith(prefix) for prefix in ['location:', 'floor:', 'room:', 'section:']
            )]

        if location_id:
            tags.append(f'location:{location_id}')
        if floor:
            tags.append(f'floor:{floor}')
        if room:
            tags.append(f'room:{room}')
        if section:
            tags.append(f'section:{section}')

        document.tags = ', '.join(tags) if tags else ''
        document.updated_at = datetime.utcnow()

        # Log the action
        log_activity('location_update', 'document', document.id, document.title,
                    f'تحديث الموقع الفيزيائي: طابق {floor}, غرفة {room}, قسم {section}')

        db.session.commit()

        print(f"✅ ENHANCED: Physical location updated for document {document.id}")
        flash('تم تحديث الموقع الفيزيائي بنجاح', 'success')

        return redirect(url_for('enhanced_document_details', id=document.id))

    except Exception as e:
        db.session.rollback()
        print(f"❌ ENHANCED: Error updating location: {e}")
        flash('حدث خطأ في تحديث الموقع الفيزيائي', 'error')
        return redirect(url_for('enhanced_document_details', id=document.id))

def get_enhanced_document_info(document):
    """CRITICAL: Get comprehensive enhanced information for a document"""
    try:
        print(f"🔄 ENHANCED: Getting enhanced info for document {document.id}")

        enhanced_info = {
            'document_number': None,
            'storage_number': None,
            'cabinet_number': None,
            'shelf_number': None,
            'physical_location': None,
            'floor': None,
            'room': None,
            'section': None,
            'storage_notes': None,
            'metadata_completion': 0,
            'tracking_status': 'not_tracked',
            'storage_assigned': False,
            'number_assigned': False,
            'location_tracked': False,
            'recommendations': [],
            'available_actions': []
        }

        # Extract information from tags
        if document.tags:
            tags = [tag.strip() for tag in document.tags.split(',')]
            for tag in tags:
                if tag.startswith('doc_number:'):
                    enhanced_info['document_number'] = tag.replace('doc_number:', '')
                    enhanced_info['number_assigned'] = True
                elif tag.startswith('storage:'):
                    enhanced_info['storage_number'] = tag.replace('storage:', '')
                elif tag.startswith('cabinet:'):
                    enhanced_info['cabinet_number'] = tag.replace('cabinet:', '')
                    enhanced_info['storage_assigned'] = True
                elif tag.startswith('shelf:'):
                    enhanced_info['shelf_number'] = tag.replace('shelf:', '')
                elif tag.startswith('location:'):
                    enhanced_info['physical_location'] = tag.replace('location:', '')
                    enhanced_info['location_tracked'] = True
                elif tag.startswith('floor:'):
                    enhanced_info['floor'] = tag.replace('floor:', '')
                elif tag.startswith('room:'):
                    enhanced_info['room'] = tag.replace('room:', '')
                elif tag.startswith('section:'):
                    enhanced_info['section'] = tag.replace('section:', '')
                elif tag.startswith('storage_notes:'):
                    enhanced_info['storage_notes'] = tag.replace('storage_notes:', '')

        # Calculate metadata completion percentage
        completion_fields = [
            enhanced_info['document_number'],
            enhanced_info['cabinet_number'],
            enhanced_info['physical_location'],
            document.title,
            document.description,
            document.document_type
        ]

        completed_fields = sum(1 for field in completion_fields if field)
        enhanced_info['metadata_completion'] = int((completed_fields / len(completion_fields)) * 100)

        # Determine tracking status
        if enhanced_info['number_assigned'] and enhanced_info['storage_assigned'] and enhanced_info['location_tracked']:
            enhanced_info['tracking_status'] = 'fully_tracked'
        elif enhanced_info['number_assigned'] or enhanced_info['storage_assigned'] or enhanced_info['location_tracked']:
            enhanced_info['tracking_status'] = 'partially_tracked'
        else:
            enhanced_info['tracking_status'] = 'not_tracked'

        # Generate recommendations
        if not enhanced_info['number_assigned']:
            enhanced_info['recommendations'].append({
                'type': 'number',
                'message': 'يُنصح بإنشاء رقم للوثيقة',
                'action': 'generate_number'
            })

        if not enhanced_info['storage_assigned']:
            enhanced_info['recommendations'].append({
                'type': 'storage',
                'message': 'يُنصح بتخصيص موقع تخزين للوثيقة',
                'action': 'assign_storage'
            })

        if not enhanced_info['location_tracked']:
            enhanced_info['recommendations'].append({
                'type': 'location',
                'message': 'يُنصح بتحديد الموقع الفيزيائي للوثيقة',
                'action': 'update_location'
            })

        if enhanced_info['metadata_completion'] < 80:
            enhanced_info['recommendations'].append({
                'type': 'metadata',
                'message': 'يُنصح بإكمال البيانات الوصفية للوثيقة',
                'action': 'update_metadata'
            })

        # Available actions
        enhanced_info['available_actions'] = [
            'update_metadata',
            'assign_storage',
            'generate_number',
            'update_location'
        ]

        print(f"✅ ENHANCED: Enhanced info generated for document {document.id}")
        return enhanced_info

    except Exception as e:
        print(f"❌ ENHANCED: Error getting enhanced info: {e}")
        return {}

@app.route('/api/documents/search-enhanced', methods=['GET'])
@login_required
def api_search_enhanced_documents():
    """CRITICAL: Enhanced document search API with advanced filters"""
    try:
        # Get search parameters
        query = request.args.get('q', '').strip()
        document_type = request.args.get('type', '')
        has_number = request.args.get('has_number', '')
        has_storage = request.args.get('has_storage', '')
        cabinet = request.args.get('cabinet', '')
        location = request.args.get('location', '')
        date_from = request.args.get('date_from', '')
        date_to = request.args.get('date_to', '')

        print(f"🔍 ENHANCED: Enhanced search - query: '{query}'")

        # Build query
        search_query = Document.query

        # Text search
        if query:
            search_query = search_query.filter(
                db.or_(
                    Document.title.ilike(f'%{query}%'),
                    Document.description.ilike(f'%{query}%'),
                    Document.tags.ilike(f'%{query}%')
                )
            )

        # Document type filter
        if document_type:
            search_query = search_query.filter(Document.document_type == document_type)

        # Enhanced filters
        if has_number == 'true':
            search_query = search_query.filter(Document.tags.like('%doc_number:%'))
        elif has_number == 'false':
            search_query = search_query.filter(~Document.tags.like('%doc_number:%'))

        if has_storage == 'true':
            search_query = search_query.filter(Document.tags.like('%cabinet:%'))
        elif has_storage == 'false':
            search_query = search_query.filter(~Document.tags.like('%cabinet:%'))

        if cabinet:
            search_query = search_query.filter(Document.tags.like(f'%cabinet:{cabinet}%'))

        if location:
            search_query = search_query.filter(Document.tags.like(f'%location:{location}%'))

        # Date filters
        if date_from:
            try:
                from_date = datetime.strptime(date_from, '%Y-%m-%d')
                search_query = search_query.filter(Document.created_at >= from_date)
            except:
                pass

        if date_to:
            try:
                to_date = datetime.strptime(date_to, '%Y-%m-%d')
                search_query = search_query.filter(Document.created_at <= to_date)
            except:
                pass

        # Execute search
        documents = search_query.order_by(Document.created_at.desc()).limit(50).all()

        # Format results
        results = []
        for doc in documents:
            enhanced_info = get_enhanced_document_info(doc)

            results.append({
                'id': doc.id,
                'title': doc.title,
                'document_type': doc.document_type,
                'status': doc.status,
                'created_at': doc.created_at.isoformat(),
                'document_number': enhanced_info.get('document_number'),
                'cabinet_number': enhanced_info.get('cabinet_number'),
                'physical_location': enhanced_info.get('physical_location'),
                'metadata_completion': enhanced_info.get('metadata_completion', 0),
                'tracking_status': enhanced_info.get('tracking_status', 'not_tracked'),
                'url': url_for('view_document', id=doc.id)
            })

        print(f"✅ ENHANCED: Search completed - {len(results)} results")

        return jsonify({
            'success': True,
            'results': results,
            'total': len(results),
            'query': query
        })

    except Exception as e:
        print(f"❌ ENHANCED: Search error: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'results': []
        }), 500

@app.route('/api/notifications/unread-count')
@login_required
def api_unread_notifications_count():
    """CRITICAL: API endpoint for unread notifications count"""
    try:
        # This would typically come from a notifications table
        # For now, return a sample count based on user activity

        # Count recent activities as potential notifications
        recent_activities = ActivityLog.query.filter(
            ActivityLog.user_id == current_user.id,
            ActivityLog.created_at >= datetime.now() - timedelta(hours=24)
        ).count()

        # Simulate unread count (this would be actual unread notifications)
        unread_count = min(recent_activities, 5)  # Cap at 5 for demo

        return jsonify({
            'success': True,
            'count': unread_count
        })

    except Exception as e:
        logger.error(f"خطأ في API عدد الإشعارات: {e}")
        return jsonify({
            'success': False,
            'count': 0
        }), 500

@app.route('/api/enhanced/numbering-settings', methods=['POST'])
@login_required
def api_save_numbering_settings():
    """CRITICAL: Save document numbering system settings"""
    try:
        print("🔧 ENHANCED: Saving numbering settings...")

        prefix = request.form.get('prefix', 'DOC')
        auto_assign = request.form.get('auto_assign') == 'on'

        # In a real system, these would be saved to a settings table
        # For now, we'll just log the settings
        print(f"✅ ENHANCED: Numbering settings saved - prefix: {prefix}, auto: {auto_assign}")

        return jsonify({
            'success': True,
            'message': 'تم حفظ إعدادات الترقيم بنجاح'
        })

    except Exception as e:
        print(f"❌ ENHANCED: Error saving numbering settings: {e}")
        return jsonify({
            'success': False,
            'message': 'حدث خطأ في حفظ الإعدادات'
        })

@app.route('/api/enhanced/assign-numbers', methods=['POST'])
@login_required
def api_assign_numbers_to_documents():
    """CRITICAL: Assign numbers to documents without numbers"""
    try:
        print("🔢 ENHANCED: Assigning numbers to documents...")

        # Find documents without numbers
        documents_without_numbers = Document.query.filter(
            ~Document.tags.like('%doc_number:%')
        ).all()

        assigned_count = 0
        current_year = datetime.now().year

        for doc in documents_without_numbers:
            try:
                # Generate document number
                existing_count = Document.query.filter(
                    Document.tags.like(f'%doc_number:DOC-{current_year}%')
                ).count()

                sequence = existing_count + assigned_count + 1
                document_number = f'DOC-{current_year}-{sequence:04d}'

                # Update tags
                tags = []
                if doc.tags:
                    tags = [tag.strip() for tag in doc.tags.split(',')]

                tags.append(f'doc_number:{document_number}')
                doc.tags = ', '.join(tags)
                doc.updated_at = datetime.utcnow()

                assigned_count += 1

                # Log the action
                log_activity('assign_number', 'document', doc.id, doc.title,
                           f'تم تخصيص الرقم: {document_number}')

            except Exception as doc_error:
                print(f"❌ ENHANCED: Error assigning number to document {doc.id}: {doc_error}")
                continue

        db.session.commit()

        print(f"✅ ENHANCED: Assigned numbers to {assigned_count} documents")

        return jsonify({
            'success': True,
            'assigned_count': assigned_count,
            'message': f'تم ترقيم {assigned_count} وثيقة بنجاح'
        })

    except Exception as e:
        db.session.rollback()
        print(f"❌ ENHANCED: Error assigning numbers: {e}")
        return jsonify({
            'success': False,
            'message': 'حدث خطأ في ترقيم الوثائق'
        })

@app.route('/api/enhanced/add-cabinet', methods=['POST'])
@login_required
def api_add_new_cabinet():
    """CRITICAL: Add new storage cabinet"""
    try:
        print("📦 ENHANCED: Adding new cabinet...")

        name = request.form.get('name')
        location = request.form.get('location')
        capacity = int(request.form.get('capacity', 100))
        notes = request.form.get('notes', '')

        if not name or not location:
            return jsonify({
                'success': False,
                'message': 'اسم الخزانة والموقع مطلوبان'
            })

        # In a real system, this would be saved to a cabinets table
        # For now, we'll simulate success
        print(f"✅ ENHANCED: Cabinet added - {name} at location {location} with capacity {capacity}")

        return jsonify({
            'success': True,
            'message': 'تم إضافة الخزانة بنجاح'
        })

    except Exception as e:
        print(f"❌ ENHANCED: Error adding cabinet: {e}")
        return jsonify({
            'success': False,
            'message': 'حدث خطأ في إضافة الخزانة'
        })

@app.route('/api/enhanced/assign-storage', methods=['POST'])
@login_required
def api_assign_storage_to_documents():
    """CRITICAL: Assign storage locations to documents"""
    try:
        print("📦 ENHANCED: Assigning storage to documents...")

        selection_type = request.form.get('selection_type', 'unassigned')
        document_type = request.form.get('document_type', '')
        assignment_strategy = request.form.get('assignment_strategy', 'auto')

        # Build query based on selection type
        query = Document.query

        if selection_type == 'unassigned':
            query = query.filter(~Document.tags.like('%cabinet:%'))
        elif selection_type == 'by_type' and document_type:
            query = query.filter(Document.document_type == document_type)

        documents = query.all()
        assigned_count = 0

        for i, doc in enumerate(documents):
            try:
                # Assign cabinet based on strategy
                if assignment_strategy == 'auto':
                    cabinet_id = (i % 5) + 1  # Distribute across 5 cabinets
                elif assignment_strategy == 'by_type':
                    cabinet_id = hash(doc.document_type) % 5 + 1
                else:  # by_date
                    cabinet_id = (doc.created_at.year % 5) + 1 if doc.created_at else 1

                shelf_id = (i % 10) + 1  # 10 shelves per cabinet

                # Update tags
                tags = []
                if doc.tags:
                    existing_tags = [tag.strip() for tag in doc.tags.split(',')]
                    tags = [tag for tag in existing_tags if not tag.startswith('cabinet:') and not tag.startswith('shelf:')]

                tags.extend([f'cabinet:{cabinet_id}', f'shelf:{shelf_id}'])
                doc.tags = ', '.join(tags)
                doc.updated_at = datetime.utcnow()

                assigned_count += 1

                # Log the action
                log_activity('storage_assign', 'document', doc.id, doc.title,
                           f'تخصيص تخزين: خزانة {cabinet_id}, رف {shelf_id}')

            except Exception as doc_error:
                print(f"❌ ENHANCED: Error assigning storage to document {doc.id}: {doc_error}")
                continue

        db.session.commit()

        print(f"✅ ENHANCED: Assigned storage to {assigned_count} documents")

        return jsonify({
            'success': True,
            'assigned_count': assigned_count,
            'message': f'تم تخصيص التخزين لـ {assigned_count} وثيقة'
        })

    except Exception as e:
        db.session.rollback()
        print(f"❌ ENHANCED: Error assigning storage: {e}")
        return jsonify({
            'success': False,
            'message': 'حدث خطأ في تخصيص التخزين'
        })

@app.route('/api/enhanced/update-locations', methods=['POST'])
@login_required
def api_update_physical_locations():
    """CRITICAL: Update physical locations of documents"""
    try:
        print("📍 ENHANCED: Updating physical locations...")

        update_method = request.form.get('update_method', 'bulk_update')
        update_scope = request.form.get('update_scope', 'untracked')

        # Build query based on scope
        query = Document.query

        if update_scope == 'untracked':
            query = query.filter(~Document.tags.like('%location:%'))
        elif update_scope == 'specific_location':
            # This would filter by specific location in a real system
            pass

        documents = query.all()
        updated_count = 0

        for i, doc in enumerate(documents):
            try:
                # Assign physical location based on method
                if update_method == 'bulk_update':
                    floor = (i % 3) + 1  # 3 floors
                    room = (i % 10) + 1  # 10 rooms per floor
                    location = f'الطابق {floor} - غرفة {room}'
                elif update_method == 'manual_entry':
                    location = f'موقع يدوي {i + 1}'
                else:  # scan_qr
                    location = f'موقع QR {i + 1}'

                # Update tags
                tags = []
                if doc.tags:
                    existing_tags = [tag.strip() for tag in doc.tags.split(',')]
                    tags = [tag for tag in existing_tags if not tag.startswith('location:')]

                tags.append(f'location:{location}')
                doc.tags = ', '.join(tags)
                doc.updated_at = datetime.utcnow()

                updated_count += 1

                # Log the action
                log_activity('location_update', 'document', doc.id, doc.title,
                           f'تحديث الموقع: {location}')

            except Exception as doc_error:
                print(f"❌ ENHANCED: Error updating location for document {doc.id}: {doc_error}")
                continue

        db.session.commit()

        print(f"✅ ENHANCED: Updated locations for {updated_count} documents")

        return jsonify({
            'success': True,
            'updated_count': updated_count,
            'message': f'تم تحديث مواقع {updated_count} وثيقة'
        })

    except Exception as e:
        db.session.rollback()
        print(f"❌ ENHANCED: Error updating locations: {e}")
        return jsonify({
            'success': False,
            'message': 'حدث خطأ في تحديث المواقع'
        })

@app.route('/api/enhanced/enhance-metadata', methods=['POST'])
@login_required
def api_enhance_metadata():
    """CRITICAL: Enhance document metadata"""
    try:
        print("🏷️ ENHANCED: Enhancing metadata...")

        enhancement_type = request.form.get('enhancement_type', 'auto_tags')
        scope = request.form.get('scope', 'incomplete')
        backup_original = request.form.get('backup_original') == 'on'

        # Build query based on scope
        query = Document.query

        if scope == 'incomplete':
            query = query.filter(
                (Document.description == None) |
                (Document.description == '') |
                (Document.tags == None) |
                (Document.tags == '')
            )
        elif scope == 'recent':
            thirty_days_ago = datetime.now() - timedelta(days=30)
            query = query.filter(Document.created_at >= thirty_days_ago)

        documents = query.all()
        enhanced_count = 0

        for doc in documents:
            try:
                enhanced = False

                if enhancement_type == 'auto_tags':
                    # Auto-generate tags based on title and type
                    auto_tags = []
                    if doc.document_type:
                        auto_tags.append(doc.document_type)

                    # Extract keywords from title
                    title_words = doc.title.split() if doc.title else []
                    for word in title_words:
                        if len(word) > 3:  # Only meaningful words
                            auto_tags.append(word)

                    if auto_tags:
                        existing_tags = []
                        if doc.tags:
                            existing_tags = [tag.strip() for tag in doc.tags.split(',')]

                        # Add new tags that don't exist
                        for tag in auto_tags[:5]:  # Limit to 5 auto tags
                            if tag not in existing_tags:
                                existing_tags.append(tag)

                        doc.tags = ', '.join(existing_tags)
                        enhanced = True

                elif enhancement_type == 'categorization':
                    # Auto-categorize based on keywords
                    if doc.title:
                        title_lower = doc.title.lower()
                        if any(word in title_lower for word in ['مالي', 'فاتورة', 'ميزانية']):
                            doc.document_type = 'وثائق مالية'
                            enhanced = True
                        elif any(word in title_lower for word in ['قانوني', 'عقد', 'اتفاقية']):
                            doc.document_type = 'وثائق قانونية'
                            enhanced = True
                        elif any(word in title_lower for word in ['إداري', 'قرار', 'تعميم']):
                            doc.document_type = 'وثائق إدارية'
                            enhanced = True

                elif enhancement_type == 'completion':
                    # Complete missing data
                    if not doc.description and doc.title:
                        doc.description = f'وثيقة {doc.document_type}: {doc.title}'
                        enhanced = True

                if enhanced:
                    doc.updated_at = datetime.utcnow()
                    enhanced_count += 1

                    # Log the action
                    log_activity('metadata_enhance', 'document', doc.id, doc.title,
                               f'تحسين البيانات: {enhancement_type}')

            except Exception as doc_error:
                print(f"❌ ENHANCED: Error enhancing metadata for document {doc.id}: {doc_error}")
                continue

        db.session.commit()

        print(f"✅ ENHANCED: Enhanced metadata for {enhanced_count} documents")

        return jsonify({
            'success': True,
            'enhanced_count': enhanced_count,
            'message': f'تم تحسين بيانات {enhanced_count} وثيقة'
        })

    except Exception as e:
        db.session.rollback()
        print(f"❌ ENHANCED: Error enhancing metadata: {e}")
        return jsonify({
            'success': False,
            'message': 'حدث خطأ في تحسين البيانات'
        })

# مسارات إدارة الوثائق
@app.route('/documents')
@login_required
def documents():
    """عرض جميع الوثائق - محسن للسرعة القصوى"""
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '', type=str)
    doc_type = request.args.get('type', '', type=str)
    status = request.args.get('status', '', type=str)

    # استعلام محسن مع تحديد الحقول الأساسية فقط
    query = Document.query.with_entities(
        Document.id,
        Document.title,
        Document.document_type,
        Document.status,
        Document.created_at
    )

    # فلاتر محسنة
    if search:
        query = query.filter(Document.title.ilike(f'%{search}%'))
    if doc_type:
        query = query.filter(Document.document_type == doc_type)
    if status:
        query = query.filter(Document.status == status)

    # ترتيب وتقسيم محسن
    documents = query.order_by(Document.created_at.desc()).paginate(
        page=page, per_page=15, error_out=False
    )

    return render_template('documents/list.html', documents=documents,
                         search=search, doc_type=doc_type, status=status)

@app.route('/documents/add', methods=['GET', 'POST'])
@login_required
def add_document():
    """إضافة وثيقة جديدة - محسن ومُصلح بالكامل"""
    return add_document_logic()

@app.route('/test-add-document', methods=['GET', 'POST'])
def test_add_document():
    """مسار اختبار لصفحة إضافة الوثائق بدون تسجيل دخول"""
    return add_document_logic()

def check_auto_save_recovery():
    """Check for auto-save recovery data"""
    try:
        backup_dir = os.path.join(app.root_path, 'backups', 'documents')
        emergency_dir = os.path.join(app.root_path, 'emergency_saves')

        recovery_data = None
        latest_time = 0

        # Check backup files
        if os.path.exists(backup_dir):
            for filename in os.listdir(backup_dir):
                if filename.endswith('_backup.json'):
                    filepath = os.path.join(backup_dir, filename)
                    mtime = os.path.getmtime(filepath)

                    # Only consider files from last 24 hours
                    if time.time() - mtime < 86400 and mtime > latest_time:
                        try:
                            with open(filepath, 'r', encoding='utf-8') as f:
                                data = json.load(f)
                                recovery_data = data
                                latest_time = mtime
                        except Exception:
                            continue

        # Check emergency saves
        if os.path.exists(emergency_dir):
            for filename in os.listdir(emergency_dir):
                if filename.startswith('emergency_') and filename.endswith('.json'):
                    filepath = os.path.join(emergency_dir, filename)
                    mtime = os.path.getmtime(filepath)

                    if time.time() - mtime < 86400 and mtime > latest_time:
                        try:
                            with open(filepath, 'r', encoding='utf-8') as f:
                                data = json.load(f)
                                recovery_data = data
                                latest_time = mtime
                        except Exception:
                            continue

        return recovery_data

    except Exception as e:
        print(f"Error checking auto-save recovery: {e}")
        return None

def add_document_logic():
    """CRITICAL FIX: Document addition logic with auto-save and guaranteed 24 document types"""
    print("🚀 CRITICAL FIX: STARTING ADD DOCUMENT PROCESS WITH AUTO-SAVE")
    print(f"📊 Request Method: {request.method}")
    print(f"👤 Current User: {current_user.username if current_user.is_authenticated else 'Test Mode'}")

    # Check for auto-save recovery
    recovery_data = check_auto_save_recovery()
    if recovery_data:
        print("🔄 Auto-save recovery data found")

    form = DocumentForm()
    print(f"📝 Form Created: {type(form)}")

    # Pre-populate form with recovery data if available
    if recovery_data and request.method == 'GET':
        form.title.data = recovery_data.get('title', '')
        form.document_type.data = recovery_data.get('document_type', '')
        form.description.data = recovery_data.get('description', '')
        form.tags.data = recovery_data.get('tags', '')
        flash('تم استرداد بيانات محفوظة تلقائياً', 'info')

    # CRITICAL FIX: Guaranteed loading of all 24 document types
    print("🔍 CRITICAL: Loading all 24 document types from database...")

    try:
        # Load all document types from database
        document_types = DocumentType.query.order_by(DocumentType.name).all()
        total_types = len(document_types) if document_types else 0

        print(f"📊 FOUND {total_types} document types in database")

        # Build choices list starting with default option
        choices = [('', 'اختر نوع الوثيقة')]

        if document_types and total_types >= 24:
            # Add all document types from database
            for dt in document_types:
                choices.append((dt.name, dt.name))

            print(f"✅ SUCCESS: Loaded {total_types} document types from database")
            print("📋 ALL 24+ DOCUMENT TYPES LOADED:")
            for i, dt in enumerate(document_types, 1):
                print(f"   {i:2d}. {dt.name}")

        else:
            print(f"⚠️ WARNING: Only {total_types} types in database, using guaranteed fallback")
            # GUARANTEED FALLBACK: All 24 required document types
            guaranteed_types = [
                'وثائق إدارية', 'قرارات', 'تعاميم', 'مذكرات',
                'وثائق مالية', 'فواتير', 'ميزانيات', 'مقاولات',
                'وثائق قانونية', 'عقود', 'اتفاقيات', 'قوانين',
                'وثائق فنية', 'تقارير', 'دراسات', 'مخططات',
                'مراسلات', 'خطابات', 'برقيات', 'فاكسات',
                'شهادات', 'تراخيص', 'محاضر', 'استمارات'
            ]

            for doc_type in guaranteed_types:
                choices.append((doc_type, doc_type))

            print(f"✅ FALLBACK: Using guaranteed list of {len(guaranteed_types)} document types")

        # CRITICAL: Set choices to form
        form.document_type.choices = choices
        total_choices = len(choices) - 1  # Exclude default option

        print(f"📋 TOTAL DOCUMENT TYPE OPTIONS: {total_choices}")
        print(f"📋 FIRST 5 CHOICES: {choices[:6]}")  # Show first 5 + default

        # VERIFICATION
        if total_choices >= 24:
            print("🎉 CRITICAL SUCCESS: 24+ document types available in dropdown!")
        else:
            print(f"❌ CRITICAL ERROR: Only {total_choices} types available, need 24+")

    except Exception as e:
        print(f"❌ CRITICAL ERROR loading document types: {e}")
        logger.error(f"❌ Critical error loading document types: {e}")
        import traceback
        traceback.print_exc()

        # EMERGENCY FALLBACK: Guaranteed 24 types
        emergency_choices = [
            ('', 'اختر نوع الوثيقة'),
            ('وثائق إدارية', 'وثائق إدارية'),
            ('قرارات', 'قرارات'),
            ('تعاميم', 'تعاميم'),
            ('مذكرات', 'مذكرات'),
            ('وثائق مالية', 'وثائق مالية'),
            ('فواتير', 'فواتير'),
            ('ميزانيات', 'ميزانيات'),
            ('مقاولات', 'مقاولات'),
            ('وثائق قانونية', 'وثائق قانونية'),
            ('عقود', 'عقود'),
            ('اتفاقيات', 'اتفاقيات'),
            ('قوانين', 'قوانين'),
            ('وثائق فنية', 'وثائق فنية'),
            ('تقارير', 'تقارير'),
            ('دراسات', 'دراسات'),
            ('مخططات', 'مخططات'),
            ('مراسلات', 'مراسلات'),
            ('خطابات', 'خطابات'),
            ('برقيات', 'برقيات'),
            ('فاكسات', 'فاكسات'),
            ('شهادات', 'شهادات'),
            ('تراخيص', 'تراخيص'),
            ('محاضر', 'محاضر'),
            ('استمارات', 'استمارات')
        ]

        form.document_type.choices = emergency_choices
        print(f"🚨 EMERGENCY: Using guaranteed 24 document types")

    # معالجة النموذج عند الإرسال
    if form.validate_on_submit():
        try:
            # إنشاء الوثيقة
            document = Document(
                title=form.title.data.strip(),
                description=form.description.data.strip() if form.description.data else '',
                document_type=form.document_type.data,
                tags=form.tags.data.strip() if form.tags.data else '',
                created_by=current_user.id,
                status='نشط'
            )

            # معالجة الملف المرفق
            if form.file.data and hasattr(form.file.data, 'filename') and form.file.data.filename:
                file = form.file.data
                if allowed_file(file.filename):
                    try:
                        # التأكد من وجود مجلد الرفع
                        os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

                        # إنشاء اسم ملف فريد
                        timestamp = int(time.time())
                        filename = f"{timestamp}_{secure_filename(file.filename)}"
                        file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)

                        # حفظ الملف
                        file.save(file_path)

                        # تحديث معلومات الوثيقة
                        document.file_name = file.filename
                        document.file_path = file_path
                        document.file_size = os.path.getsize(file_path)

                        logger.info(f"✅ تم حفظ الملف: {filename}")
                    except Exception as file_error:
                        logger.error(f"❌ خطأ في حفظ الملف: {file_error}")
                        flash('حدث خطأ في حفظ الملف المرفق', 'warning')

            # CRITICAL: AUTO-SAVE IMPLEMENTATION
            try:
                print("🔄 CRITICAL: Starting auto-save process...")

                # Primary save to database
                db.session.add(document)
                db.session.flush()  # Get document ID without committing

                print(f"📝 CRITICAL: Document ID assigned: {document.id}")

                # Create backup entry for recovery
                backup_data = {
                    'document_id': document.id,
                    'title': document.title,
                    'document_type': document.document_type,
                    'description': document.description,
                    'tags': document.tags,
                    'file_path': document.file_path,
                    'created_at': datetime.utcnow().isoformat(),
                    'created_by': current_user.id if current_user.is_authenticated else None,
                    'auto_save_timestamp': datetime.utcnow().isoformat()
                }

                # Save backup to file system for persistence
                backup_dir = os.path.join(app.root_path, 'backups', 'documents')
                os.makedirs(backup_dir, exist_ok=True)
                backup_file = os.path.join(backup_dir, f'doc_{document.id}_backup.json')

                with open(backup_file, 'w', encoding='utf-8') as f:
                    import json
                    json.dump(backup_data, f, ensure_ascii=False, indent=2)

                print(f"💾 CRITICAL: Backup saved to: {backup_file}")

                # Commit the transaction
                db.session.commit()
                print("✅ CRITICAL: Document committed to database")

                # Verify save was successful
                saved_doc = Document.query.get(document.id)
                if saved_doc:
                    print("✅ CRITICAL: Auto-save verification successful")
                else:
                    raise Exception("Auto-save verification failed")

            except Exception as save_error:
                print(f"❌ CRITICAL: Auto-save failed: {save_error}")
                db.session.rollback()

                # Try emergency save
                try:
                    emergency_backup = {
                        'title': form.title.data,
                        'document_type': form.document_type.data,
                        'description': form.description.data,
                        'tags': form.tags.data,
                        'emergency_save': True,
                        'timestamp': datetime.utcnow().isoformat()
                    }

                    emergency_dir = os.path.join(app.root_path, 'emergency_saves')
                    os.makedirs(emergency_dir, exist_ok=True)
                    emergency_file = os.path.join(emergency_dir, f'emergency_{int(time.time())}.json')

                    with open(emergency_file, 'w', encoding='utf-8') as f:
                        json.dump(emergency_backup, f, ensure_ascii=False, indent=2)

                    print(f"🚨 EMERGENCY: Data saved to: {emergency_file}")
                    flash('حدث خطأ في الحفظ، تم حفظ البيانات في نسخة احتياطية طارئة', 'warning')

                except Exception as emergency_error:
                    print(f"❌ EMERGENCY SAVE FAILED: {emergency_error}")
                    flash('فشل في حفظ الوثيقة والنسخة الاحتياطية', 'error')
                    return render_template('documents/add_new.html', form=form)

                raise save_error

            # تسجيل النشاط
            try:
                log_activity('create', 'document', document.id, document.title,
                           f'إضافة وثيقة جديدة: {document.title} (Auto-saved)')
            except Exception:
                pass  # تجاهل أخطاء تسجيل النشاط

            logger.info(f"✅ تم إضافة وثيقة جديدة مع الحفظ التلقائي: {document.title}")
            flash('تم حفظ الوثيقة تلقائياً بنجاح', 'success')

            # إعادة توجيه إلى صفحة عرض الوثيقة
            return redirect(url_for('view_document', id=document.id))

        except Exception as e:
            db.session.rollback()
            logger.error(f"❌ خطأ في إضافة الوثيقة: {e}")
            flash('حدث خطأ في إضافة الوثيقة. يرجى المحاولة مرة أخرى.', 'error')

    # عرض النموذج - التحقق النهائي
    print("🎯 FINAL TEMPLATE RENDERING")
    print(f"📊 Final form choices count: {len(form.document_type.choices)}")
    print(f"📋 First 5 choices: {form.document_type.choices[:5]}")

    # التحقق من جودة البيانات
    choices_count = len(form.document_type.choices) - 1  # -1 للخيار الافتراضي
    if choices_count >= 20:
        print("🎉 EXCELLENT: 20+ document types available")
    elif choices_count >= 10:
        print("✅ GOOD: 10+ document types available")
    elif choices_count >= 5:
        print("⚠️ FAIR: 5+ document types available")
    else:
        print("❌ POOR: Less than 5 document types available")

    try:
        print("🔄 CRITICAL FIX: Rendering template: documents/add_new.html")
        result = render_template('documents/add_new.html', form=form)
        print("✅ CRITICAL SUCCESS: Template rendered successfully")
        print("🎉 CRITICAL FIX COMPLETE: ADD DOCUMENT PAGE READY!")
        return result

    except Exception as e:
        print(f"❌ CRITICAL ERROR rendering template: {e}")
        logger.error(f"❌ Template rendering error: {e}")
        import traceback
        traceback.print_exc()

        # إرجاع صفحة خطأ مفيدة
        error_html = f"""
        <html>
        <head><title>خطأ في النظام</title></head>
        <body style="font-family: Arial; text-align: center; padding: 50px;">
            <h1>❌ خطأ في عرض صفحة إضافة الوثائق</h1>
            <p>تفاصيل الخطأ: {e}</p>
            <p><a href="/documents">العودة لقائمة الوثائق</a></p>
        </body>
        </html>
        """
        return error_html, 500

@app.route('/documents/<int:id>')
@login_required
def view_document(id):
    """عرض تفاصيل الوثيقة"""
    document = Document.query.get_or_404(id)
    return render_template('documents/view.html', document=document)

@app.route('/documents/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit_document(id):
    """تعديل الوثيقة - محسن مع أنواع الوثائق من قاعدة البيانات"""
    document = Document.query.get_or_404(id)

    # التحقق من الصلاحيات
    if not current_user.is_admin() and document.created_by != current_user.id:
        flash('ليس لديك صلاحية لتعديل هذه الوثيقة', 'error')
        return redirect(url_for('documents'))

    form = DocumentForm(obj=document)

    # تحديث خيارات أنواع الوثائق من قاعدة البيانات
    try:
        document_types = DocumentType.query.all()
        if document_types:
            form.document_type.choices = [('', 'اختر نوع الوثيقة')] + [
                (dt.name, dt.name) for dt in document_types
            ]
        else:
            # في حالة عدم وجود أنواع في قاعدة البيانات، استخدم القائمة الافتراضية
            form.document_type.choices = [
                ('', 'اختر نوع الوثيقة'),
                ('وثيقة رسمية', 'وثيقة رسمية'),
                ('كتاب رسمي', 'كتاب رسمي'),
                ('مذكرة داخلية', 'مذكرة داخلية'),
                ('تقرير', 'تقرير'),
                ('مراسلة', 'مراسلة'),
                ('عقد', 'عقد'),
                ('فاتورة', 'فاتورة'),
                ('أخرى', 'أخرى')
            ]
    except Exception:
        # في حالة الخطأ، استخدم القائمة الافتراضية
        form.document_type.choices = [
            ('', 'اختر نوع الوثيقة'),
            ('وثيقة رسمية', 'وثيقة رسمية'),
            ('تقرير', 'تقرير'),
            ('مراسلة', 'مراسلة'),
            ('عقد', 'عقد'),
            ('فاتورة', 'فاتورة'),
            ('أخرى', 'أخرى')
        ]

    if form.validate_on_submit():
        try:
            document.title = form.title.data
            document.description = form.description.data
            document.document_type = form.document_type.data
            document.tags = form.tags.data
            document.updated_at = datetime.utcnow()

            # معالجة الملف الجديد إذا تم رفعه
            if form.file.data:
                file = form.file.data
                if file and allowed_file(file.filename):
                    # حذف الملف القديم
                    if document.file_path and os.path.exists(document.file_path):
                        os.remove(document.file_path)

                    filename = secure_filename(file.filename)
                    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S_')
                    filename = timestamp + filename

                    file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
                    file.save(file_path)

                    document.file_name = file.filename
                    document.file_path = file_path
                    document.file_size = os.path.getsize(file_path)

            db.session.commit()

            logger.info(f"تم تعديل الوثيقة: {document.title} بواسطة {current_user.username}")
            flash('تم تعديل الوثيقة بنجاح', 'success')
            return redirect(url_for('view_document', id=document.id))

        except Exception as e:
            db.session.rollback()
            logger.error(f"خطأ في تعديل الوثيقة: {e}")
            flash('حدث خطأ في تعديل الوثيقة', 'error')

    return render_template('documents/edit.html', form=form, document=document)

@app.route('/documents/<int:id>/delete', methods=['POST'])
@login_required
def delete_document(id):
    """حذف الوثيقة"""
    document = Document.query.get_or_404(id)

    # التحقق من الصلاحيات
    if not current_user.is_admin() and document.created_by != current_user.id:
        flash('ليس لديك صلاحية لحذف هذه الوثيقة', 'error')
        return redirect(url_for('documents'))

    try:
        # حذف الملف المرفق
        if document.file_path and os.path.exists(document.file_path):
            os.remove(document.file_path)

        title = document.title
        db.session.delete(document)
        db.session.commit()

        logger.info(f"تم حذف الوثيقة: {title} بواسطة {current_user.username}")
        flash('تم حذف الوثيقة بنجاح', 'success')

    except Exception as e:
        db.session.rollback()
        logger.error(f"خطأ في حذف الوثيقة: {e}")
        flash('حدث خطأ في حذف الوثيقة', 'error')

    return redirect(url_for('documents'))

# مسارات الكتب الواردة
@app.route('/incoming')
@login_required
def incoming_documents():
    """عرض الكتب الواردة"""
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '', type=str)
    status = request.args.get('status', '', type=str)
    priority = request.args.get('priority', '', type=str)

    query = IncomingDocument.query

    if search:
        query = query.filter(IncomingDocument.subject.contains(search) |
                           IncomingDocument.sender_name.contains(search))
    if status:
        query = query.filter(IncomingDocument.status == status)
    if priority:
        query = query.filter(IncomingDocument.priority == priority)

    documents = query.order_by(IncomingDocument.received_date.desc()).paginate(
        page=page, per_page=10, error_out=False
    )

    return render_template('incoming/list.html', documents=documents,
                         search=search, status=status, priority=priority)

@app.route('/incoming/add', methods=['GET', 'POST'])
@login_required
def add_incoming():
    """إضافة كتاب وارد جديد"""
    form = IncomingForm()

    if form.validate_on_submit():
        try:
            incoming_number = form.incoming_number.data or generate_incoming_number()

            existing = IncomingDocument.query.filter_by(incoming_number=incoming_number).first()
            if existing:
                flash('رقم الوارد موجود مسبقاً', 'error')
                return render_template('incoming/add.html', form=form)

            document = IncomingDocument(
                incoming_number=incoming_number,
                sender_name=form.sender_name.data,
                subject=form.subject.data,
                received_date=form.received_date.data,
                priority=form.priority.data,
                status='جديد',
                notes=form.notes.data,
                received_by=current_user.id
            )

            # معالجة الملف المرفق
            if form.file.data:
                file_info = save_file(form.file.data, 'incoming')
                if file_info:
                    document.file_name = file_info['original_name']
                    document.file_path = file_info['file_path']
                    document.file_size = file_info['file_size']
                    document.file_type = file_info['file_type']

            db.session.add(document)
            db.session.commit()

            logger.info(f"تم إضافة كتاب وارد جديد: {document.incoming_number}")
            flash('تم إضافة الكتاب الوارد بنجاح', 'success')
            return redirect(url_for('incoming_documents'))

        except Exception as e:
            db.session.rollback()
            logger.error(f"خطأ في إضافة الكتاب الوارد: {e}")
            flash('حدث خطأ في إضافة الكتاب الوارد', 'error')

    return render_template('incoming/add.html', form=form)

@app.route('/incoming/<int:id>')
@login_required
def view_incoming(id):
    """عرض تفاصيل الكتاب الوارد"""
    document = IncomingDocument.query.get_or_404(id)
    return render_template('incoming/view.html', document=document)

@app.route('/incoming/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit_incoming(id):
    """تعديل الكتاب الوارد"""
    document = IncomingDocument.query.get_or_404(id)

    if not current_user.is_admin() and document.received_by != current_user.id:
        flash('ليس لديك صلاحية لتعديل هذا الكتاب', 'error')
        return redirect(url_for('incoming_documents'))

    form = IncomingForm(obj=document)

    if form.validate_on_submit():
        try:
            if form.incoming_number.data != document.incoming_number:
                existing = IncomingDocument.query.filter_by(incoming_number=form.incoming_number.data).first()
                if existing:
                    flash('رقم الوارد موجود مسبقاً', 'error')
                    return render_template('incoming/edit.html', form=form, document=document)

            document.incoming_number = form.incoming_number.data
            document.sender_name = form.sender_name.data
            document.subject = form.subject.data
            document.received_date = form.received_date.data
            document.priority = form.priority.data
            document.status = request.form.get('status', 'جديد')
            document.notes = form.notes.data

            # معالجة الملف المرفق الجديد
            if form.file.data:
                # حذف الملف القديم إذا كان موجوداً
                if document.file_path:
                    delete_file(document.file_path)

                # حفظ الملف الجديد
                file_info = save_file(form.file.data, 'incoming')
                if file_info:
                    document.file_name = file_info['original_name']
                    document.file_path = file_info['file_path']
                    document.file_size = file_info['file_size']
                    document.file_type = file_info['file_type']

            db.session.commit()

            logger.info(f"تم تعديل الكتاب الوارد: {document.incoming_number}")
            flash('تم تعديل الكتاب الوارد بنجاح', 'success')
            return redirect(url_for('view_incoming', id=document.id))

        except Exception as e:
            db.session.rollback()
            logger.error(f"خطأ في تعديل الكتاب الوارد: {e}")
            flash('حدث خطأ في تعديل الكتاب الوارد', 'error')

    return render_template('incoming/edit.html', form=form, document=document)

@app.route('/incoming/<int:id>/delete', methods=['POST'])
@login_required
def delete_incoming(id):
    """حذف الكتاب الوارد"""
    document = IncomingDocument.query.get_or_404(id)

    if not current_user.is_admin() and document.received_by != current_user.id:
        flash('ليس لديك صلاحية لحذف هذا الكتاب', 'error')
        return redirect(url_for('incoming_documents'))

    try:
        incoming_number = document.incoming_number

        # حذف الملف المرفق إذا كان موجوداً
        if document.file_path:
            delete_file(document.file_path)

        db.session.delete(document)
        db.session.commit()

        logger.info(f"تم حذف الكتاب الوارد: {incoming_number}")
        flash('تم حذف الكتاب الوارد بنجاح', 'success')

    except Exception as e:
        db.session.rollback()
        logger.error(f"خطأ في حذف الكتاب الوارد: {e}")
        flash('حدث خطأ في حذف الكتاب الوارد', 'error')

    return redirect(url_for('incoming_documents'))

# مسارات الكتب الصادرة
@app.route('/outgoing')
@login_required
def outgoing_documents():
    """عرض الكتب الصادرة"""
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '', type=str)
    status = request.args.get('status', '', type=str)
    priority = request.args.get('priority', '', type=str)

    query = OutgoingDocument.query

    if search:
        query = query.filter(OutgoingDocument.subject.contains(search) |
                           OutgoingDocument.recipient_name.contains(search))
    if status:
        query = query.filter(OutgoingDocument.status == status)
    if priority:
        query = query.filter(OutgoingDocument.priority == priority)

    documents = query.order_by(OutgoingDocument.sent_date.desc()).paginate(
        page=page, per_page=10, error_out=False
    )

    return render_template('outgoing/list.html', documents=documents,
                         search=search, status=status, priority=priority)

@app.route('/outgoing/add', methods=['GET', 'POST'])
@login_required
def add_outgoing():
    """إضافة كتاب صادر جديد"""
    form = OutgoingForm()

    if form.validate_on_submit():
        try:
            outgoing_number = form.outgoing_number.data or generate_outgoing_number()

            existing = OutgoingDocument.query.filter_by(outgoing_number=outgoing_number).first()
            if existing:
                flash('رقم الصادر موجود مسبقاً', 'error')
                return render_template('outgoing/add.html', form=form)

            document = OutgoingDocument(
                outgoing_number=outgoing_number,
                recipient_name=form.recipient_name.data,
                subject=form.subject.data,
                sent_date=form.sent_date.data,
                priority=form.priority.data,
                status='مسودة',
                notes=form.notes.data,
                prepared_by=current_user.id
            )

            # معالجة الملف المرفق
            if form.file.data:
                file_info = save_file(form.file.data, 'outgoing')
                if file_info:
                    document.file_name = file_info['original_name']
                    document.file_path = file_info['file_path']
                    document.file_size = file_info['file_size']
                    document.file_type = file_info['file_type']

            db.session.add(document)
            db.session.commit()

            logger.info(f"تم إضافة كتاب صادر جديد: {document.outgoing_number}")
            flash('تم إضافة الكتاب الصادر بنجاح', 'success')
            return redirect(url_for('outgoing_documents'))

        except Exception as e:
            db.session.rollback()
            logger.error(f"خطأ في إضافة الكتاب الصادر: {e}")
            flash('حدث خطأ في إضافة الكتاب الصادر', 'error')

    return render_template('outgoing/add.html', form=form)

@app.route('/outgoing/<int:id>')
@login_required
def view_outgoing(id):
    """عرض تفاصيل الكتاب الصادر"""
    document = OutgoingDocument.query.get_or_404(id)
    return render_template('outgoing/view.html', document=document)

@app.route('/outgoing/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit_outgoing(id):
    """تعديل الكتاب الصادر"""
    document = OutgoingDocument.query.get_or_404(id)

    if not current_user.is_admin() and document.prepared_by != current_user.id:
        flash('ليس لديك صلاحية لتعديل هذا الكتاب', 'error')
        return redirect(url_for('outgoing_documents'))

    form = OutgoingForm(obj=document)

    if form.validate_on_submit():
        try:
            if form.outgoing_number.data != document.outgoing_number:
                existing = OutgoingDocument.query.filter_by(outgoing_number=form.outgoing_number.data).first()
                if existing:
                    flash('رقم الصادر موجود مسبقاً', 'error')
                    return render_template('outgoing/edit.html', form=form, document=document)

            document.outgoing_number = form.outgoing_number.data
            document.recipient_name = form.recipient_name.data
            document.subject = form.subject.data
            document.sent_date = form.sent_date.data
            document.priority = form.priority.data
            document.status = request.form.get('status', 'مسودة')
            document.notes = form.notes.data

            # معالجة الملف المرفق الجديد
            if form.file.data:
                # حذف الملف القديم إذا كان موجوداً
                if document.file_path:
                    delete_file(document.file_path)

                # حفظ الملف الجديد
                file_info = save_file(form.file.data, 'outgoing')
                if file_info:
                    document.file_name = file_info['original_name']
                    document.file_path = file_info['file_path']
                    document.file_size = file_info['file_size']
                    document.file_type = file_info['file_type']

            db.session.commit()

            logger.info(f"تم تعديل الكتاب الصادر: {document.outgoing_number}")
            flash('تم تعديل الكتاب الصادر بنجاح', 'success')
            return redirect(url_for('view_outgoing', id=document.id))

        except Exception as e:
            db.session.rollback()
            logger.error(f"خطأ في تعديل الكتاب الصادر: {e}")
            flash('حدث خطأ في تعديل الكتاب الصادر', 'error')

    return render_template('outgoing/edit.html', form=form, document=document)

@app.route('/outgoing/<int:id>/delete', methods=['POST'])
@login_required
def delete_outgoing(id):
    """حذف الكتاب الصادر"""
    document = OutgoingDocument.query.get_or_404(id)

    if not current_user.is_admin() and document.prepared_by != current_user.id:
        flash('ليس لديك صلاحية لحذف هذا الكتاب', 'error')
        return redirect(url_for('outgoing_documents'))

    try:
        outgoing_number = document.outgoing_number

        # حذف الملف المرفق إذا كان موجوداً
        if document.file_path:
            delete_file(document.file_path)

        db.session.delete(document)
        db.session.commit()

        logger.info(f"تم حذف الكتاب الصادر: {outgoing_number}")
        flash('تم حذف الكتاب الصادر بنجاح', 'success')

    except Exception as e:
        db.session.rollback()
        logger.error(f"خطأ في حذف الكتاب الصادر: {e}")
        flash('حدث خطأ في حذف الكتاب الصادر', 'error')

    return redirect(url_for('outgoing_documents'))

# مسارات البحث والتقارير
@app.route('/search')
@login_required
def search():
    """صفحة البحث المتقدم"""
    query = request.args.get('q', '', type=str)
    doc_type = request.args.get('type', '', type=str)
    date_from = request.args.get('date_from', '', type=str)
    date_to = request.args.get('date_to', '', type=str)

    results = {
        'documents': [],
        'incoming': [],
        'outgoing': [],
        'total': 0
    }

    if query:
        # البحث في الوثائق
        doc_query = Document.query.filter(
            Document.title.contains(query) |
            Document.description.contains(query) |
            Document.tags.contains(query)
        )

        if doc_type:
            doc_query = doc_query.filter(Document.document_type == doc_type)

        if date_from:
            doc_query = doc_query.filter(Document.created_at >= datetime.strptime(date_from, '%Y-%m-%d'))

        if date_to:
            doc_query = doc_query.filter(Document.created_at <= datetime.strptime(date_to, '%Y-%m-%d'))

        results['documents'] = doc_query.limit(20).all()

        # البحث في الكتب الواردة
        incoming_query = IncomingDocument.query.filter(
            IncomingDocument.subject.contains(query) |
            IncomingDocument.sender_name.contains(query)
        )

        if date_from:
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d')
            incoming_query = incoming_query.filter(IncomingDocument.received_date >= date_from_obj)

        if date_to:
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d')
            incoming_query = incoming_query.filter(IncomingDocument.received_date <= date_to_obj)

        results['incoming'] = incoming_query.limit(20).all()

        # البحث في الكتب الصادرة
        outgoing_query = OutgoingDocument.query.filter(
            OutgoingDocument.subject.contains(query) |
            OutgoingDocument.recipient_name.contains(query)
        )

        if date_from:
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d')
            outgoing_query = outgoing_query.filter(OutgoingDocument.sent_date >= date_from_obj)

        if date_to:
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d')
            outgoing_query = outgoing_query.filter(OutgoingDocument.sent_date <= date_to_obj)

        results['outgoing'] = outgoing_query.limit(20).all()

        results['total'] = len(results['documents']) + len(results['incoming']) + len(results['outgoing'])

    return render_template('search.html', results=results, query=query,
                         doc_type=doc_type, date_from=date_from, date_to=date_to)



@app.route('/profile')
@login_required
def profile():
    """ENHANCED: صفحة الملف الشخصي المحسنة"""
    try:
        print(f"🚀 PROFILE: Loading profile for user {current_user.username}")

        # Get user statistics
        user_stats = {
            'documents_created': Document.query.filter_by(created_by=current_user.id).count(),
            'incoming_processed': IncomingDocument.query.filter_by(received_by=current_user.id).count(),
            'outgoing_prepared': OutgoingDocument.query.filter_by(prepared_by=current_user.id).count(),
            'signatures_created': DigitalSignature.query.filter_by(user_id=current_user.id).count(),
            'last_login': current_user.last_login,
            'account_created': current_user.created_at,
            'total_activities': ActivityLog.query.filter_by(user_id=current_user.id).count()
        }

        # Get recent activities
        recent_activities = ActivityLog.query.filter_by(user_id=current_user.id)\
            .order_by(ActivityLog.created_at.desc()).limit(10).all()

        # Get user preferences (this would come from user_preferences table)
        user_preferences = {
            'language': 'ar',
            'theme': 'light',
            'notifications_enabled': True,
            'email_notifications': True,
            'dashboard_layout': 'default'
        }

        print(f"✅ PROFILE: Profile loaded successfully for {current_user.username}")

        return render_template('profile/index.html',
                             user=current_user,
                             user_stats=user_stats,
                             recent_activities=recent_activities,
                             user_preferences=user_preferences)

    except Exception as e:
        logger.error(f"خطأ في تحميل الملف الشخصي: {e}")
        flash('حدث خطأ في تحميل الملف الشخصي', 'error')
        return render_template('profile/index.html',
                             user=current_user,
                             user_stats={},
                             recent_activities=[],
                             user_preferences={})

@app.route('/profile/edit', methods=['GET', 'POST'])
@login_required
def edit_profile():
    """CRITICAL: تعديل الملف الشخصي"""
    try:
        print(f"🔄 PROFILE: Editing profile for user {current_user.username}")

        if request.method == 'POST':
            # Get form data
            full_name = request.form.get('full_name', '').strip()
            email = request.form.get('email', '').strip()
            department = request.form.get('department', '').strip()
            current_password = request.form.get('current_password', '')
            new_password = request.form.get('new_password', '')
            confirm_password = request.form.get('confirm_password', '')

            # Validation
            if not full_name:
                flash('الاسم الكامل مطلوب', 'error')
                return render_template('profile/edit.html', user=current_user)

            if not email:
                flash('البريد الإلكتروني مطلوب', 'error')
                return render_template('profile/edit.html', user=current_user)

            # Check if email is already used by another user
            existing_user = User.query.filter(User.email == email, User.id != current_user.id).first()
            if existing_user:
                flash('البريد الإلكتروني مستخدم من قبل مستخدم آخر', 'error')
                return render_template('profile/edit.html', user=current_user)

            # Update basic info
            current_user.full_name = full_name
            current_user.email = email
            current_user.department = department

            # Handle password change
            if new_password:
                if not current_password:
                    flash('كلمة المرور الحالية مطلوبة لتغيير كلمة المرور', 'error')
                    return render_template('profile/edit.html', user=current_user)

                if not current_user.check_password(current_password):
                    flash('كلمة المرور الحالية غير صحيحة', 'error')
                    return render_template('profile/edit.html', user=current_user)

                if new_password != confirm_password:
                    flash('كلمة المرور الجديدة وتأكيدها غير متطابقين', 'error')
                    return render_template('profile/edit.html', user=current_user)

                if len(new_password) < 6:
                    flash('كلمة المرور يجب أن تكون 6 أحرف على الأقل', 'error')
                    return render_template('profile/edit.html', user=current_user)

                current_user.set_password(new_password)
                print(f"🔐 PROFILE: Password updated for user {current_user.username}")

            # Save changes
            db.session.commit()

            # Log activity
            log_activity('update', 'profile', current_user.id,
                        f'تحديث الملف الشخصي للمستخدم: {current_user.username}')

            print(f"✅ PROFILE: Profile updated successfully for {current_user.username}")
            flash('تم تحديث الملف الشخصي بنجاح', 'success')
            return redirect(url_for('profile'))

        return render_template('profile/edit.html', user=current_user)

    except Exception as e:
        db.session.rollback()
        logger.error(f"خطأ في تعديل الملف الشخصي: {e}")
        flash('حدث خطأ في تحديث الملف الشخصي', 'error')
        return render_template('profile/edit.html', user=current_user)

@app.route('/profile/preferences', methods=['GET', 'POST'])
@login_required
def profile_preferences():
    """ENHANCED: إعدادات تفضيلات المستخدم"""
    try:
        print(f"🔄 PROFILE: Managing preferences for user {current_user.username}")

        if request.method == 'POST':
            # Get preferences from form
            language = request.form.get('language', 'ar')
            theme = request.form.get('theme', 'light')
            notifications_enabled = request.form.get('notifications_enabled') == 'on'
            email_notifications = request.form.get('email_notifications') == 'on'
            dashboard_layout = request.form.get('dashboard_layout', 'default')

            # This would typically save to a user_preferences table
            # For now, we'll store in session or user tags
            preferences = {
                'language': language,
                'theme': theme,
                'notifications_enabled': notifications_enabled,
                'email_notifications': email_notifications,
                'dashboard_layout': dashboard_layout
            }

            # Store preferences in session for now
            session['user_preferences'] = preferences

            # Log activity
            log_activity('update', 'preferences', current_user.id,
                        f'تحديث تفضيلات المستخدم: {current_user.username}')

            print(f"✅ PROFILE: Preferences updated for {current_user.username}")
            flash('تم حفظ التفضيلات بنجاح', 'success')
            return redirect(url_for('profile'))

        # Get current preferences
        current_preferences = session.get('user_preferences', {
            'language': 'ar',
            'theme': 'light',
            'notifications_enabled': True,
            'email_notifications': True,
            'dashboard_layout': 'default'
        })

        return render_template('profile/preferences.html',
                             user=current_user,
                             preferences=current_preferences)

    except Exception as e:
        logger.error(f"خطأ في إدارة التفضيلات: {e}")
        flash('حدث خطأ في إدارة التفضيلات', 'error')
        return redirect(url_for('profile'))

# مسارات التقارير
@app.route('/reports')
@login_required
def reports():
    """صفحة التقارير الرئيسية"""
    if not current_user.is_admin():
        flash('ليس لديك صلاحية للوصول إلى التقارير', 'error')
        return redirect(url_for('dashboard'))

    # إحصائيات سريعة للتقارير
    stats = {
        'total_documents': Document.query.count(),
        'total_incoming': IncomingDocument.query.count(),
        'total_outgoing': OutgoingDocument.query.count(),
        'total_users': User.query.count(),
        'documents_this_month': Document.query.filter(
            extract('month', Document.created_at) == datetime.now().month,
            extract('year', Document.created_at) == datetime.now().year
        ).count(),
        'incoming_this_month': IncomingDocument.query.filter(
            extract('month', IncomingDocument.created_at) == datetime.now().month,
            extract('year', IncomingDocument.created_at) == datetime.now().year
        ).count(),
        'outgoing_this_month': OutgoingDocument.query.filter(
            extract('month', OutgoingDocument.created_at) == datetime.now().month,
            extract('year', OutgoingDocument.created_at) == datetime.now().year
        ).count()
    }

    # ENHANCED: Add comprehensive report data
    try:
        # Get available report templates
        report_templates = get_report_templates()

        # Get recent reports
        recent_reports = get_recent_reports()

        # Get report statistics
        report_stats = get_report_statistics()

        # Get activity-based reports
        activity_reports = get_activity_based_reports()

        print(f"✅ REPORTS: Enhanced data loaded - {len(report_templates)} templates")

        return render_template('reports/index.html',
                             stats=stats,
                             report_templates=report_templates,
                             recent_reports=recent_reports,
                             report_stats=report_stats,
                             activity_reports=activity_reports)

    except Exception as e:
        logger.error(f"خطأ في تحميل بيانات التقارير المحسنة: {e}")
        return render_template('reports/index.html', stats=stats)

@app.route('/reports/generate', methods=['GET', 'POST'])
@login_required
def generate_report():
    """توليد التقارير"""
    if not current_user.is_admin():
        flash('ليس لديك صلاحية لتوليد التقارير', 'error')
        return redirect(url_for('dashboard'))

    form = ReportForm()

    # تحديث خيارات المستخدمين
    try:
        users = User.query.all()
        form.user_filter.choices = [('', 'جميع المستخدمين')] + [(str(u.id), u.full_name) for u in users]
    except Exception as e:
        logger.error(f"خطأ في تحميل المستخدمين: {e}")
        form.user_filter.choices = [('', 'جميع المستخدمين')]

    if form.validate_on_submit():
        try:
            report_data = generate_report_data(form)

            if form.export_format.data == 'html':
                return render_template('reports/view.html',
                                     report_data=report_data,
                                     form=form)
            elif form.export_format.data == 'pdf':
                return export_pdf_report(report_data, form)
            elif form.export_format.data == 'excel':
                return export_excel_report(report_data, form)
            elif form.export_format.data == 'csv':
                return export_csv_report(report_data, form)

        except Exception as e:
            logger.error(f"خطأ في توليد التقرير: {e}")
            flash('حدث خطأ في توليد التقرير', 'error')

    # ENHANCED: Add available templates to form
    templates = get_report_templates()

    return render_template('reports/generate.html', form=form, templates=templates)

@app.route('/reports/statistics')
@login_required
def statistics():
    """صفحة الإحصائيات المتقدمة"""
    if not current_user.is_admin():
        flash('ليس لديك صلاحية للوصول إلى الإحصائيات', 'error')
        return redirect(url_for('dashboard'))

    # إحصائيات شاملة
    stats = get_comprehensive_statistics()

    return render_template('reports/statistics.html', stats=stats)

# مسارات إدارة أنواع الوثائق
@app.route('/document-types')
@login_required
def document_types():
    """عرض أنواع الوثائق"""
    if not current_user.is_admin():
        flash('ليس لديك صلاحية للوصول إلى إدارة أنواع الوثائق', 'error')
        return redirect(url_for('dashboard'))

    types = DocumentType.query.order_by(DocumentType.name).all()
    return render_template('admin/document_types.html', types=types)

@app.route('/document-types/add', methods=['GET', 'POST'])
@login_required
def add_document_type():
    """إضافة نوع وثيقة جديد"""
    if not current_user.is_admin():
        flash('ليس لديك صلاحية لإضافة أنواع الوثائق', 'error')
        return redirect(url_for('dashboard'))

    if request.method == 'POST':
        name = request.form.get('name', '').strip()
        description = request.form.get('description', '').strip()

        if not name:
            flash('يرجى إدخال اسم نوع الوثيقة', 'error')
        else:
            # التحقق من عدم وجود نوع بنفس الاسم
            existing = DocumentType.query.filter_by(name=name).first()
            if existing:
                flash('نوع الوثيقة موجود مسبقاً', 'error')
            else:
                try:
                    doc_type = DocumentType(name=name, description=description)
                    db.session.add(doc_type)
                    db.session.commit()

                    # تسجيل النشاط
                    log_activity('create', 'document_type', doc_type.id, doc_type.name)

                    flash('تم إضافة نوع الوثيقة بنجاح', 'success')
                    return redirect(url_for('document_types'))
                except Exception as e:
                    db.session.rollback()
                    logger.error(f"خطأ في إضافة نوع الوثيقة: {e}")
                    flash('حدث خطأ في إضافة نوع الوثيقة', 'error')

    return render_template('admin/add_document_type.html')

@app.route('/document-types/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit_document_type(id):
    """تعديل نوع الوثيقة"""
    if not current_user.is_admin():
        flash('ليس لديك صلاحية لتعديل أنواع الوثائق', 'error')
        return redirect(url_for('dashboard'))

    doc_type = DocumentType.query.get_or_404(id)

    if request.method == 'POST':
        name = request.form.get('name', '').strip()
        description = request.form.get('description', '').strip()

        if not name:
            flash('يرجى إدخال اسم نوع الوثيقة', 'error')
        else:
            # التحقق من عدم وجود نوع آخر بنفس الاسم
            existing = DocumentType.query.filter(DocumentType.name == name, DocumentType.id != id).first()
            if existing:
                flash('نوع الوثيقة موجود مسبقاً', 'error')
            else:
                try:
                    doc_type.name = name
                    doc_type.description = description
                    db.session.commit()

                    # تسجيل النشاط
                    log_activity('update', 'document_type', doc_type.id, doc_type.name)

                    flash('تم تعديل نوع الوثيقة بنجاح', 'success')
                    return redirect(url_for('document_types'))
                except Exception as e:
                    db.session.rollback()
                    logger.error(f"خطأ في تعديل نوع الوثيقة: {e}")
                    flash('حدث خطأ في تعديل نوع الوثيقة', 'error')

    return render_template('admin/edit_document_type.html', doc_type=doc_type)

@app.route('/document-types/<int:id>/delete', methods=['POST'])
@login_required
def delete_document_type(id):
    """حذف نوع الوثيقة"""
    if not current_user.is_admin():
        flash('ليس لديك صلاحية لحذف أنواع الوثائق', 'error')
        return redirect(url_for('dashboard'))

    doc_type = DocumentType.query.get_or_404(id)

    # التحقق من عدم وجود وثائق تستخدم هذا النوع
    documents_count = Document.query.filter_by(document_type=doc_type.name).count()
    if documents_count > 0:
        flash(f'لا يمكن حذف نوع الوثيقة لأنه مستخدم في {documents_count} وثيقة', 'error')
        return redirect(url_for('document_types'))

    try:
        name = doc_type.name
        db.session.delete(doc_type)
        db.session.commit()

        # تسجيل النشاط
        log_activity('delete', 'document_type', id, name)

        flash('تم حذف نوع الوثيقة بنجاح', 'success')
    except Exception as e:
        db.session.rollback()
        logger.error(f"خطأ في حذف نوع الوثيقة: {e}")
        flash('حدث خطأ في حذف نوع الوثيقة', 'error')

    return redirect(url_for('document_types'))

# مسارات إعدادات النظام
@app.route('/settings')
@login_required
def settings():
    """صفحة إعدادات النظام"""
    if not current_user.is_admin():
        flash('ليس لديك صلاحية للوصول إلى إعدادات النظام', 'error')
        return redirect(url_for('dashboard'))

    # تجميع الإعدادات حسب الفئة
    settings_by_category = {}
    try:
        all_settings = SystemSettings.query.all()

        for setting in all_settings:
            if setting.category not in settings_by_category:
                settings_by_category[setting.category] = []
            settings_by_category[setting.category].append(setting)
    except Exception as e:
        logger.error(f"خطأ في تحميل الإعدادات: {e}")
        flash('حدث خطأ في تحميل الإعدادات', 'error')

    return render_template('settings/index.html', settings=settings_by_category)

@app.route('/settings/edit', methods=['GET', 'POST'])
@login_required
def edit_settings():
    """تعديل إعدادات النظام"""
    if not current_user.is_admin():
        flash('ليس لديك صلاحية لتعديل إعدادات النظام', 'error')
        return redirect(url_for('dashboard'))

    form = SettingsForm()

    if request.method == 'GET':
        # تحميل القيم الحالية
        form.system_name.data = SystemSettings.get_setting('system_name', 'نظام إدارة الأرشيف العام')
        form.organization_name.data = SystemSettings.get_setting('organization_name', 'المؤسسة')
        form.contact_email.data = SystemSettings.get_setting('contact_email', '')
        form.contact_phone.data = SystemSettings.get_setting('contact_phone', '')
        form.address.data = SystemSettings.get_setting('address', '')
        form.auto_archive_days.data = str(SystemSettings.get_setting('auto_archive_days', 365))
        form.max_file_size.data = str(SystemSettings.get_setting('max_file_size', 50))
        form.allowed_extensions.data = SystemSettings.get_setting('allowed_extensions', 'pdf,doc,docx,jpg,png,gif')
        form.session_timeout.data = str(SystemSettings.get_setting('session_timeout', 60))
        form.password_min_length.data = str(SystemSettings.get_setting('password_min_length', 6))
        form.enable_two_factor.data = str(SystemSettings.get_setting('enable_two_factor', False)).lower()
        form.backup_frequency.data = SystemSettings.get_setting('backup_frequency', 'weekly')
        form.backup_retention_days.data = str(SystemSettings.get_setting('backup_retention_days', 30))

    if form.validate_on_submit():
        try:
            # حفظ الإعدادات
            SystemSettings.set_setting('system_name', form.system_name.data, 'text', 'اسم النظام', 'general')
            SystemSettings.set_setting('organization_name', form.organization_name.data, 'text', 'اسم المؤسسة', 'general')
            SystemSettings.set_setting('contact_email', form.contact_email.data, 'text', 'البريد الإلكتروني', 'general')
            SystemSettings.set_setting('contact_phone', form.contact_phone.data, 'text', 'رقم الهاتف', 'general')
            SystemSettings.set_setting('address', form.address.data, 'text', 'العنوان', 'general')

            SystemSettings.set_setting('auto_archive_days', form.auto_archive_days.data, 'number', 'أيام الأرشفة التلقائية', 'archive')
            SystemSettings.set_setting('max_file_size', form.max_file_size.data, 'number', 'الحد الأقصى لحجم الملف', 'archive')
            SystemSettings.set_setting('allowed_extensions', form.allowed_extensions.data, 'text', 'امتدادات الملفات المسموحة', 'archive')

            SystemSettings.set_setting('session_timeout', form.session_timeout.data, 'number', 'مهلة انتهاء الجلسة', 'security')
            SystemSettings.set_setting('password_min_length', form.password_min_length.data, 'number', 'الحد الأدنى لطول كلمة المرور', 'security')
            SystemSettings.set_setting('enable_two_factor', form.enable_two_factor.data, 'boolean', 'تفعيل المصادقة الثنائية', 'security')

            SystemSettings.set_setting('backup_frequency', form.backup_frequency.data, 'text', 'تكرار النسخ الاحتياطي', 'backup')
            SystemSettings.set_setting('backup_retention_days', form.backup_retention_days.data, 'number', 'أيام الاحتفاظ بالنسخ الاحتياطية', 'backup')

            logger.info(f"تم تحديث إعدادات النظام بواسطة {current_user.username}")
            flash('تم حفظ إعدادات النظام بنجاح', 'success')
            return redirect(url_for('settings'))

        except Exception as e:
            logger.error(f"خطأ في حفظ إعدادات النظام: {e}")
            flash('حدث خطأ في حفظ الإعدادات', 'error')

    return render_template('settings/edit.html', form=form)

@app.route('/settings/backup')
@login_required
def create_backup():
    """إنشاء نسخة احتياطية"""
    if not current_user.is_admin():
        flash('ليس لديك صلاحية لإنشاء نسخة احتياطية', 'error')
        return redirect(url_for('dashboard'))

    try:
        backup_path = create_system_backup()
        flash(f'تم إنشاء النسخة الاحتياطية بنجاح: {backup_path}', 'success')
    except Exception as e:
        logger.error(f"خطأ في إنشاء النسخة الاحتياطية: {e}")
        flash('حدث خطأ في إنشاء النسخة الاحتياطية', 'error')

    return redirect(url_for('settings'))

# مسارات نسيت كلمة المرور
@app.route('/forgot-password', methods=['GET', 'POST'])
def forgot_password():
    """صفحة نسيت كلمة المرور"""
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))

    form = ForgotPasswordForm()

    if form.validate_on_submit():
        user = User.query.filter_by(email=form.email.data).first()

        if user:
            # إنشاء رمز إعادة التعيين
            token = secrets.token_urlsafe(32)
            expires_at = datetime.utcnow() + timedelta(hours=1)

            # حذف الرموز القديمة
            PasswordReset.query.filter_by(user_id=user.id).delete()

            # إنشاء رمز جديد
            reset_request = PasswordReset(
                user_id=user.id,
                token=token,
                expires_at=expires_at
            )

            db.session.add(reset_request)
            db.session.commit()

            # إرسال البريد الإلكتروني
            if send_password_reset_email(user, token):
                flash('تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني', 'success')
            else:
                flash('حدث خطأ في إرسال البريد الإلكتروني. يرجى المحاولة لاحقاً', 'error')
        else:
            # لأسباب أمنية، نعرض نفس الرسالة حتى لو لم يكن البريد موجوداً
            flash('إذا كان البريد الإلكتروني موجوداً، ستتلقى رابط إعادة التعيين', 'info')

        return redirect(url_for('login'))

    return render_template('auth/forgot_password.html', form=form)

@app.route('/reset-password/<token>', methods=['GET', 'POST'])
def reset_password(token):
    """صفحة إعادة تعيين كلمة المرور"""
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))

    # التحقق من صحة الرمز
    reset_request = PasswordReset.query.filter_by(token=token, used=False).first()

    if not reset_request or reset_request.is_expired():
        flash('رابط إعادة التعيين غير صحيح أو منتهي الصلاحية', 'error')
        return redirect(url_for('forgot_password'))

    form = ResetPasswordForm()

    if form.validate_on_submit():
        try:
            # تحديث كلمة المرور
            user = reset_request.user
            user.set_password(form.password.data)

            # تمييز الرمز كمستخدم
            reset_request.used = True

            db.session.commit()

            log_activity('password_reset', 'user', user.id, user.full_name)

            flash('تم تعيين كلمة المرور الجديدة بنجاح. يمكنك الآن تسجيل الدخول', 'success')
            return redirect(url_for('login'))

        except Exception as e:
            db.session.rollback()
            logger.error(f"خطأ في إعادة تعيين كلمة المرور: {e}")
            flash('حدث خطأ في تعيين كلمة المرور. يرجى المحاولة مرة أخرى', 'error')

    return render_template('auth/reset_password.html', form=form, token=token)

# مسار تغيير اللغة
@app.route('/set-language/<language>')
def set_language(language=None):
    """تغيير لغة النظام"""
    if language in app.config['LANGUAGES']:
        session['language'] = language
        flash(f'تم تغيير اللغة إلى {app.config["LANGUAGES"][language]}', 'success')

    return redirect(request.referrer or url_for('login'))

# مسار عرض سجل الأنشطة
@app.route('/activity-log')
@login_required
def activity_log():
    """صفحة عرض سجل الأنشطة"""
    if not current_user.is_admin():
        flash('ليس لديك صلاحية للوصول إلى سجل الأنشطة', 'error')
        return redirect(url_for('dashboard'))

    page = request.args.get('page', 1, type=int)
    per_page = 20

    # فلترة حسب المستخدم
    user_filter = request.args.get('user_id', type=int)
    action_filter = request.args.get('action')
    entity_filter = request.args.get('entity_type')

    query = ActivityLog.query

    if user_filter:
        query = query.filter(ActivityLog.user_id == user_filter)

    if action_filter:
        query = query.filter(ActivityLog.action == action_filter)

    if entity_filter:
        query = query.filter(ActivityLog.entity_type == entity_filter)

    activities = query.order_by(ActivityLog.created_at.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )

    # قائمة المستخدمين للفلترة
    users = User.query.all()

    # قائمة الأنشطة المتاحة
    actions = ['create', 'update', 'delete', 'view', 'login', 'logout', 'password_reset']
    entity_types = ['document', 'incoming', 'outgoing', 'user', 'system']

    return render_template('activity_log.html',
                         activities=activities,
                         users=users,
                         actions=actions,
                         entity_types=entity_types,
                         current_filters={
                             'user_id': user_filter,
                             'action': action_filter,
                             'entity_type': entity_filter
                         })

# مسارات سلة المهملات
@app.route('/trash')
@login_required
def trash():
    """صفحة سلة المهملات"""
    if not current_user.is_admin():
        flash('ليس لديك صلاحية للوصول إلى سلة المهملات', 'error')
        return redirect(url_for('dashboard'))

    page = request.args.get('page', 1, type=int)
    per_page = 20

    # فلترة حسب نوع العنصر
    entity_filter = request.args.get('entity_type')

    query = DeletedItem.query

    if entity_filter:
        query = query.filter(DeletedItem.entity_type == entity_filter)

    deleted_items = query.order_by(DeletedItem.deleted_at.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )

    # إحصائيات سلة المهملات
    trash_stats = {
        'total_items': DeletedItem.query.count(),
        'documents': DeletedItem.query.filter_by(entity_type='document').count(),
        'incoming': DeletedItem.query.filter_by(entity_type='incoming').count(),
        'outgoing': DeletedItem.query.filter_by(entity_type='outgoing').count()
    }

    entity_types = ['document', 'incoming', 'outgoing']

    return render_template('trash.html',
                         deleted_items=deleted_items,
                         trash_stats=trash_stats,
                         entity_types=entity_types,
                         current_filter=entity_filter)

@app.route('/trash/restore/<int:item_id>')
@login_required
def restore_item(item_id):
    """استرجاع عنصر من سلة المهملات"""
    if not current_user.is_admin():
        flash('ليس لديك صلاحية لاسترجاع العناصر', 'error')
        return redirect(url_for('dashboard'))

    deleted_item = DeletedItem.query.get_or_404(item_id)

    if not deleted_item.can_restore:
        flash('لا يمكن استرجاع هذا العنصر', 'error')
        return redirect(url_for('trash'))

    try:
        # استرجاع البيانات الأصلية
        original_data = json.loads(deleted_item.entity_data)

        # إنشاء العنصر حسب نوعه
        if deleted_item.entity_type == 'document':
            document = Document(
                title=original_data.get('title'),
                description=original_data.get('description'),
                document_type=original_data.get('document_type'),
                status=original_data.get('status'),
                created_by=current_user.id,
                file_name=original_data.get('file_name'),
                file_path=original_data.get('file_path'),
                file_size=original_data.get('file_size'),
                file_type=original_data.get('file_type')
            )
            db.session.add(document)
            entity_name = document.title

        elif deleted_item.entity_type == 'incoming':
            incoming = IncomingDocument(
                incoming_number=generate_incoming_number(),
                subject=original_data.get('subject'),
                sender=original_data.get('sender'),
                received_date=datetime.utcnow(),
                priority=original_data.get('priority'),
                status=original_data.get('status'),
                received_by=current_user.id,
                file_name=original_data.get('file_name'),
                file_path=original_data.get('file_path'),
                file_size=original_data.get('file_size'),
                file_type=original_data.get('file_type')
            )
            db.session.add(incoming)
            entity_name = incoming.subject

        elif deleted_item.entity_type == 'outgoing':
            outgoing = OutgoingDocument(
                outgoing_number=generate_outgoing_number(),
                subject=original_data.get('subject'),
                recipient=original_data.get('recipient'),
                sent_date=datetime.utcnow(),
                priority=original_data.get('priority'),
                status=original_data.get('status'),
                prepared_by=current_user.id,
                file_name=original_data.get('file_name'),
                file_path=original_data.get('file_path'),
                file_size=original_data.get('file_size'),
                file_type=original_data.get('file_type')
            )
            db.session.add(outgoing)
            entity_name = outgoing.subject

        # حذف العنصر من سلة المهملات
        db.session.delete(deleted_item)
        db.session.commit()

        # تسجيل النشاط
        log_activity('restore', deleted_item.entity_type, deleted_item.original_id, entity_name)

        flash(f'تم استرجاع {entity_name} بنجاح', 'success')

    except Exception as e:
        db.session.rollback()
        logger.error(f"خطأ في استرجاع العنصر: {e}")
        flash('حدث خطأ في استرجاع العنصر', 'error')

    return redirect(url_for('trash'))

@app.route('/trash/delete-permanently/<int:item_id>')
@login_required
def delete_permanently(item_id):
    """حذف عنصر نهائياً من سلة المهملات"""
    if not current_user.is_admin():
        flash('ليس لديك صلاحية للحذف النهائي', 'error')
        return redirect(url_for('dashboard'))

    deleted_item = DeletedItem.query.get_or_404(item_id)

    try:
        # حذف الملف المرفق إن وجد
        original_data = json.loads(deleted_item.entity_data)
        if original_data.get('file_path'):
            file_path = Path(original_data['file_path'])
            if file_path.exists():
                file_path.unlink()

        entity_name = original_data.get('title') or original_data.get('subject', 'عنصر غير محدد')

        # حذف العنصر من سلة المهملات
        db.session.delete(deleted_item)
        db.session.commit()

        # تسجيل النشاط
        log_activity('delete_permanently', deleted_item.entity_type, deleted_item.original_id, entity_name)

        flash(f'تم حذف {entity_name} نهائياً', 'success')

    except Exception as e:
        db.session.rollback()
        logger.error(f"خطأ في الحذف النهائي: {e}")
        flash('حدث خطأ في الحذف النهائي', 'error')

    return redirect(url_for('trash'))

@app.route('/trash/empty')
@login_required
def empty_trash():
    """إفراغ سلة المهملات بالكامل"""
    if not current_user.is_admin():
        flash('ليس لديك صلاحية لإفراغ سلة المهملات', 'error')
        return redirect(url_for('dashboard'))

    try:
        # حذف جميع الملفات المرفقة
        deleted_items = DeletedItem.query.all()
        deleted_count = len(deleted_items)

        for item in deleted_items:
            try:
                original_data = json.loads(item.entity_data)
                if original_data.get('file_path'):
                    file_path = Path(original_data['file_path'])
                    if file_path.exists():
                        file_path.unlink()
            except Exception as e:
                logger.error(f"خطأ في حذف الملف: {e}")

        # حذف جميع العناصر من سلة المهملات
        DeletedItem.query.delete()
        db.session.commit()

        # تسجيل النشاط
        log_activity('empty_trash', 'system', 0, f'إفراغ سلة المهملات ({deleted_count} عنصر)')

        flash(f'تم إفراغ سلة المهملات ({deleted_count} عنصر)', 'success')

    except Exception as e:
        db.session.rollback()
        logger.error(f"خطأ في إفراغ سلة المهملات: {e}")
        flash('حدث خطأ في إفراغ سلة المهملات', 'error')

    return redirect(url_for('trash'))



def generate_qr_text(doc_id):
    """توليد نص QR Code"""
    return f"QR-{doc_id}-{datetime.now().strftime('%Y%m%d')}"

def generate_incoming_number():
    """توليد رقم وارد تلقائي"""
    from sqlalchemy import extract
    year = datetime.now().year
    count = IncomingDocument.query.filter(
        extract('year', IncomingDocument.received_date) == year
    ).count()
    return f"IN-{year}-{count + 1:04d}"

def generate_outgoing_number():
    """توليد رقم صادر تلقائي"""
    from sqlalchemy import extract
    year = datetime.now().year
    count = OutgoingDocument.query.filter(
        extract('year', OutgoingDocument.sent_date) == year
    ).count()
    return f"OUT-{year}-{count + 1:04d}"

def log_activity(action, entity_type, entity_id, entity_name=None, details=None):
    """تسجيل نشاط المستخدم - محسن"""
    try:
        if current_user.is_authenticated:
            activity = ActivityLog(
                user_id=current_user.id,
                action=action,
                entity_type=entity_type,
                entity_id=entity_id,
                entity_name=entity_name or '',
                details=details or f'{action} {entity_type}',
                ip_address=request.remote_addr or '127.0.0.1'
            )
            db.session.add(activity)
            db.session.commit()
    except Exception:
        # تجاهل أخطاء تسجيل النشاط لعدم تأثيرها على العملية الأساسية
        pass

def move_to_trash(entity_type, entity_id, entity_data):
    """نقل عنصر إلى سلة المهملات"""
    try:
        deleted_item = DeletedItem(
            original_id=entity_id,
            entity_type=entity_type,
            entity_data=json.dumps(entity_data, default=str),
            deleted_by=current_user.id
        )
        db.session.add(deleted_item)
        db.session.commit()

        log_activity('delete', entity_type, entity_id, entity_data.get('title', ''))
        logger.info(f"تم نقل العنصر إلى سلة المهملات: {entity_type}:{entity_id}")
        return True
    except Exception as e:
        logger.error(f"خطأ في نقل العنصر إلى سلة المهملات: {e}")
        return False

def send_password_reset_email(user, token):
    """إرسال بريد إلكتروني لإعادة تعيين كلمة المرور"""
    try:
        reset_url = url_for('reset_password', token=token, _external=True)

        msg = Message(
            subject='إعادة تعيين كلمة المرور - نظام إدارة الأرشيف',
            recipients=[user.email],
            html=render_template('emails/reset_password.html',
                               user=user, reset_url=reset_url),
            body=f"""
            مرحباً {user.full_name},

            تم طلب إعادة تعيين كلمة المرور لحسابك.

            يرجى النقر على الرابط التالي لإعادة تعيين كلمة المرور:
            {reset_url}

            هذا الرابط صالح لمدة ساعة واحدة فقط.

            إذا لم تطلب إعادة تعيين كلمة المرور، يرجى تجاهل هذا البريد.

            مع تحيات فريق نظام إدارة الأرشيف
            """
        )

        mail.send(msg)
        logger.info(f"تم إرسال بريد إعادة تعيين كلمة المرور إلى: {user.email}")
        return True
    except Exception as e:
        logger.error(f"خطأ في إرسال البريد الإلكتروني: {e}")
        return False

def generate_qr_code(data, size=(200, 200)):
    """توليد QR code - محسن للسرعة"""
    try:
        # إعدادات محسنة للسرعة
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,  # أقل مستوى للسرعة
            box_size=8,  # حجم أصغر
            border=2,    # حدود أقل
        )
        qr.add_data(data)
        qr.make(fit=True)

        # إنشاء الصورة بسرعة
        img = qr.make_image(fill_color="black", back_color="white")

        # تغيير الحجم فقط إذا لزم الأمر
        if size != (200, 200):
            img = img.resize(size)

        # تحويل سريع إلى bytes
        buffer = io.BytesIO()
        img.save(buffer, format='PNG', optimize=True)
        buffer.seek(0)

        return buffer.getvalue()
    except Exception as e:
        logger.error(f"خطأ في توليد QR code: {e}")
        return None

def generate_barcode(data, format='code128'):
    """توليد باركود"""
    try:
        if format == 'code128':
            code = Code128(data, writer=ImageWriter())

        buffer = io.BytesIO()
        code.write(buffer)
        buffer.seek(0)

        return buffer.getvalue()
    except Exception as e:
        logger.error(f"خطأ في توليد الباركود: {e}")
        return None

def create_document_label(document):
    """CRITICAL: إنشاء ملصق A4 للوثيقة مع QR code بأبعاد محددة"""
    try:
        from PIL import Image, ImageDraw, ImageFont
        import os

        print("🔄 CRITICAL: Creating A4 document label with QR code...")

        # A4 dimensions in pixels at 300 DPI (high quality printing)
        a4_width = int(8.27 * 300)  # 2481 pixels
        a4_height = int(11.69 * 300)  # 3507 pixels

        # Create A4 size image
        img = Image.new('RGB', (a4_width, a4_height), 'white')
        draw = ImageDraw.Draw(img)

        # Load fonts for Arabic/English support
        try:
            font_paths = [
                "C:/Windows/Fonts/arial.ttf",
                "C:/Windows/Fonts/calibri.ttf",
                "C:/Windows/Fonts/tahoma.ttf",
            ]

            font_title = None
            font_text = None
            font_small = None

            for font_path in font_paths:
                if os.path.exists(font_path):
                    try:
                        font_title = ImageFont.truetype(font_path, 72)  # Large for A4
                        font_text = ImageFont.truetype(font_path, 48)   # Medium for A4
                        font_small = ImageFont.truetype(font_path, 36)  # Small for A4
                        break
                    except Exception:
                        continue

            if not font_title:
                font_title = ImageFont.load_default()
                font_text = ImageFont.load_default()
                font_small = ImageFont.load_default()

        except Exception:
            font_title = ImageFont.load_default()
            font_text = ImageFont.load_default()
            font_small = ImageFont.load_default()

        # CRITICAL: QR Code with exact dimensions (14mm x 14mm at 300 DPI)
        qr_size_pixels = int(14 * 300 / 25.4)  # Convert 14mm to pixels at 300 DPI (~165 pixels)

        # Document information for QR code
        doc_url = f"http://localhost:5000/documents/{document.id}"
        qr_text = f"""
الوثيقة: {document.title}
الرقم: #{document.id}
النوع: {document.document_type}
الحالة: {document.status}
الكلمات المفتاحية: {document.tags or 'لا توجد'}

Document: {document.title}
ID: #{document.id}
Type: {document.document_type}
Status: {document.status}

URL: {doc_url}
        """.strip()

        # Generate QR code with exact dimensions
        qr_data = generate_qr_code(qr_text, (qr_size_pixels, qr_size_pixels))

        # Position QR code (top-right corner with margin)
        qr_x = a4_width - qr_size_pixels - 200  # 200 pixels margin from right
        qr_y = 200  # 200 pixels margin from top

        if qr_data:
            qr_img = Image.open(io.BytesIO(qr_data))
            qr_img = qr_img.resize((qr_size_pixels, qr_size_pixels), Image.Resampling.LANCZOS)
            img.paste(qr_img, (qr_x, qr_y))
            print(f"✅ CRITICAL: QR code added at ({qr_x}, {qr_y}) with size {qr_size_pixels}x{qr_size_pixels} pixels (14mm x 14mm)")
        else:
            print("❌ CRITICAL: Failed to generate QR code")

        # CRITICAL: Add document information with bilingual support
        try:
            # Draw border and header
            draw.rectangle([(50, 50), (a4_width-50, a4_height-50)], outline='black', width=8)
            draw.rectangle([(70, 70), (a4_width-70, 400)], outline='navy', width=4)

            # Header section
            header_y = 100
            draw.text((100, header_y), "ملصق الوثيقة الرسمية", fill='navy', font=font_title)
            draw.text((100, header_y + 80), "Official Document Label", fill='darkblue', font=font_text)

            # Document information section
            info_y = 300
            line_height = 60

            # Document details in Arabic and English
            doc_info_text = [
                f"الوثيقة / Document: {document.title}",
                f"الرقم / ID: #{document.id}",
                f"النوع / Type: {document.document_type}",
                f"الحالة / Status: {document.status}",
                f"تاريخ الإنشاء / Created: {document.created_at.strftime('%Y-%m-%d %H:%M')}",
            ]

            if hasattr(document, 'creator') and document.creator:
                doc_info_text.append(f"المنشئ / Creator: {document.creator.username}")

            if document.tags:
                doc_info_text.append(f"الكلمات المفتاحية / Keywords: {document.tags}")

            if document.description:
                # Truncate long descriptions
                desc = document.description[:100] + "..." if len(document.description) > 100 else document.description
                doc_info_text.append(f"الوصف / Description: {desc}")

            # Draw document information
            for i, info_line in enumerate(doc_info_text):
                y_pos = info_y + (i * line_height)
                if y_pos < a4_height - 300:  # Ensure we don't go off page
                    draw.text((100, y_pos), info_line, fill='black', font=font_small)

            # QR Code label
            draw.text((qr_x, qr_y + qr_size_pixels + 20), "امسح للوصول السريع", fill='blue', font=font_small)
            draw.text((qr_x, qr_y + qr_size_pixels + 60), "Scan for Quick Access", fill='blue', font=font_small)

            # Footer with timestamp and system info
            footer_y = a4_height - 200
            draw.text((100, footer_y), f"تم الإنشاء بواسطة نظام إدارة الأرشيف العام", fill='gray', font=font_small)
            draw.text((100, footer_y + 40), f"Generated by Public Archive Management System", fill='gray', font=font_small)
            draw.text((100, footer_y + 80), f"تاريخ الطباعة / Print Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", fill='gray', font=font_small)

            print("✅ CRITICAL: A4 label content created successfully")

        except Exception as text_error:
            print(f"❌ CRITICAL: Text rendering error: {text_error}")
            # Fallback to basic layout
            draw.text((100, 100), f"Document: {document.title}", fill='black', font=font_text)
            draw.text((100, 200), f"ID: #{document.id}", fill='black', font=font_text)
            draw.text((100, 300), f"Type: {document.document_type}", fill='black', font=font_text)

        # CRITICAL: Save A4 label with high quality
        try:
            buffer = io.BytesIO()
            # Save with high quality for printing
            img.save(buffer, format='PNG', optimize=True, dpi=(300, 300))
            buffer.seek(0)

            label_data = buffer.getvalue()
            print(f"✅ CRITICAL: A4 label saved successfully, size: {len(label_data)} bytes")
            return label_data

        except Exception as save_error:
            print(f"❌ CRITICAL: A4 label save error: {save_error}")
            return None

    except Exception as e:
        print(f"❌ CRITICAL: A4 label creation error: {e}")
        logger.error(f"خطأ في إنشاء ملصق الوثيقة A4: {e}")
        return None

def create_document_a4_pdf_label(document):
    """CRITICAL: إنشاء ملصق A4 للوثيقة بصيغة PDF مع QR code"""
    try:
        print(f"🔄 CRITICAL: Creating A4 PDF label for document {document.id}")

        # Try to use reportlab for PDF generation
        try:
            from reportlab.lib.pagesizes import A4
            from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Image, Table, TableStyle
            from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
            from reportlab.lib.units import mm, cm
            from reportlab.lib import colors
            from reportlab.pdfbase import pdfmetrics
            from reportlab.pdfbase.ttfonts import TTFont
            from reportlab.lib.enums import TA_RIGHT, TA_CENTER, TA_LEFT

            print("✅ CRITICAL: ReportLab imported successfully")

        except ImportError:
            print("❌ CRITICAL: ReportLab not available, falling back to HTML-to-PDF")
            return create_document_a4_html_to_pdf_label(document)

        # Create PDF buffer
        buffer = io.BytesIO()

        # Create PDF document
        doc = SimpleDocTemplate(
            buffer,
            pagesize=A4,
            rightMargin=2*cm,
            leftMargin=2*cm,
            topMargin=2*cm,
            bottomMargin=2*cm
        )

        # Create styles
        styles = getSampleStyleSheet()

        # Arabic-compatible styles
        title_style = ParagraphStyle(
            'ArabicTitle',
            parent=styles['Title'],
            fontSize=24,
            spaceAfter=20,
            alignment=TA_CENTER,
            textColor=colors.darkblue,
            fontName='Helvetica-Bold'
        )

        heading_style = ParagraphStyle(
            'ArabicHeading',
            parent=styles['Heading2'],
            fontSize=16,
            spaceAfter=12,
            alignment=TA_RIGHT,
            textColor=colors.darkblue,
            fontName='Helvetica-Bold'
        )

        normal_style = ParagraphStyle(
            'ArabicNormal',
            parent=styles['Normal'],
            fontSize=12,
            spaceAfter=8,
            alignment=TA_RIGHT,
            fontName='Helvetica'
        )

        # Build content
        content = []

        # Title
        content.append(Paragraph("ملصق الوثيقة / Document Label", title_style))
        content.append(Spacer(1, 20))

        # Document information table
        doc_data = [
            ['Document Information / معلومات الوثيقة', ''],
            [f'العنوان / Title: {document.title}', ''],
            [f'الرقم / ID: #{document.id}', ''],
            [f'النوع / Type: {document.document_type}', ''],
            [f'الحالة / Status: {document.status}', ''],
            [f'تاريخ الإنشاء / Created: {document.created_at.strftime("%Y-%m-%d") if document.created_at else "غير محدد"}', ''],
        ]

        if document.tags:
            doc_data.append([f'الكلمات المفتاحية / Keywords: {document.tags}', ''])

        if document.description:
            doc_data.append([f'الوصف / Description: {document.description[:100]}...', ''])

        # Create table
        doc_table = Table(doc_data, colWidths=[15*cm, 2*cm])
        doc_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.lightblue),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.darkblue),
            ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 14),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('FONTSIZE', (0, 1), (-1, -1), 11),
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.lightgrey]),
        ]))

        content.append(doc_table)
        content.append(Spacer(1, 30))

        # QR Code section
        try:
            # Generate QR code
            doc_url = f"http://localhost:5000/documents/{document.id}"
            qr_text = f"""الوثيقة: {document.title}
الرقم: #{document.id}
النوع: {document.document_type}
الحالة: {document.status}

Document: {document.title}
ID: #{document.id}
Type: {document.document_type}
Status: {document.status}

URL: {doc_url}"""

            qr_data = generate_qr_code(qr_text, (200, 200))

            if qr_data:
                # Save QR code to temporary image
                qr_img_buffer = io.BytesIO(qr_data)
                qr_image = Image(qr_img_buffer, width=4*cm, height=4*cm)

                # QR section table
                qr_data_table = [
                    ['رمز QR للوصول السريع / QR Code for Quick Access', qr_image],
                    ['امسح هذا الرمز للوصول للوثيقة مباشرة', ''],
                    ['Scan this code to access the document directly', ''],
                    [f'الرابط / URL: {doc_url}', ''],
                ]

                qr_table = Table(qr_data_table, colWidths=[12*cm, 5*cm])
                qr_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (0, 0), colors.lightgreen),
                    ('TEXTCOLOR', (0, 0), (0, 0), colors.darkgreen),
                    ('ALIGN', (0, 0), (0, -1), 'RIGHT'),
                    ('ALIGN', (1, 0), (1, 0), 'CENTER'),
                    ('VALIGN', (1, 0), (1, 0), 'MIDDLE'),
                    ('FONTNAME', (0, 0), (0, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (0, 0), 12),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black),
                    ('SPAN', (1, 0), (1, -1)),
                ]))

                content.append(qr_table)
                print("✅ CRITICAL: QR code added to PDF")
            else:
                content.append(Paragraph("خطأ في إنشاء رمز QR / QR Code generation error", normal_style))
                print("❌ CRITICAL: Failed to generate QR code for PDF")

        except Exception as qr_error:
            print(f"❌ CRITICAL: QR code error in PDF: {qr_error}")
            content.append(Paragraph("خطأ في إنشاء رمز QR / QR Code generation error", normal_style))

        content.append(Spacer(1, 30))

        # Footer
        footer_text = f"""تم إنشاء هذا الملصق تلقائياً من نظام إدارة الأرشيف العام
Generated automatically by Public Archive Management System
تاريخ الطباعة / Print Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"""

        content.append(Paragraph(footer_text, ParagraphStyle(
            'Footer',
            parent=styles['Normal'],
            fontSize=10,
            alignment=TA_CENTER,
            textColor=colors.grey
        )))

        # Build PDF
        doc.build(content)

        # Get PDF data
        pdf_data = buffer.getvalue()
        buffer.close()

        print(f"✅ CRITICAL: A4 PDF label created successfully, size: {len(pdf_data)} bytes")
        return pdf_data

    except Exception as e:
        print(f"❌ CRITICAL: Error creating A4 PDF label: {e}")
        logger.error(f"خطأ في إنشاء ملصق PDF: {e}")
        return None

def create_document_a4_html_to_pdf_label(document):
    """Fallback: إنشاء ملصق A4 HTML (fallback when ReportLab not available)"""
    try:
        print(f"🔄 CRITICAL: Creating A4 HTML label as PDF fallback for document {document.id}")

        # Generate QR code as base64
        doc_url = f"http://localhost:5000/documents/{document.id}"
        qr_text = f"""الوثيقة: {document.title}
الرقم: #{document.id}
النوع: {document.document_type}
الحالة: {document.status}

URL: {doc_url}"""

        qr_data = generate_qr_code(qr_text, (200, 200))
        qr_base64 = ""
        if qr_data:
            import base64
            qr_base64 = base64.b64encode(qr_data).decode('utf-8')

        # Create HTML content
        html_content = f"""<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>ملصق الوثيقة #{document.id}</title>
    <style>
        @page {{ size: A4; margin: 2cm; }}
        body {{ font-family: Arial, sans-serif; direction: rtl; }}
        .header {{ background: #3498db; color: white; padding: 2rem; text-align: center; border-radius: 10px; margin-bottom: 2rem; }}
        .content {{ margin: 2rem 0; }}
        .qr-section {{ text-align: center; margin: 2rem 0; }}
        .footer {{ text-align: center; color: #666; font-size: 0.9rem; margin-top: 3rem; }}
        table {{ width: 100%; border-collapse: collapse; margin: 1rem 0; }}
        th, td {{ border: 1px solid #ddd; padding: 0.75rem; text-align: right; }}
        th {{ background: #f8f9fa; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>ملصق الوثيقة / Document Label</h1>
    </div>

    <div class="content">
        <table>
            <tr><th colspan="2">معلومات الوثيقة / Document Information</th></tr>
            <tr><td>العنوان / Title</td><td>{document.title}</td></tr>
            <tr><td>الرقم / ID</td><td>#{document.id}</td></tr>
            <tr><td>النوع / Type</td><td>{document.document_type}</td></tr>
            <tr><td>الحالة / Status</td><td>{document.status}</td></tr>
            <tr><td>تاريخ الإنشاء / Created</td><td>{document.created_at.strftime('%Y-%m-%d') if document.created_at else 'غير محدد'}</td></tr>
            {f'<tr><td>الكلمات المفتاحية / Keywords</td><td>{document.tags}</td></tr>' if document.tags else ''}
        </table>
    </div>

    <div class="qr-section">
        <h3>رمز QR للوصول السريع / QR Code for Quick Access</h3>
        {f'<img src="data:image/png;base64,{qr_base64}" alt="QR Code" style="width: 150px; height: 150px;">' if qr_base64 else '<p>خطأ في إنشاء رمز QR</p>'}
        <p>امسح هذا الرمز للوصول للوثيقة مباشرة</p>
        <p>Scan this code to access the document directly</p>
        <p style="font-size: 0.8rem; color: #666;">{doc_url}</p>
    </div>

    <div class="footer">
        <p>تم إنشاء هذا الملصق تلقائياً من نظام إدارة الأرشيف العام</p>
        <p>Generated automatically by Public Archive Management System</p>
        <p>تاريخ الطباعة / Print Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
    </div>
</body>
</html>"""

        # For now, return HTML as bytes (can be converted to PDF with external tools)
        print("✅ CRITICAL: A4 HTML label created as fallback")
        return html_content.encode('utf-8')

    except Exception as e:
        print(f"❌ CRITICAL: Error creating HTML fallback label: {e}")
        logger.error(f"خطأ في إنشاء ملصق HTML: {e}")
        return None

def create_document_a4_label(document):
    """إنشاء ملصق A4 شامل للوثيقة كملف HTML"""
    try:
        from datetime import datetime
        import io

        # إنشاء محتوى HTML
        html_content = f"""
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ملصق الوثيقة #{document.id}</title>
    <style>
        @page {{
            size: A4;
            margin: 2cm;
        }}

        body {{
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            direction: rtl;
            text-align: right;
        }}

        .header {{
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            padding: 2rem;
            text-align: center;
            border-radius: 10px;
            margin-bottom: 2rem;
        }}

        .header h1 {{
            margin: 0;
            font-size: 2rem;
            font-weight: bold;
        }}

        .print-date {{
            margin-top: 1rem;
            font-size: 1rem;
            opacity: 0.9;
        }}

        .document-info {{
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            padding: 2rem;
            margin-bottom: 2rem;
        }}

        .info-row {{
            display: flex;
            margin-bottom: 1rem;
            padding: 0.5rem 0;
            border-bottom: 1px solid #e9ecef;
        }}

        .info-label {{
            font-weight: bold;
            color: #2c3e50;
            min-width: 150px;
            flex-shrink: 0;
        }}

        .info-value {{
            color: #34495e;
            flex: 1;
        }}

        .qr-section {{
            text-align: center;
            margin: 2rem 0;
            padding: 2rem;
            background: #fff;
            border: 2px dashed #bdc3c7;
            border-radius: 10px;
        }}

        .qr-code {{
            margin: 1rem 0;
        }}

        .footer {{
            text-align: center;
            margin-top: 3rem;
            padding-top: 2rem;
            border-top: 2px solid #e9ecef;
            color: #7f8c8d;
            font-size: 0.9rem;
        }}

        @media print {{
            body {{
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }}
        }}
    </style>
</head>
<body>
    <div class="header">
        <h1>نظام إدارة الأرشيف العام</h1>
        <div class="print-date">تاريخ الطباعة: {datetime.now().strftime('%Y/%m/%d %H:%M')}</div>
    </div>

    <div class="document-info">
        <div class="info-row">
            <div class="info-label">العنوان:</div>
            <div class="info-value">{document.title or "غير محدد"}</div>
        </div>
        <div class="info-row">
            <div class="info-label">الوصف:</div>
            <div class="info-value">{document.description or "غير محدد"}</div>
        </div>
        <div class="info-row">
            <div class="info-label">النوع:</div>
            <div class="info-value">{document.document_type or "غير محدد"}</div>
        </div>
        <div class="info-row">
            <div class="info-label">الحالة:</div>
            <div class="info-value">{document.status or "غير محدد"}</div>
        </div>
        <div class="info-row">
            <div class="info-label">الكلمات المفتاحية:</div>
            <div class="info-value">{document.tags or "غير محدد"}</div>
        </div>
        <div class="info-row">
            <div class="info-label">رقم الوثيقة:</div>
            <div class="info-value">#{document.id}</div>
        </div>
        <div class="info-row">
            <div class="info-label">تاريخ الإنشاء:</div>
            <div class="info-value">{document.created_at.strftime('%Y/%m/%d %H:%M')}</div>
        </div>
        <div class="info-row">
            <div class="info-label">المنشئ:</div>
            <div class="info-value">{document.created_by.full_name if document.created_by else "غير محدد"}</div>
        </div>
        <div class="info-row">
            <div class="info-label">حجم الملف:</div>
            <div class="info-value">{f"{document.file_size / 1024 / 1024:.2f} ميجابايت" if document.file_size else "غير محدد"}</div>
        </div>
        <div class="info-row">
            <div class="info-label">نوع الملف:</div>
            <div class="info-value">{document.file_name.split('.')[-1].upper() if document.file_name else "غير محدد"}</div>
        </div>
    </div>

    <div class="qr-section">
        <h3>رمز QR للوصول المباشر</h3>
        <div class="qr-code">
            <p>امسح هذا الرمز للوصول للوثيقة وتحميل الملف المرفق</p>
            <p style="font-size: 0.9rem; color: #666;">
                الرابط: {url_for('view_document', id=document.id, _external=True)}
            </p>
        </div>
    </div>

    <div class="footer">
        تم إنشاء هذا الملصق تلقائياً من نظام إدارة الأرشيف العام
    </div>
</body>
</html>
        """

        # تحويل إلى bytes
        return html_content.encode('utf-8')

    except Exception as e:
        logger.error(f"خطأ في إنشاء ملصق HTML: {e}")
        return None



def auto_archive_documents():
    """أرشفة الوثائق تلقائياً حسب الإعدادات"""
    try:
        # الحصول على إعدادات الأرشفة
        auto_archive_days = SystemSettings.get_setting('auto_archive_days', 365)

        if auto_archive_days <= 0:
            return  # الأرشفة التلقائية معطلة

        # حساب التاريخ الحد
        cutoff_date = datetime.utcnow() - timedelta(days=auto_archive_days)

        # البحث عن الوثائق القديمة
        old_documents = Document.query.filter(
            Document.created_at < cutoff_date,
            Document.status != 'مؤرشف'
        ).all()

        archived_count = 0
        for doc in old_documents:
            doc.status = 'مؤرشف'

            # تسجيل النشاط
            log_activity('archive', 'document', doc.id, f'أرشفة تلقائية: {doc.title}')

            # إنشاء إشعار للمنشئ
            notification = Notification(
                title='تم أرشفة وثيقة تلقائياً',
                message=f'تم أرشفة الوثيقة "{doc.title}" تلقائياً بعد {auto_archive_days} يوم',
                type='info',
                user_id=doc.created_by,
                entity_type='document',
                entity_id=doc.id
            )
            db.session.add(notification)
            archived_count += 1

        if archived_count > 0:
            db.session.commit()
            logger.info(f"تم أرشفة {archived_count} وثيقة تلقائياً")

        return archived_count

    except Exception as e:
        db.session.rollback()
        logger.error(f"خطأ في الأرشفة التلقائية: {e}")
        return 0

def cleanup_old_logs():
    """تنظيف سجلات الأنشطة القديمة"""
    try:
        # الحصول على إعدادات الاحتفاظ
        log_retention_days = SystemSettings.get_setting('log_retention_days', 90)

        if log_retention_days <= 0:
            return  # التنظيف معطل

        # حساب التاريخ الحد
        cutoff_date = datetime.utcnow() - timedelta(days=log_retention_days)

        # حذف السجلات القديمة
        deleted_count = ActivityLog.query.filter(
            ActivityLog.created_at < cutoff_date
        ).delete()

        if deleted_count > 0:
            db.session.commit()
            logger.info(f"تم حذف {deleted_count} سجل نشاط قديم")

        return deleted_count

    except Exception as e:
        db.session.rollback()
        logger.error(f"خطأ في تنظيف السجلات: {e}")
        return 0

def backup_database():
    """إنشاء نسخة احتياطية من قاعدة البيانات"""
    try:
        backup_dir = Path('backups')
        backup_dir.mkdir(exist_ok=True)

        # اسم ملف النسخة الاحتياطية
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_file = backup_dir / f'backup_{timestamp}.db'

        # نسخ قاعدة البيانات
        shutil.copy2('archive_system.db', backup_file)

        # ضغط النسخة الاحتياطية
        zip_file = backup_dir / f'backup_{timestamp}.zip'
        with zipfile.ZipFile(zip_file, 'w', zipfile.ZIP_DEFLATED) as zf:
            zf.write(backup_file, backup_file.name)

        # حذف الملف غير المضغوط
        backup_file.unlink()

        logger.info(f"تم إنشاء نسخة احتياطية: {zip_file}")

        # تنظيف النسخ الاحتياطية القديمة
        cleanup_old_backups()

        return str(zip_file)

    except Exception as e:
        logger.error(f"خطأ في إنشاء النسخة الاحتياطية: {e}")
        return None

def generate_report_data(form):
    """توليد بيانات التقرير"""
    report_type = form.report_type.data
    date_from = form.date_from.data
    date_to = form.date_to.data
    status_filter = form.status_filter.data
    priority_filter = form.priority_filter.data
    user_filter = form.user_filter.data

    data = {
        'report_type': report_type,
        'date_from': date_from,
        'date_to': date_to,
        'generated_at': datetime.now(),
        'generated_by': current_user.full_name,
        'filters': {
            'status': status_filter,
            'priority': priority_filter,
            'user': user_filter
        },
        'items': [],
        'summary': {}
    }

    if report_type == 'documents':
        query = Document.query
        if date_from:
            try:
                if isinstance(date_from, str):
                    date_from = datetime.strptime(date_from, '%Y-%m-%d').date()
                query = query.filter(Document.created_at >= date_from)
            except (ValueError, TypeError):
                pass
        if date_to:
            try:
                if isinstance(date_to, str):
                    date_to = datetime.strptime(date_to, '%Y-%m-%d').date()
                query = query.filter(Document.created_at <= date_to)
            except (ValueError, TypeError):
                pass
        if status_filter:
            query = query.filter(Document.status == status_filter)
        if user_filter:
            try:
                query = query.filter(Document.created_by == int(user_filter))
            except ValueError:
                pass

        documents = query.order_by(Document.created_at.desc()).all()
        data['items'] = documents
        try:
            data['summary'] = {
                'total_count': len(documents),
                'by_type': db.session.query(Document.document_type, func.count(Document.id)).group_by(Document.document_type).all(),
                'by_status': db.session.query(Document.status, func.count(Document.id)).group_by(Document.status).all()
            }
        except Exception as e:
            logger.error(f"خطأ في إحصائيات الوثائق: {e}")
            data['summary'] = {'total_count': len(documents), 'by_type': [], 'by_status': []}

    elif report_type == 'incoming':
        query = IncomingDocument.query
        if date_from:
            query = query.filter(IncomingDocument.received_date >= date_from)
        if date_to:
            query = query.filter(IncomingDocument.received_date <= date_to)
        if status_filter:
            query = query.filter(IncomingDocument.status == status_filter)
        if priority_filter:
            query = query.filter(IncomingDocument.priority == priority_filter)
        if user_filter:
            try:
                query = query.filter(IncomingDocument.received_by == int(user_filter))
            except ValueError:
                pass

        documents = query.order_by(IncomingDocument.received_date.desc()).all()
        data['items'] = documents
        data['summary'] = {
            'total_count': len(documents),
            'by_priority': db.session.query(IncomingDocument.priority, func.count(IncomingDocument.id)).group_by(IncomingDocument.priority).all(),
            'by_status': db.session.query(IncomingDocument.status, func.count(IncomingDocument.id)).group_by(IncomingDocument.status).all()
        }

    elif report_type == 'outgoing':
        query = OutgoingDocument.query
        if date_from:
            query = query.filter(OutgoingDocument.sent_date >= date_from)
        if date_to:
            query = query.filter(OutgoingDocument.sent_date <= date_to)
        if status_filter:
            query = query.filter(OutgoingDocument.status == status_filter)
        if priority_filter:
            query = query.filter(OutgoingDocument.priority == priority_filter)
        if user_filter:
            try:
                query = query.filter(OutgoingDocument.prepared_by == int(user_filter))
            except ValueError:
                pass

        documents = query.order_by(OutgoingDocument.sent_date.desc()).all()
        data['items'] = documents
        data['summary'] = {
            'total_count': len(documents),
            'by_priority': db.session.query(OutgoingDocument.priority, func.count(OutgoingDocument.id)).group_by(OutgoingDocument.priority).all(),
            'by_status': db.session.query(OutgoingDocument.status, func.count(OutgoingDocument.id)).group_by(OutgoingDocument.status).all()
        }

    elif report_type == 'users':
        query = User.query
        if date_from:
            query = query.filter(User.created_at >= date_from)
        if date_to:
            query = query.filter(User.created_at <= date_to)

        users = query.order_by(User.created_at.desc()).all()
        data['items'] = users
        data['summary'] = {
            'total_count': len(users),
            'by_role': db.session.query(User.role, func.count(User.id)).group_by(User.role).all(),
            'by_department': db.session.query(User.department, func.count(User.id)).group_by(User.department).all(),
            'active_users': User.query.filter_by(is_active=True).count(),
            'inactive_users': User.query.filter_by(is_active=False).count()
        }

    elif report_type == 'statistics':
        data = get_comprehensive_statistics()

    return data

def get_comprehensive_statistics():
    """الحصول على إحصائيات شاملة"""
    now = datetime.now()
    current_year = now.year
    current_month = now.month

    stats = {
        'overview': {
            'total_documents': Document.query.count(),
            'total_incoming': IncomingDocument.query.count(),
            'total_outgoing': OutgoingDocument.query.count(),
            'total_users': User.query.count(),
            'active_users': User.query.filter_by(is_active=True).count()
        },
        'monthly': {
            'documents': Document.query.filter(
                extract('year', Document.created_at) == current_year,
                extract('month', Document.created_at) == current_month
            ).count(),
            'incoming': IncomingDocument.query.filter(
                extract('year', IncomingDocument.created_at) == current_year,
                extract('month', IncomingDocument.created_at) == current_month
            ).count(),
            'outgoing': OutgoingDocument.query.filter(
                extract('year', OutgoingDocument.created_at) == current_year,
                extract('month', OutgoingDocument.created_at) == current_month
            ).count()
        },
        'yearly': {
            'documents': Document.query.filter(
                extract('year', Document.created_at) == current_year
            ).count(),
            'incoming': IncomingDocument.query.filter(
                extract('year', IncomingDocument.created_at) == current_year
            ).count(),
            'outgoing': OutgoingDocument.query.filter(
                extract('year', OutgoingDocument.created_at) == current_year
            ).count()
        },
        'by_status': {
            'documents': dict(db.session.query(Document.status, func.count(Document.id)).group_by(Document.status).all()),
            'incoming': dict(db.session.query(IncomingDocument.status, func.count(IncomingDocument.id)).group_by(IncomingDocument.status).all()),
            'outgoing': dict(db.session.query(OutgoingDocument.status, func.count(OutgoingDocument.id)).group_by(OutgoingDocument.status).all())
        },
        'by_priority': {
            'incoming': dict(db.session.query(IncomingDocument.priority, func.count(IncomingDocument.id)).group_by(IncomingDocument.priority).all()),
            'outgoing': dict(db.session.query(OutgoingDocument.priority, func.count(OutgoingDocument.id)).group_by(OutgoingDocument.priority).all())
        },
        'by_type': dict(db.session.query(Document.document_type, func.count(Document.id)).group_by(Document.document_type).all()),
        'by_department': dict(db.session.query(User.department, func.count(User.id)).group_by(User.department).all()),
        'monthly_trends': get_monthly_trends(),
        'top_users': get_top_users()
    }

    return stats

def get_monthly_trends():
    """الحصول على اتجاهات شهرية"""
    trends = []
    for i in range(12):
        month = datetime.now().month - i
        year = datetime.now().year
        if month <= 0:
            month += 12
            year -= 1

        month_data = {
            'month': month,
            'year': year,
            'month_name': ['', 'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                          'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'][month],
            'documents': Document.query.filter(
                extract('year', Document.created_at) == year,
                extract('month', Document.created_at) == month
            ).count(),
            'incoming': IncomingDocument.query.filter(
                extract('year', IncomingDocument.created_at) == year,
                extract('month', IncomingDocument.created_at) == month
            ).count(),
            'outgoing': OutgoingDocument.query.filter(
                extract('year', OutgoingDocument.created_at) == year,
                extract('month', OutgoingDocument.created_at) == month
            ).count()
        }
        trends.append(month_data)

    return list(reversed(trends))

def get_top_users():
    """الحصول على أكثر المستخدمين نشاطاً"""
    users_stats = []
    for user in User.query.filter_by(is_active=True).all():
        user_data = {
            'user': user,
            'documents_count': Document.query.filter_by(created_by=user.id).count(),
            'incoming_count': IncomingDocument.query.filter_by(received_by=user.id).count(),
            'outgoing_count': OutgoingDocument.query.filter_by(prepared_by=user.id).count()
        }
        user_data['total_activity'] = user_data['documents_count'] + user_data['incoming_count'] + user_data['outgoing_count']
        users_stats.append(user_data)

    return sorted(users_stats, key=lambda x: x['total_activity'], reverse=True)[:10]

def export_csv_report(report_data, form):
    """تصدير التقرير كملف CSV"""
    import csv
    from io import StringIO

    output = StringIO()
    writer = csv.writer(output)

    # كتابة العنوان
    writer.writerow([f"تقرير {form.report_type.data}"])
    writer.writerow([f"تاريخ التوليد: {report_data['generated_at'].strftime('%Y-%m-%d %H:%M')}"])
    writer.writerow([f"المولد بواسطة: {report_data['generated_by']}"])
    writer.writerow([])

    # كتابة البيانات حسب نوع التقرير
    if form.report_type.data in ['documents', 'incoming', 'outgoing']:
        if form.report_type.data == 'documents':
            writer.writerow(['الرقم', 'العنوان', 'النوع', 'الحالة', 'تاريخ الإنشاء', 'المنشئ'])
            for item in report_data['items']:
                writer.writerow([
                    item.id,
                    item.title,
                    item.document_type,
                    item.status,
                    item.created_at.strftime('%Y-%m-%d'),
                    item.creator.full_name if item.creator else ''
                ])
        elif form.report_type.data == 'incoming':
            writer.writerow(['رقم الوارد', 'الموضوع', 'الجهة المرسلة', 'الأولوية', 'الحالة', 'تاريخ الاستلام', 'المستلم'])
            for item in report_data['items']:
                writer.writerow([
                    item.incoming_number,
                    item.subject,
                    item.sender_name,
                    item.priority,
                    item.status,
                    item.received_date.strftime('%Y-%m-%d'),
                    item.receiver.full_name if item.receiver else ''
                ])
        elif form.report_type.data == 'outgoing':
            writer.writerow(['رقم الصادر', 'الموضوع', 'الجهة المرسل إليها', 'الأولوية', 'الحالة', 'تاريخ الإرسال', 'المُعِد'])
            for item in report_data['items']:
                writer.writerow([
                    item.outgoing_number,
                    item.subject,
                    item.recipient_name,
                    item.priority,
                    item.status,
                    item.sent_date.strftime('%Y-%m-%d'),
                    item.preparer.full_name if item.preparer else ''
                ])

    # إنشاء الاستجابة
    response = make_response(output.getvalue())
    response.headers['Content-Type'] = 'text/csv; charset=utf-8'
    response.headers['Content-Disposition'] = f'attachment; filename=report_{form.report_type.data}_{datetime.now().strftime("%Y%m%d")}.csv'

    return response

def export_excel_report(report_data, form):
    """تصدير التقرير كملف Excel"""
    try:
        # محاولة استخدام openpyxl إذا كانت متاحة
        import openpyxl
        from openpyxl import Workbook

        wb = Workbook()
        ws = wb.active
        ws.title = f"تقرير {form.report_type.data}"

        # إضافة العناوين
        headers = []
        if form.report_type.data == 'documents':
            headers = ['الرقم', 'العنوان', 'النوع', 'الحالة', 'تاريخ الإنشاء']
        elif form.report_type.data == 'incoming':
            headers = ['رقم الوارد', 'الموضوع', 'الجهة المرسلة', 'الأولوية', 'الحالة', 'تاريخ الاستلام']
        elif form.report_type.data == 'outgoing':
            headers = ['رقم الصادر', 'الموضوع', 'الجهة المرسل إليها', 'الأولوية', 'الحالة', 'تاريخ الإرسال']

        for col, header in enumerate(headers, 1):
            ws.cell(row=1, column=col, value=header)

        # إضافة البيانات
        for row, item in enumerate(report_data['items'], 2):
            if form.report_type.data == 'documents':
                ws.cell(row=row, column=1, value=item.id)
                ws.cell(row=row, column=2, value=item.title)
                ws.cell(row=row, column=3, value=item.document_type)
                ws.cell(row=row, column=4, value=item.status)
                ws.cell(row=row, column=5, value=item.created_at.strftime('%Y-%m-%d'))

        # حفظ الملف
        output = BytesIO()
        wb.save(output)
        output.seek(0)

        response = make_response(output.getvalue())
        response.headers['Content-Type'] = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        response.headers['Content-Disposition'] = f'attachment; filename=report_{form.report_type.data}_{datetime.now().strftime("%Y%m%d")}.xlsx'

        return response

    except ImportError:
        flash('مكتبة Excel غير متاحة. يرجى استخدام تصدير CSV', 'warning')
        return redirect(url_for('generate_report'))
    except Exception as e:
        logger.error(f"خطأ في تصدير Excel: {e}")
        flash('حدث خطأ في تصدير Excel', 'error')
        return redirect(url_for('generate_report'))

def export_pdf_report(report_data, form):
    """تصدير التقرير كملف PDF"""
    try:
        # محاولة استخدام reportlab إذا كانت متاحة
        from reportlab.lib.pagesizes import letter, A4
        from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph
        from reportlab.lib.styles import getSampleStyleSheet
        from reportlab.lib import colors

        output = BytesIO()
        doc = SimpleDocTemplate(output, pagesize=A4)

        # إنشاء المحتوى
        story = []
        styles = getSampleStyleSheet()

        # العنوان
        title = Paragraph(f"تقرير {form.report_type.data}", styles['Title'])
        story.append(title)

        # البيانات في جدول
        data = []
        if form.report_type.data == 'documents':
            data.append(['الرقم', 'العنوان', 'النوع', 'الحالة'])
            for item in report_data['items'][:50]:  # أول 50 عنصر
                data.append([str(item.id), item.title[:30], item.document_type, item.status])

        if data:
            table = Table(data)
            table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 14),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            story.append(table)

        doc.build(story)
        output.seek(0)

        response = make_response(output.getvalue())
        response.headers['Content-Type'] = 'application/pdf'
        response.headers['Content-Disposition'] = f'attachment; filename=report_{form.report_type.data}_{datetime.now().strftime("%Y%m%d")}.pdf'

        return response

    except ImportError:
        flash('مكتبة PDF غير متاحة. يرجى استخدام تصدير CSV', 'warning')
        return redirect(url_for('generate_report'))
    except Exception as e:
        logger.error(f"خطأ في تصدير PDF: {e}")
        flash('حدث خطأ في تصدير PDF', 'error')
        return redirect(url_for('generate_report'))

def create_system_backup():
    """إنشاء نسخة احتياطية من النظام"""
    import shutil
    import zipfile

    backup_dir = Path('backups')
    backup_dir.mkdir(exist_ok=True)

    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    backup_name = f'backup_{timestamp}'
    backup_path = backup_dir / f'{backup_name}.zip'

    with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
        # نسخ قاعدة البيانات
        db_path = Path('archive_system.db')
        if db_path.exists():
            zipf.write(db_path, 'archive_system.db')

        # نسخ مجلد الملفات المرفوعة
        uploads_dir = Path('uploads')
        if uploads_dir.exists():
            for file_path in uploads_dir.rglob('*'):
                if file_path.is_file():
                    zipf.write(file_path, f'uploads/{file_path.relative_to(uploads_dir)}')

        # نسخ ملفات الإعدادات
        for config_file in ['app.py', 'requirements.txt']:
            config_path = Path(config_file)
            if config_path.exists():
                zipf.write(config_path, config_file)

    logger.info(f"تم إنشاء نسخة احتياطية: {backup_path}")
    return str(backup_path)

def preprocess_image_for_ocr(image):
    """معالجة متقدمة للصورة لتحسين نتائج OCR"""
    try:
        # تحويل إلى رمادي
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()

        # تحسين الحجم إذا كانت الصورة صغيرة جداً
        height, width = gray.shape
        if height < 300 or width < 300:
            scale_factor = max(300/height, 300/width, 2.0)
            new_width = int(width * scale_factor)
            new_height = int(height * scale_factor)
            gray = cv2.resize(gray, (new_width, new_height), interpolation=cv2.INTER_CUBIC)

        # إزالة الضوضاء
        denoised = cv2.medianBlur(gray, 3)

        # تحسين التباين باستخدام CLAHE
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
        enhanced = clahe.apply(denoised)

        # تطبيق Gaussian blur خفيف لتنعيم الحواف
        blurred = cv2.GaussianBlur(enhanced, (1, 1), 0)

        # تطبيق threshold متقدم
        _, thresh = cv2.threshold(blurred, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

        # تحسين النص باستخدام morphological operations
        kernel = np.ones((1,1), np.uint8)
        processed = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)

        # إزالة النقاط الصغيرة
        kernel2 = np.ones((2,2), np.uint8)
        processed = cv2.morphologyEx(processed, cv2.MORPH_OPEN, kernel2)

        return processed, enhanced, thresh

    except Exception as e:
        logger.error(f"خطأ في معالجة الصورة: {e}")
        return None, None, None

def extract_text_from_image(image_path):
    """استخراج النص من الصورة باستخدام OCR محسن"""
    try:
        # قراءة الصورة
        image = cv2.imread(image_path)
        if image is None:
            logger.error(f"لا يمكن قراءة الصورة: {image_path}")
            return None

        logger.info(f"بدء معالجة الصورة: {image_path}")

        # معالجة الصورة
        processed, enhanced, thresh = preprocess_image_for_ocr(image)
        if processed is None:
            return None

        extracted_texts = []

        # تكوينات OCR متقدمة
        configs = [
            # تكوينات للنصوص المختلطة
            {'config': '--oem 3 --psm 6 -l ara+eng', 'desc': 'عربي + إنجليزي - فقرات'},
            {'config': '--oem 3 --psm 4 -l ara+eng', 'desc': 'عربي + إنجليزي - عمود واحد'},
            {'config': '--oem 3 --psm 3 -l ara+eng', 'desc': 'عربي + إنجليزي - صفحة كاملة'},

            # تكوينات للنصوص الإنجليزية
            {'config': '--oem 3 --psm 6 -l eng', 'desc': 'إنجليزي - فقرات'},
            {'config': '--oem 3 --psm 8 -l eng', 'desc': 'إنجليزي - كلمة واحدة'},
            {'config': '--oem 3 --psm 7 -l eng', 'desc': 'إنجليزي - سطر واحد'},

            # تكوينات للنصوص العربية
            {'config': '--oem 3 --psm 6 -l ara', 'desc': 'عربي - فقرات'},
            {'config': '--oem 3 --psm 4 -l ara', 'desc': 'عربي - عمود واحد'},

            # تكوينات خاصة للقطات الشاشة
            {'config': '--oem 3 --psm 11 -l ara+eng', 'desc': 'نص متناثر'},
            {'config': '--oem 3 --psm 12 -l ara+eng', 'desc': 'نص متناثر مع OSD'},
        ]

        # جرب مع الصور المعالجة المختلفة
        images_to_try = [
            ('معالجة متقدمة', processed),
            ('تحسين التباين', enhanced),
            ('threshold', thresh)
        ]

        for img_desc, img in images_to_try:
            for config_data in configs:
                try:
                    text = pytesseract.image_to_string(img, config=config_data['config'])
                    if text and text.strip() and len(text.strip()) > 3:
                        cleaned_text = text.strip()
                        extracted_texts.append({
                            'text': cleaned_text,
                            'length': len(cleaned_text),
                            'config': config_data['desc'],
                            'image': img_desc
                        })
                        logger.info(f"نجح استخراج النص مع {config_data['desc']} على {img_desc}: {len(cleaned_text)} حرف")

                except Exception as config_error:
                    logger.debug(f"فشل في {config_data['desc']}: {config_error}")
                    continue

        # اختيار أفضل نتيجة
        if extracted_texts:
            # ترتيب حسب الطول والجودة
            best_result = max(extracted_texts, key=lambda x: x['length'])
            logger.info(f"أفضل نتيجة: {best_result['config']} على {best_result['image']} - {best_result['length']} حرف")
            return best_result['text']

        # محاولة أخيرة مع تكوين بسيط
        try:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY) if len(image.shape) == 3 else image
            simple_text = pytesseract.image_to_string(gray, config='--psm 6')
            if simple_text and simple_text.strip():
                logger.info(f"نجح التكوين البسيط: {len(simple_text.strip())} حرف")
                return simple_text.strip()
        except Exception as simple_error:
            logger.error(f"فشل في التكوين البسيط: {simple_error}")

        logger.warning("لم يتم العثور على نص في الصورة مع جميع التكوينات")
        return None

    except Exception as e:
        logger.error(f"خطأ في استخراج النص من الصورة: {e}")
        return None

def extract_text_from_pdf(pdf_path):
    """استخراج النص من ملف PDF مع معالجة محسنة"""
    try:
        logger.info(f"بدء معالجة ملف PDF: {pdf_path}")

        # محاولة استخدام PyMuPDF أولاً
        try:
            import fitz  # PyMuPDF

            text = ""
            doc = fitz.open(pdf_path)
            total_pages = len(doc)
            logger.info(f"عدد صفحات PDF: {total_pages}")

            for page_num in range(total_pages):
                page = doc.load_page(page_num)
                page_text = page.get_text()

                if page_text.strip():
                    text += page_text + "\n"
                    logger.info(f"استخراج نص مباشر من الصفحة {page_num + 1}: {len(page_text)} حرف")
                else:
                    # إذا لم يكن هناك نص، جرب OCR على الصور
                    logger.info(f"الصفحة {page_num + 1} تحتاج OCR")

                    # تحويل الصفحة إلى صورة بجودة عالية
                    mat = fitz.Matrix(4, 4)  # تكبير أكبر للحصول على جودة أفضل
                    pix = page.get_pixmap(matrix=mat)
                    img_data = pix.tobytes("png")

                    # حفظ مؤقت للصورة
                    temp_img_path = os.path.join(app.config['UPLOAD_FOLDER'], 'temp', f"temp_pdf_page_{page_num}_{int(time.time())}.png")
                    os.makedirs(os.path.dirname(temp_img_path), exist_ok=True)

                    try:
                        with open(temp_img_path, "wb") as f:
                            f.write(img_data)

                        # استخراج النص من الصورة
                        ocr_text = extract_text_from_image(temp_img_path)
                        if ocr_text:
                            text += ocr_text + "\n"
                            logger.info(f"OCR للصفحة {page_num + 1}: {len(ocr_text)} حرف")
                        else:
                            logger.warning(f"لم يتم العثور على نص في الصفحة {page_num + 1}")

                    except Exception as page_error:
                        logger.error(f"خطأ في معالجة الصفحة {page_num + 1}: {page_error}")

                    finally:
                        # حذف الملف المؤقت
                        try:
                            if os.path.exists(temp_img_path):
                                os.remove(temp_img_path)
                        except Exception as cleanup_error:
                            logger.warning(f"فشل في حذف الملف المؤقت: {cleanup_error}")

            doc.close()

            if text.strip():
                logger.info(f"تم استخراج النص من PDF بنجاح: {len(text)} حرف من {total_pages} صفحة")
                return text.strip()

        except ImportError:
            logger.warning("PyMuPDF غير متوفر، محاولة استخدام طريقة بديلة")
        except Exception as fitz_error:
            logger.error(f"خطأ في PyMuPDF: {fitz_error}")

        # محاولة استخدام pdf2image + OCR كبديل
        try:
            from pdf2image import convert_from_path

            logger.info("استخدام pdf2image لتحويل PDF إلى صور")

            # تحويل PDF إلى صور بجودة عالية
            images = convert_from_path(pdf_path, dpi=400, fmt='png')
            text = ""

            for i, image in enumerate(images):
                logger.info(f"معالجة الصفحة {i + 1} من {len(images)}")

                # حفظ الصورة مؤقتاً
                temp_img_path = os.path.join(app.config['UPLOAD_FOLDER'], 'temp', f"temp_pdf2img_{i}_{int(time.time())}.png")
                os.makedirs(os.path.dirname(temp_img_path), exist_ok=True)

                try:
                    image.save(temp_img_path, 'PNG')

                    # استخراج النص
                    page_text = extract_text_from_image(temp_img_path)
                    if page_text:
                        text += page_text + "\n"
                        logger.info(f"استخراج نص من الصفحة {i + 1}: {len(page_text)} حرف")

                except Exception as page_error:
                    logger.error(f"خطأ في معالجة الصفحة {i + 1}: {page_error}")

                finally:
                    # حذف الملف المؤقت
                    try:
                        if os.path.exists(temp_img_path):
                            os.remove(temp_img_path)
                    except:
                        pass

            if text.strip():
                logger.info(f"تم استخراج النص من PDF باستخدام pdf2image: {len(text)} حرف")
                return text.strip()

        except ImportError:
            logger.error("pdf2image غير متوفر. يرجى تثبيته: pip install pdf2image")
        except Exception as pdf2image_error:
            logger.error(f"خطأ في pdf2image: {pdf2image_error}")

        logger.warning("لم يتم العثور على نص في ملف PDF مع جميع الطرق")
        return None

    except Exception as e:
        logger.error(f"خطأ عام في استخراج النص من PDF: {e}")
        return None

def process_document_ocr(document_id):
    """معالجة وثيقة بـ OCR واستخراج النص"""
    try:
        document = Document.query.get(document_id)
        if not document or not document.file_path:
            return None

        file_path = os.path.join(app.config['UPLOAD_FOLDER'], document.file_path)
        if not os.path.exists(file_path):
            return None

        extracted_text = ""
        file_ext = os.path.splitext(file_path)[1].lower()

        if file_ext in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']:
            # معالجة الصور
            extracted_text = extract_text_from_image(file_path)
        elif file_ext == '.pdf':
            # معالجة ملفات PDF
            extracted_text = extract_text_from_pdf(file_path)

        if extracted_text:
            # حفظ النص المستخرج في قاعدة البيانات
            document.extracted_text = extracted_text
            db.session.commit()

            # تسجيل النشاط
            log_activity('ocr', 'document', document.id, f'استخراج النص: {document.title}')

            return extracted_text

        return None

    except Exception as e:
        logger.error(f"خطأ في معالجة OCR للوثيقة {document_id}: {e}")
        return None

def generate_digital_signature(document_id, user_id, signature_type='normal', notes=None):
    """توليد توقيع رقمي متدرج للوثيقة"""
    try:
        import hashlib
        import hmac
        from datetime import datetime

        # التحقق من صلاحية المستخدم
        user = User.query.get(user_id)
        if not user or not user.is_admin():
            logger.warning(f"محاولة توقيع من مستخدم غير مخول: {user_id}")
            return None

        # إنشاء بيانات التوقيع المتقدمة
        timestamp = datetime.utcnow().isoformat()
        document = Document.query.get(document_id)

        # بيانات التوقيع الشاملة
        signature_data = f"{document_id}:{user_id}:{timestamp}:{signature_type}"
        if document:
            signature_data += f":{document.title}:{document.document_type}"
        if notes:
            signature_data += f":{notes}"

        # مفتاح سري للتوقيع (في بيئة الإنتاج يجب أن يكون آمناً)
        secret_key = app.config.get('SECRET_KEY', 'default-secret-key')

        # خوارزمية تشفير أقوى للتوقيع المعتمد
        if signature_type == 'certified':
            # استخدام SHA-512 للتوقيع المعتمد
            signature = hmac.new(
                secret_key.encode('utf-8'),
                signature_data.encode('utf-8'),
                hashlib.sha512
            ).hexdigest()
            algorithm = 'HMAC-SHA512'
        else:
            # استخدام SHA-256 للتوقيع العادي
            signature = hmac.new(
                secret_key.encode('utf-8'),
                signature_data.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()
            algorithm = 'HMAC-SHA256'

        return {
            'signature': signature,
            'timestamp': timestamp,
            'document_id': document_id,
            'user_id': user_id,
            'signature_type': signature_type,
            'algorithm': algorithm,
            'notes': notes,
            'user_name': user.full_name or user.username,
            'user_role': user.role,
            'document_title': document.title if document else 'غير محدد'
        }

    except Exception as e:
        logger.error(f"خطأ في توليد التوقيع الرقمي: {e}")
        return None

def verify_digital_signature(signature_data):
    """التحقق من صحة التوقيع الرقمي"""
    try:
        import hashlib
        import hmac

        # استخراج البيانات
        signature = signature_data.get('signature')
        timestamp = signature_data.get('timestamp')
        document_id = signature_data.get('document_id')
        user_id = signature_data.get('user_id')

        # إعادة بناء بيانات التوقيع
        original_data = f"{document_id}:{user_id}:{timestamp}"

        # مفتاح سري للتحقق
        secret_key = app.config.get('SECRET_KEY', 'default-secret-key')

        # توليد التوقيع المتوقع
        expected_signature = hmac.new(
            secret_key.encode('utf-8'),
            original_data.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()

        # مقارنة التوقيعات
        return hmac.compare_digest(signature, expected_signature)

    except Exception as e:
        logger.error(f"خطأ في التحقق من التوقيع الرقمي: {e}")
        return False

def create_signature_certificate(document_id, user_id, signature_data):
    """CRITICAL FIX: إنشاء شهادة توقيع رقمي مع دعم النصوص العربية والإنجليزية"""
    try:
        from PIL import Image, ImageDraw, ImageFont
        import os

        print("🔄 CRITICAL: Creating digital signature certificate...")

        # إنشاء صورة الشهادة بدقة عالية
        width, height = 800, 600
        img = Image.new('RGB', (width, height), 'white')
        draw = ImageDraw.Draw(img)

        # الحصول على بيانات المستخدم والوثيقة
        user = User.query.get(user_id)
        document = Document.query.get(document_id)

        if not user or not document:
            print("❌ CRITICAL: User or document not found")
            return None

        # CRITICAL FIX: تحسين الخطوط لدعم النصوص العربية والإنجليزية
        try:
            # محاولة استخدام خطوط تدعم العربية
            font_paths = [
                "C:/Windows/Fonts/arial.ttf",
                "C:/Windows/Fonts/calibri.ttf",
                "C:/Windows/Fonts/tahoma.ttf",
                "/System/Library/Fonts/Arial.ttf",  # macOS
                "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",  # Linux
            ]

            font_title = None
            font_text = None
            font_small = None

            for font_path in font_paths:
                if os.path.exists(font_path):
                    try:
                        font_title = ImageFont.truetype(font_path, 28)
                        font_text = ImageFont.truetype(font_path, 18)
                        font_small = ImageFont.truetype(font_path, 14)
                        print(f"✅ CRITICAL: Using font: {font_path}")
                        break
                    except Exception:
                        continue

            if not font_title:
                font_title = ImageFont.load_default()
                font_text = ImageFont.load_default()
                font_small = ImageFont.load_default()
                print("⚠️ CRITICAL: Using default fonts")

        except Exception as e:
            print(f"❌ CRITICAL: Font loading error: {e}")
            font_title = ImageFont.load_default()
            font_text = ImageFont.load_default()
            font_small = ImageFont.load_default()

        # CRITICAL FIX: إضافة النصوص مع تشفير صحيح
        try:
            # رسم الخلفية والحدود
            draw.rectangle([(10, 10), (width-10, height-10)], outline='navy', width=3)
            draw.rectangle([(20, 20), (width-20, height-20)], outline='lightblue', width=1)

            # العنوان الرئيسي - ثنائي اللغة
            title_ar = "شهادة التوقيع الرقمي"
            title_en = "Digital Signature Certificate"

            # تحسين عرض النصوص العربية والإنجليزية
            draw.text((50, 40), title_ar, fill='navy', font=font_title)
            draw.text((50, 75), title_en, fill='darkblue', font=font_text)

            # خط فاصل مزخرف
            draw.line([(50, 120), (width-50, 120)], fill='navy', width=3)
            draw.line([(50, 125), (width-50, 125)], fill='lightblue', width=1)

            # معلومات الوثيقة مع تشفير آمن
            y_pos = 160

            # CRITICAL: تشفير النصوص بشكل صحيح
            doc_title = document.title if document.title else "غير محدد"
            doc_type = document.document_type if document.document_type else "غير محدد"
            user_name = user.full_name if user.full_name else user.username
            user_email = user.email if user.email else "غير محدد"
            user_dept = user.department if user.department else "غير محدد"

            # معلومات الوثيقة
            draw.text((50, y_pos), f"الوثيقة / Document: {doc_title}", fill='black', font=font_text)
            y_pos += 30
            draw.text((50, y_pos), f"رقم الوثيقة / Document ID: #{document.id}", fill='black', font=font_text)
            y_pos += 30
            draw.text((50, y_pos), f"نوع الوثيقة / Type: {doc_type}", fill='black', font=font_text)
            y_pos += 40

            # معلومات الموقع
            draw.text((50, y_pos), f"الموقع / Signer: {user_name}", fill='darkgreen', font=font_text)
            y_pos += 30
            draw.text((50, y_pos), f"البريد الإلكتروني / Email: {user_email}", fill='black', font=font_small)
            y_pos += 25
            draw.text((50, y_pos), f"القسم / Department: {user_dept}", fill='black', font=font_small)
            y_pos += 40

            # معلومات التوقيع
            timestamp = signature_data.get('timestamp', 'غير محدد')
            algorithm = signature_data.get('algorithm', 'غير محدد')
            signature_hash = signature_data.get('signature', '')
            signature_type = signature_data.get('signature_type', 'normal')

            draw.text((50, y_pos), f"تاريخ التوقيع / Date: {timestamp[:19]}", fill='purple', font=font_text)
            y_pos += 25
            draw.text((50, y_pos), f"خوارزمية التوقيع / Algorithm: {algorithm}", fill='black', font=font_small)
            y_pos += 25
            draw.text((50, y_pos), f"نوع التوقيع / Type: {signature_type.upper()}", fill='red', font=font_small)
            y_pos += 25

            # عرض جزء من التوقيع
            if len(signature_hash) > 40:
                signature_display = f"{signature_hash[:20]}...{signature_hash[-20:]}"
            else:
                signature_display = signature_hash

            draw.text((50, y_pos), f"التوقيع / Signature:", fill='blue', font=font_small)
            y_pos += 20
            draw.text((50, y_pos), signature_display, fill='blue', font=font_small)

            # ختم التوثيق
            draw.text((width-200, height-80), "VERIFIED", fill='green', font=font_title)
            draw.text((width-200, height-50), "موثق", fill='green', font=font_text)

            print("✅ CRITICAL: Certificate content created successfully")

        except Exception as text_error:
            print(f"❌ CRITICAL: Text rendering error: {text_error}")
            # Fallback to basic English text only
            draw.text((50, 50), "Digital Signature Certificate", fill='navy', font=font_title)
            draw.text((50, 100), f"Document: {document.title}", fill='black', font=font_text)
            draw.text((50, 130), f"Signer: {user.username}", fill='black', font=font_text)
            draw.text((50, 160), f"Date: {signature_data.get('timestamp', 'N/A')}", fill='black', font=font_text)

        # CRITICAL: حفظ الشهادة مع ضغط محسن
        try:
            buffer = io.BytesIO()
            # حفظ بجودة عالية
            img.save(buffer, format='PNG', optimize=True, quality=95)
            buffer.seek(0)

            certificate_data = buffer.getvalue()
            print(f"✅ CRITICAL: Certificate saved successfully, size: {len(certificate_data)} bytes")
            return certificate_data

        except Exception as save_error:
            print(f"❌ CRITICAL: Certificate save error: {save_error}")
            return None

    except Exception as e:
        logger.error(f"خطأ في إنشاء شهادة التوقيع: {e}")
        return None

def get_database_size():
    """الحصول على حجم قاعدة البيانات"""
    try:
        db_path = 'archive_system.db'
        if os.path.exists(db_path):
            size_bytes = os.path.getsize(db_path)
            # تحويل إلى MB
            size_mb = round(size_bytes / (1024 * 1024), 2)
            return f"{size_mb} MB"
        return "غير محدد"
    except:
        return "غير محدد"

def get_upload_folder_size():
    """الحصول على حجم مجلد الملفات المرفوعة"""
    try:
        upload_folder = app.config.get('UPLOAD_FOLDER', 'uploads')
        if not os.path.exists(upload_folder):
            return "0 MB"

        total_size = 0
        for dirpath, dirnames, filenames in os.walk(upload_folder):
            for filename in filenames:
                filepath = os.path.join(dirpath, filename)
                total_size += os.path.getsize(filepath)

        # تحويل إلى MB
        size_mb = round(total_size / (1024 * 1024), 2)
        return f"{size_mb} MB"
    except:
        return "غير محدد"

def get_last_maintenance_date(maintenance_type):
    """الحصول على تاريخ آخر عملية صيانة"""
    try:
        # البحث في سجل الأنشطة عن آخر عملية صيانة
        last_activity = ActivityLog.query.filter(
            ActivityLog.action.like(f'%{maintenance_type}%')
        ).order_by(ActivityLog.created_at.desc()).first()

        if last_activity:
            return last_activity.created_at.strftime('%Y/%m/%d %H:%M')
        return "لم يتم"
    except:
        return "غير محدد"

def get_system_uptime():
    """الحصول على وقت تشغيل النظام"""
    try:
        import psutil
        boot_time = psutil.boot_time()
        uptime_seconds = time.time() - boot_time

        days = int(uptime_seconds // 86400)
        hours = int((uptime_seconds % 86400) // 3600)

        if days > 0:
            return f"{days} يوم، {hours} ساعة"
        else:
            return f"{hours} ساعة"
    except:
        return "غير محدد"

# تهيئة قاعدة البيانات
def init_database():
    """تهيئة قاعدة البيانات"""
    with app.app_context():
        # حذف الجداول الموجودة وإعادة إنشائها لتطبيق التغييرات الجديدة
        db.drop_all()
        db.create_all()

        # إنشاء مستخدم مدير افتراضي
        admin = User(
            username='admin',
            email='<EMAIL>',
            full_name='مدير النظام',
            role='admin',
            department='إدارة النظام'
        )
        admin.set_password('Admin123!')
        db.session.add(admin)

        # إنشاء مستخدم موظف للاختبار
        employee = User(
            username='employee',
            email='<EMAIL>',
            full_name='موظف الأرشيف',
            role='employee',
            department='قسم الأرشيف'
        )
        employee.set_password('Employee123!')
        db.session.add(employee)

        # إنشاء أنواع الوثائق الافتراضية
        default_document_types = [
            # الوثائق الإدارية
            {'name': 'وثائق إدارية', 'description': 'الوثائق الإدارية العامة والقرارات الإدارية', 'color': '#3498db'},
            {'name': 'قرارات', 'description': 'القرارات الإدارية والتنفيذية', 'color': '#e74c3c'},
            {'name': 'تعاميم', 'description': 'التعاميم والتوجيهات الإدارية', 'color': '#f39c12'},
            {'name': 'مذكرات', 'description': 'المذكرات الداخلية والتوضيحية', 'color': '#9b59b6'},

            # الوثائق المالية
            {'name': 'وثائق مالية', 'description': 'الوثائق المالية والمحاسبية', 'color': '#2ecc71'},
            {'name': 'فواتير', 'description': 'الفواتير والمطالبات المالية', 'color': '#27ae60'},
            {'name': 'ميزانيات', 'description': 'الميزانيات والتقديرات المالية', 'color': '#16a085'},
            {'name': 'مقاولات', 'description': 'عقود المقاولات والاتفاقيات المالية', 'color': '#1abc9c'},

            # الوثائق القانونية
            {'name': 'وثائق قانونية', 'description': 'الوثائق والمستندات القانونية', 'color': '#34495e'},
            {'name': 'عقود', 'description': 'العقود والاتفاقيات القانونية', 'color': '#2c3e50'},
            {'name': 'اتفاقيات', 'description': 'الاتفاقيات ومذكرات التفاهم', 'color': '#7f8c8d'},
            {'name': 'قوانين', 'description': 'القوانين واللوائح التنظيمية', 'color': '#95a5a6'},

            # الوثائق الفنية
            {'name': 'وثائق فنية', 'description': 'الوثائق والتقارير الفنية', 'color': '#e67e22'},
            {'name': 'تقارير', 'description': 'التقارير الفنية والإدارية', 'color': '#d35400'},
            {'name': 'دراسات', 'description': 'الدراسات والبحوث المتخصصة', 'color': '#f39c12'},
            {'name': 'مخططات', 'description': 'المخططات والرسوم التقنية', 'color': '#e67e22'},

            # المراسلات
            {'name': 'مراسلات', 'description': 'المراسلات الرسمية والإدارية', 'color': '#8e44ad'},
            {'name': 'خطابات', 'description': 'الخطابات الرسمية والشخصية', 'color': '#9b59b6'},
            {'name': 'برقيات', 'description': 'البرقيات والرسائل العاجلة', 'color': '#e74c3c'},
            {'name': 'فاكسات', 'description': 'رسائل الفاكس والمراسلات السريعة', 'color': '#c0392b'},

            # أنواع إضافية
            {'name': 'شهادات', 'description': 'الشهادات والوثائق المعتمدة', 'color': '#f1c40f'},
            {'name': 'تراخيص', 'description': 'التراخيص والتصاريح الرسمية', 'color': '#f39c12'},
            {'name': 'محاضر', 'description': 'محاضر الاجتماعات والجلسات', 'color': '#3498db'},
            {'name': 'استمارات', 'description': 'الاستمارات والنماذج الرسمية', 'color': '#1abc9c'}
        ]

        created_types = 0
        for type_data in default_document_types:
            if not DocumentType.query.filter_by(name=type_data['name']).first():
                doc_type = DocumentType(
                    name=type_data['name'],
                    description=type_data['description'],
                    color=type_data['color']
                )
                db.session.add(doc_type)
                created_types += 1

        # إنشاء الإعدادات الافتراضية
        default_settings = [
            ('system_name', 'نظام إدارة الأرشيف العام', 'text', 'اسم النظام', 'general'),
            ('organization_name', 'المؤسسة', 'text', 'اسم المؤسسة', 'general'),
            ('contact_email', '<EMAIL>', 'text', 'البريد الإلكتروني للتواصل', 'general'),
            ('contact_phone', '', 'text', 'رقم الهاتف', 'general'),
            ('address', '', 'text', 'العنوان', 'general'),
            ('auto_archive_days', '365', 'number', 'عدد أيام الأرشفة التلقائية', 'archive'),
            ('max_file_size', '50', 'number', 'الحد الأقصى لحجم الملف بالميجابايت', 'archive'),
            ('allowed_extensions', 'pdf,doc,docx,jpg,jpeg,png,gif', 'text', 'امتدادات الملفات المسموحة', 'archive'),
            ('session_timeout', '60', 'number', 'مهلة انتهاء الجلسة بالدقائق', 'security'),
            ('password_min_length', '6', 'number', 'الحد الأدنى لطول كلمة المرور', 'security'),
            ('enable_two_factor', 'false', 'boolean', 'تفعيل المصادقة الثنائية', 'security'),
            ('backup_frequency', 'weekly', 'text', 'تكرار النسخ الاحتياطي', 'backup'),
            ('backup_retention_days', '30', 'number', 'عدد أيام الاحتفاظ بالنسخ الاحتياطية', 'backup')
        ]

        for key, value, setting_type, description, category in default_settings:
            if not SystemSettings.query.filter_by(setting_key=key).first():
                setting = SystemSettings(
                    setting_key=key,
                    setting_value=value,
                    setting_type=setting_type,
                    description=description,
                    category=category
                )
                db.session.add(setting)

        db.session.commit()
        logger.info(f"تم إنشاء قاعدة البيانات والمستخدمين والإعدادات الافتراضية")
        logger.info(f"تم إنشاء {created_types} نوع وثيقة افتراضي")

        # التحقق من النتيجة
        total_types = DocumentType.query.count()
        logger.info(f"إجمالي أنواع الوثائق في النظام: {total_types}")

        # تخطي سجل النشاط الأولي للسرعة
        pass

# مسارات التقويم الذكي - تم دمجها مع النظام المحسن أعلاه

@app.route('/calendar/events')
@login_required
def calendar_events():
    """API لجلب أحداث التقويم"""
    start = request.args.get('start')
    end = request.args.get('end')

    query = CalendarEvent.query

    if start:
        start_date = datetime.fromisoformat(start.replace('Z', '+00:00'))
        query = query.filter(CalendarEvent.start_date >= start_date)

    if end:
        end_date = datetime.fromisoformat(end.replace('Z', '+00:00'))
        query = query.filter(CalendarEvent.start_date <= end_date)

    events = query.all()
    return jsonify([event.to_dict() for event in events])

@app.route('/calendar/add', methods=['GET', 'POST'])
@login_required
def add_calendar_event():
    """إضافة حدث جديد للتقويم - محسن للسرعة القصوى"""
    form = CalendarEventForm()

    # تحديث خيارات الربط بشكل محسن وسريع
    try:
        # تحميل محدود للوثائق الحديثة فقط
        form.document_id.choices = [('', 'لا يوجد')] + [
            (str(d[0]), d[1][:40] + '...' if len(d[1]) > 40 else d[1])
            for d in Document.query.with_entities(Document.id, Document.title).order_by(Document.created_at.desc()).limit(50).all()
        ]
        form.incoming_id.choices = [('', 'لا يوجد')] + [
            (str(i[0]), i[1][:40] + '...' if len(i[1]) > 40 else i[1])
            for i in IncomingDocument.query.with_entities(IncomingDocument.id, IncomingDocument.subject).order_by(IncomingDocument.created_at.desc()).limit(50).all()
        ]
        form.outgoing_id.choices = [('', 'لا يوجد')] + [
            (str(o[0]), o[1][:40] + '...' if len(o[1]) > 40 else o[1])
            for o in OutgoingDocument.query.with_entities(OutgoingDocument.id, OutgoingDocument.subject).order_by(OutgoingDocument.created_at.desc()).limit(50).all()
        ]
    except Exception:
        # في حالة الخطأ، استخدم خيارات فارغة
        form.document_id.choices = [('', 'لا يوجد')]
        form.incoming_id.choices = [('', 'لا يوجد')]
        form.outgoing_id.choices = [('', 'لا يوجد')]

    if request.method == 'POST' and form.validate_on_submit():
        try:
            # تحويل التاريخ والوقت بشكل مبسط وسريع
            if form.all_day.data == '1':
                start_datetime = datetime.combine(form.start_date.data, datetime.min.time())
                end_datetime = datetime.combine(
                    form.end_date.data if form.end_date.data else form.start_date.data,
                    datetime.max.time()
                )
            else:
                try:
                    start_time = datetime.strptime(form.start_time.data or '09:00', '%H:%M').time()
                    start_datetime = datetime.combine(form.start_date.data, start_time)

                    if form.end_date.data:
                        end_time = datetime.strptime(form.end_time.data or '10:00', '%H:%M').time()
                        end_datetime = datetime.combine(form.end_date.data, end_time)
                    else:
                        end_time = datetime.strptime(form.end_time.data or '10:00', '%H:%M').time()
                        end_datetime = datetime.combine(form.start_date.data, end_time)
                except ValueError:
                    # في حالة خطأ في التوقيت، استخدم قيم افتراضية
                    start_datetime = datetime.combine(form.start_date.data, datetime.strptime('09:00', '%H:%M').time())
                    end_datetime = datetime.combine(form.start_date.data, datetime.strptime('10:00', '%H:%M').time())

            # إنشاء الحدث بشكل آمن ومحسن
            event = CalendarEvent(
                title=form.title.data.strip(),
                description=form.description.data.strip() if form.description.data else '',
                start_date=start_datetime,
                end_date=end_datetime,
                all_day=bool(int(form.all_day.data)),
                color=form.color.data,
                document_id=int(form.document_id.data) if form.document_id.data and form.document_id.data != '' else None,
                incoming_id=int(form.incoming_id.data) if form.incoming_id.data and form.incoming_id.data != '' else None,
                outgoing_id=int(form.outgoing_id.data) if form.outgoing_id.data and form.outgoing_id.data != '' else None,
                created_by=current_user.id
            )

            # حفظ سريع في قاعدة البيانات
            db.session.add(event)
            db.session.commit()

            # تسجيل النشاط بشكل مبسط (لا يؤثر على السرعة)
            try:
                activity = ActivityLog(
                    user_id=current_user.id,
                    action='create',
                    entity_type='event',
                    entity_id=event.id,
                    entity_name=event.title,
                    details=f'إضافة حدث: {event.title}',
                    ip_address=request.remote_addr or '127.0.0.1'
                )
                db.session.add(activity)
                db.session.commit()
            except Exception:
                # تجاهل أخطاء تسجيل النشاط
                pass

            flash('تم إضافة الحدث بنجاح', 'success')
            return redirect(url_for('calendar'))

        except Exception as e:
            db.session.rollback()
            logger.error(f"خطأ في إضافة الحدث: {e}")
            flash('حدث خطأ في إضافة الحدث. يرجى المحاولة مرة أخرى.', 'error')

    return render_template('calendar/add_event.html', form=form)

@app.route('/calendar/event/<int:event_id>')
@login_required
def view_calendar_event(event_id):
    """عرض تفاصيل حدث"""
    event = CalendarEvent.query.get_or_404(event_id)
    return render_template('calendar/view_event.html', event=event)

@app.route('/calendar/event/<int:event_id>/delete')
@login_required
def delete_calendar_event(event_id):
    """حذف حدث من التقويم"""
    event = CalendarEvent.query.get_or_404(event_id)

    if event.created_by != current_user.id and not current_user.is_admin():
        flash('ليس لديك صلاحية لحذف هذا الحدث', 'error')
        return redirect(url_for('calendar'))

    try:
        event_title = event.title
        db.session.delete(event)
        db.session.commit()

        # تسجيل النشاط
        log_activity('delete', 'event', event_id, event_title)

        flash(f'تم حذف الحدث "{event_title}" بنجاح', 'success')

    except Exception as e:
        db.session.rollback()
        logger.error(f"خطأ في حذف الحدث: {e}")
        flash('حدث خطأ في حذف الحدث', 'error')

    return redirect(url_for('calendar'))

# مسارات الإشعارات - تم دمجها مع النظام المحسن أعلاه

# مسار QR Code فقط
@app.route('/documents/<int:doc_id>/qr')
@login_required
def document_qr(doc_id):
    """توليد QR code للوثيقة"""
    try:
        document = Document.query.get_or_404(doc_id)

        # إنشاء رابط الوثيقة ورابط التحميل
        doc_url = url_for('view_document', id=doc_id, _external=True)
        download_url = url_for('download_document_file', doc_id=doc_id, _external=True) if document.file_name else ""

        # إنشاء بيانات QR Code شاملة
        qr_text = f"{doc_url}\n\nالوثيقة: {document.title}\nالرقم: #{document.id}\nالنوع: {document.document_type}"
        if download_url:
            qr_text += f"\n\nتحميل الملف: {download_url}"

        # توليد QR code
        qr_data = generate_qr_code(qr_text)

        if qr_data:
            return send_file(
                io.BytesIO(qr_data),
                mimetype='image/png',
                as_attachment=True,
                download_name=f'qr_document_{doc_id}.png'
            )
        else:
            flash('حدث خطأ في توليد QR code', 'error')
            return redirect(url_for('view_document', id=doc_id))

    except Exception as e:
        logger.error(f"خطأ في توليد QR code للوثيقة {doc_id}: {e}")
        flash('حدث خطأ في توليد QR code', 'error')
        return redirect(url_for('view_document', id=doc_id))

@app.route('/documents/<int:doc_id>/a4-label')
@login_required
def document_a4_label(doc_id):
    """توليد ملصق A4 شامل للوثيقة"""
    try:
        document = Document.query.get_or_404(doc_id)
        logger.info(f"إنشاء ملصق A4 للوثيقة {doc_id}")

        # إنشاء ملصق A4
        label_data = create_document_a4_label(document)

        if label_data:
            return send_file(
                io.BytesIO(label_data),
                mimetype='text/html',
                as_attachment=True,
                download_name=f'document_a4_label_{doc_id}.html'
            )
        else:
            flash('حدث خطأ في إنشاء ملصق A4', 'error')
            return redirect(url_for('view_document', id=doc_id))

    except Exception as e:
        logger.error(f"خطأ في إنشاء ملصق A4 للوثيقة {doc_id}: {e}")
        flash('حدث خطأ في إنشاء ملصق A4', 'error')
        return redirect(url_for('view_document', id=doc_id))

@app.route('/documents/<int:doc_id>/qr-preview')
@login_required
def qr_preview(doc_id):
    """معاينة QR Code مع التعليمات"""
    try:
        document = Document.query.get_or_404(doc_id)

        # إنشاء QR Code للمعاينة
        doc_url = url_for('view_document', id=doc_id, _external=True)
        download_url = url_for('download_document_file', doc_id=doc_id, _external=True) if document.file_name else ""

        qr_text = f"{doc_url}\n\nالوثيقة: {document.title}\nالرقم: #{document.id}\nالنوع: {document.document_type}"
        if download_url:
            qr_text += f"\n\nتحميل الملف: {download_url}"

        qr_data = generate_qr_code(qr_text, (300, 300))

        if qr_data:
            import base64
            qr_image_base64 = base64.b64encode(qr_data).decode('utf-8')
            return render_template('documents/qr_preview.html',
                                 document=document,
                                 qr_image_base64=qr_image_base64)
        else:
            flash('حدث خطأ في إنشاء QR Code', 'error')
            return redirect(url_for('view_document', id=doc_id))

    except Exception as e:
        logger.error(f"خطأ في معاينة QR Code للوثيقة {doc_id}: {e}")
        flash('حدث خطأ في معاينة QR Code', 'error')
        return redirect(url_for('view_document', id=doc_id))

@app.route('/documents/<int:doc_id>/a4-label-pdf')
@login_required
def document_a4_label_pdf(doc_id):
    """CRITICAL: إنشاء ملصق A4 للوثيقة بصيغة PDF مع QR code"""
    try:
        document = Document.query.get_or_404(doc_id)
        print(f"🚀 CRITICAL: Creating A4 PDF label for document {doc_id}")

        # إنشاء ملصق PDF
        pdf_data = create_document_a4_pdf_label(document)

        if pdf_data:
            print(f"✅ CRITICAL: A4 PDF label created successfully, size: {len(pdf_data)} bytes")
            return send_file(
                io.BytesIO(pdf_data),
                mimetype='application/pdf',
                as_attachment=True,
                download_name=f'document_a4_label_{doc_id}.pdf'
            )
        else:
            print("❌ CRITICAL: Failed to create A4 PDF label")
            flash('حدث خطأ في إنشاء ملصق PDF', 'error')
            return redirect(url_for('view_document', id=doc_id))

    except Exception as e:
        print(f"❌ CRITICAL: Error creating A4 PDF label for document {doc_id}: {e}")
        logger.error(f"خطأ في إنشاء ملصق PDF للوثيقة {doc_id}: {e}")
        flash('حدث خطأ في إنشاء ملصق PDF', 'error')
        return redirect(url_for('view_document', id=doc_id))

# مسارات إدارة المستخدمين الكاملة
@app.route('/users')
@login_required
def manage_users():
    """صفحة إدارة المستخدمين"""
    if not current_user.is_admin():
        flash('ليس لديك صلاحية للوصول إلى إدارة المستخدمين', 'error')
        return redirect(url_for('dashboard'))

    page = request.args.get('page', 1, type=int)
    per_page = 20

    users = User.query.paginate(page=page, per_page=per_page, error_out=False)

    # إحصائيات المستخدمين
    user_stats = {
        'total_users': User.query.count(),
        'active_users': User.query.filter_by(is_active=True).count(),
        'admin_users': User.query.filter_by(role='admin').count(),
        'employee_users': User.query.filter_by(role='employee').count()
    }

    return render_template('users/manage.html', users=users, user_stats=user_stats)

@app.route('/users/add', methods=['GET', 'POST'])
@login_required
def add_user():
    """إضافة مستخدم جديد"""
    if not current_user.is_admin():
        flash('ليس لديك صلاحية لإضافة مستخدمين', 'error')
        return redirect(url_for('dashboard'))

    form = UserForm()

    if form.validate_on_submit():
        try:
            # التحقق من عدم وجود المستخدم
            existing_user = User.query.filter(
                (User.username == form.username.data) |
                (User.email == form.email.data)
            ).first()

            if existing_user:
                flash('اسم المستخدم أو البريد الإلكتروني موجود مسبقاً', 'error')
                return render_template('users/add.html', form=form)

            user = User(
                username=form.username.data,
                email=form.email.data,
                full_name=form.full_name.data,
                department=form.department.data,
                role=form.role.data,
                is_active=form.is_active.data
            )
            user.set_password(form.password.data)

            db.session.add(user)
            db.session.commit()

            # تسجيل النشاط
            log_activity('create', 'user', user.id, user.full_name or user.username)

            # إنشاء إشعار للمستخدم الجديد
            notification = Notification(
                title='مرحباً بك في النظام',
                message=f'تم إنشاء حسابك بنجاح. يمكنك الآن تسجيل الدخول باستخدام اسم المستخدم: {user.username}',
                type='success',
                user_id=user.id
            )
            db.session.add(notification)
            db.session.commit()

            flash(f'تم إضافة المستخدم {user.full_name or user.username} بنجاح', 'success')
            return redirect(url_for('manage_users'))

        except Exception as e:
            db.session.rollback()
            logger.error(f"خطأ في إضافة المستخدم: {e}")
            flash('حدث خطأ في إضافة المستخدم', 'error')

    return render_template('users/add.html', form=form)

@app.route('/users/<int:user_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_user(user_id):
    """تعديل مستخدم"""
    if not current_user.is_admin():
        flash('ليس لديك صلاحية لتعديل المستخدمين', 'error')
        return redirect(url_for('dashboard'))

    user = User.query.get_or_404(user_id)
    form = UserForm(obj=user)

    if form.validate_on_submit():
        try:
            # التحقق من عدم تضارب البيانات
            existing_user = User.query.filter(
                (User.username == form.username.data) |
                (User.email == form.email.data),
                User.id != user_id
            ).first()

            if existing_user:
                flash('اسم المستخدم أو البريد الإلكتروني موجود مسبقاً', 'error')
                return render_template('users/edit.html', form=form, user=user)

            user.username = form.username.data
            user.email = form.email.data
            user.full_name = form.full_name.data
            user.department = form.department.data
            user.role = form.role.data
            user.is_active = form.is_active.data

            if form.password.data:
                user.set_password(form.password.data)

            db.session.commit()

            # تسجيل النشاط
            log_activity('update', 'user', user.id, user.full_name or user.username)

            flash(f'تم تحديث بيانات المستخدم {user.full_name or user.username} بنجاح', 'success')
            return redirect(url_for('manage_users'))

        except Exception as e:
            db.session.rollback()
            logger.error(f"خطأ في تحديث المستخدم: {e}")
            flash('حدث خطأ في تحديث المستخدم', 'error')

    return render_template('users/edit.html', form=form, user=user)

@app.route('/users/<int:user_id>/toggle-status')
@login_required
def toggle_user_status(user_id):
    """تفعيل/إلغاء تفعيل مستخدم"""
    if not current_user.is_admin():
        flash('ليس لديك صلاحية لتغيير حالة المستخدمين', 'error')
        return redirect(url_for('dashboard'))

    user = User.query.get_or_404(user_id)

    if user.id == current_user.id:
        flash('لا يمكنك تغيير حالة حسابك الخاص', 'error')
        return redirect(url_for('manage_users'))

    try:
        user.is_active = not user.is_active
        db.session.commit()

        status = 'تم تفعيل' if user.is_active else 'تم إلغاء تفعيل'
        log_activity('update', 'user', user.id, f'{status} المستخدم {user.full_name or user.username}')

        flash(f'{status} المستخدم {user.full_name or user.username} بنجاح', 'success')

    except Exception as e:
        db.session.rollback()
        logger.error(f"خطأ في تغيير حالة المستخدم: {e}")
        flash('حدث خطأ في تغيير حالة المستخدم', 'error')

    return redirect(url_for('manage_users'))

# مسارات إدارة الأقسام
@app.route('/departments')
@login_required
def manage_departments():
    """صفحة إدارة الأقسام"""
    if not current_user.is_admin():
        flash('ليس لديك صلاحية للوصول إلى إدارة الأقسام', 'error')
        return redirect(url_for('dashboard'))

    departments = Department.query.all()

    # إحصائيات الأقسام
    dept_stats = {
        'total_departments': Department.query.count(),
        'active_departments': Department.query.filter_by(is_active=True).count(),
        'total_employees': User.query.filter(User.department_id.isnot(None)).count()
    }

    return render_template('departments/manage.html', departments=departments, dept_stats=dept_stats)

@app.route('/departments/add', methods=['GET', 'POST'])
@login_required
def add_department():
    """إضافة قسم جديد"""
    if not current_user.is_admin():
        flash('ليس لديك صلاحية لإضافة أقسام', 'error')
        return redirect(url_for('dashboard'))

    form = DepartmentForm()

    # تحديث خيارات المديرين
    form.manager_id.choices = [('', 'لا يوجد')] + [(str(u.id), u.full_name or u.username) for u in User.query.filter_by(is_active=True).all()]

    if form.validate_on_submit():
        try:
            department = Department(
                name=form.name.data,
                description=form.description.data,
                manager_id=int(form.manager_id.data) if form.manager_id.data else None,
                is_active=form.is_active.data == 'True'
            )

            db.session.add(department)
            db.session.commit()

            # تسجيل النشاط
            log_activity('create', 'department', department.id, department.name)

            flash(f'تم إضافة القسم {department.name} بنجاح', 'success')
            return redirect(url_for('manage_departments'))

        except Exception as e:
            db.session.rollback()
            logger.error(f"خطأ في إضافة القسم: {e}")
            flash('حدث خطأ في إضافة القسم', 'error')

    return render_template('departments/add.html', form=form)

# مسارات المهام التلقائية
@app.route('/admin/maintenance')
@login_required
def maintenance():
    """ENHANCED: صفحة صيانة النظام الشاملة"""
    if not current_user.is_admin():
        flash('ليس لديك صلاحية للوصول إلى صيانة النظام', 'error')
        return redirect(url_for('dashboard'))

    try:
        print("🚀 MAINTENANCE: Loading comprehensive system maintenance...")

        # جمع إحصائيات النظام المحسنة
        stats = get_comprehensive_system_stats()

        # معلومات آخر عمليات الصيانة
        maintenance_history = get_maintenance_history()

        # فحص صحة النظام
        health_check = perform_comprehensive_health_check()

        # إعدادات الصيانة التلقائية
        auto_settings = get_auto_maintenance_settings()

        # تحليل الأداء
        performance_analysis = get_system_performance_analysis()

        return render_template('admin/maintenance_simple.html',
                             stats=stats,
                             maintenance_history=maintenance_history,
                             health_check=health_check,
                             auto_settings=auto_settings,
                             performance_analysis=performance_analysis)

    except Exception as e:
        logger.error(f"خطأ في تحميل صفحة الصيانة: {e}")
        flash('حدث خطأ في تحميل صفحة الصيانة', 'error')
        return render_template('admin/maintenance_simple.html',
                             stats={},
                             maintenance_history={},
                             health_check={},
                             auto_settings={},
                             performance_analysis={})

@app.route('/admin/auto-archive')
@login_required
def run_auto_archive():
    """تشغيل الأرشفة التلقائية يدوياً"""
    if not current_user.is_admin():
        flash('ليس لديك صلاحية لتشغيل الأرشفة التلقائية', 'error')
        return redirect(url_for('dashboard'))

    try:
        archived_count = auto_archive_documents()
        if archived_count > 0:
            flash(f'تم أرشفة {archived_count} وثيقة بنجاح', 'success')
        else:
            flash('لا توجد وثائق تحتاج للأرشفة', 'info')
    except Exception as e:
        logger.error(f"خطأ في تشغيل الأرشفة التلقائية: {e}")
        flash('حدث خطأ في تشغيل الأرشفة التلقائية', 'error')

    return redirect(url_for('maintenance'))

@app.route('/admin/cleanup-logs')
@login_required
def run_cleanup_logs():
    """تنظيف السجلات القديمة"""
    if not current_user.is_admin():
        flash('ليس لديك صلاحية لتنظيف السجلات', 'error')
        return redirect(url_for('dashboard'))

    try:
        deleted_count = cleanup_old_logs()
        if deleted_count > 0:
            flash(f'تم حذف {deleted_count} سجل قديم بنجاح', 'success')
        else:
            flash('لا توجد سجلات قديمة للحذف', 'info')
    except Exception as e:
        logger.error(f"خطأ في تنظيف السجلات: {e}")
        flash('حدث خطأ في تنظيف السجلات', 'error')

    return redirect(url_for('maintenance'))

@app.route('/admin/backup')
@login_required
def run_backup():
    """إنشاء نسخة احتياطية"""
    if not current_user.is_admin():
        flash('ليس لديك صلاحية لإنشاء نسخ احتياطية', 'error')
        return redirect(url_for('dashboard'))

    try:
        backup_file = backup_database()
        if backup_file:
            flash(f'تم إنشاء النسخة الاحتياطية بنجاح: {Path(backup_file).name}', 'success')
        else:
            flash('حدث خطأ في إنشاء النسخة الاحتياطية', 'error')
    except Exception as e:
        logger.error(f"خطأ في إنشاء النسخة الاحتياطية: {e}")
        flash('حدث خطأ في إنشاء النسخة الاحتياطية', 'error')

    return redirect(url_for('maintenance'))

@app.route('/admin/auto-archive', methods=['POST'])
@login_required
def auto_archive():
    """تشغيل الأرشفة التلقائية"""
    if not current_user.is_admin():
        flash('ليس لديك صلاحية لتشغيل الأرشفة التلقائية', 'error')
        return redirect(url_for('dashboard'))

    try:
        # Archive documents older than 1 year
        old_documents = Document.query.filter(
            Document.created_at < datetime.now() - timedelta(days=365)
        ).all()

        archived_count = 0
        for doc in old_documents:
            if not doc.is_archived:
                doc.is_archived = True
                archived_count += 1

        db.session.commit()

        # Log the activity
        log_activity('archive', 'system', None, f'تم أرشفة {archived_count} وثيقة تلقائياً')

        flash(f'تم أرشفة {archived_count} وثيقة بنجاح', 'success')

    except Exception as e:
        logger.error(f"خطأ في الأرشفة التلقائية: {e}")
        flash('حدث خطأ في الأرشفة التلقائية', 'error')

    return redirect(url_for('maintenance'))

@app.route('/admin/cleanup-logs', methods=['POST'])
@login_required
def cleanup_logs():
    """تنظيف السجلات القديمة"""
    if not current_user.is_admin():
        flash('ليس لديك صلاحية لتنظيف السجلات', 'error')
        return redirect(url_for('dashboard'))

    try:
        # Delete logs older than 90 days
        old_logs = ActivityLog.query.filter(
            ActivityLog.created_at < datetime.now() - timedelta(days=90)
        ).all()

        deleted_count = len(old_logs)
        for log in old_logs:
            db.session.delete(log)

        db.session.commit()

        # Log the cleanup activity
        log_activity('cleanup', 'system', None, f'تم حذف {deleted_count} سجل قديم')

        flash(f'تم حذف {deleted_count} سجل قديم بنجاح', 'success')

    except Exception as e:
        logger.error(f"خطأ في تنظيف السجلات: {e}")
        flash('حدث خطأ في تنظيف السجلات', 'error')

    return redirect(url_for('maintenance'))

@app.route('/admin/create-backup', methods=['POST'])
@login_required
def admin_create_backup():
    """إنشاء نسخة احتياطية من صفحة الصيانة"""
    if not current_user.is_admin():
        flash('ليس لديك صلاحية لإنشاء نسخ احتياطية', 'error')
        return redirect(url_for('dashboard'))

    try:
        backup_file = backup_database()
        if backup_file:
            # Log the backup activity
            log_activity('backup', 'system', None, f'تم إنشاء نسخة احتياطية: {Path(backup_file).name}')
            flash(f'تم إنشاء النسخة الاحتياطية بنجاح: {Path(backup_file).name}', 'success')
        else:
            flash('حدث خطأ في إنشاء النسخة الاحتياطية', 'error')

    except Exception as e:
        logger.error(f"خطأ في إنشاء النسخة الاحتياطية: {e}")
        flash('حدث خطأ في إنشاء النسخة الاحتياطية', 'error')

    return redirect(url_for('maintenance'))

@app.route('/admin/optimize-database', methods=['POST'])
@login_required
def optimize_database():
    """تحسين قاعدة البيانات"""
    if not current_user.is_admin():
        flash('ليس لديك صلاحية لتحسين قاعدة البيانات', 'error')
        return redirect(url_for('dashboard'))

    try:
        # SQLite optimization commands
        db.session.execute('VACUUM')
        db.session.execute('ANALYZE')
        db.session.commit()

        # Log the optimization activity
        log_activity('optimize', 'system', None, 'تم تحسين قاعدة البيانات')

        flash('تم تحسين قاعدة البيانات بنجاح', 'success')

    except Exception as e:
        logger.error(f"خطأ في تحسين قاعدة البيانات: {e}")
        flash('حدث خطأ في تحسين قاعدة البيانات', 'error')

    return redirect(url_for('maintenance'))

@app.route('/admin/cleanup-orphaned-files', methods=['POST'])
@login_required
def cleanup_orphaned_files():
    """تنظيف الملفات اليتيمة"""
    if not current_user.is_admin():
        flash('ليس لديك صلاحية لتنظيف الملفات', 'error')
        return redirect(url_for('dashboard'))

    try:
        upload_folder = app.config.get('UPLOAD_FOLDER', 'uploads')
        if not os.path.exists(upload_folder):
            flash('مجلد الرفع غير موجود', 'warning')
            return redirect(url_for('maintenance'))

        # Get all file paths from database
        db_files = set()
        documents = Document.query.all()
        for doc in documents:
            if doc.file_path:
                db_files.add(os.path.basename(doc.file_path))

        # Find and delete orphaned files
        deleted_count = 0
        for root, dirs, files in os.walk(upload_folder):
            for file in files:
                if file not in db_files:
                    file_path = os.path.join(root, file)
                    try:
                        os.remove(file_path)
                        deleted_count += 1
                    except:
                        pass

        # Log the cleanup activity
        log_activity('cleanup', 'system', None, f'تم حذف {deleted_count} ملف يتيم')

        flash(f'تم حذف {deleted_count} ملف يتيم بنجاح', 'success')

    except Exception as e:
        logger.error(f"خطأ في تنظيف الملفات اليتيمة: {e}")
        flash('حدث خطأ في تنظيف الملفات اليتيمة', 'error')

    return redirect(url_for('maintenance'))

def get_comprehensive_system_stats():
    """CRITICAL: Get comprehensive system statistics"""
    try:
        print("🔄 MAINTENANCE: Getting comprehensive system stats...")

        now = datetime.now()

        stats = {
            # Basic counts
            'total_documents': Document.query.count(),
            'total_users': User.query.count(),
            'total_logs': ActivityLog.query.count(),
            'archived_documents': Document.query.filter_by(status='مؤرشف').count(),
            'total_signatures': DigitalSignature.query.count(),
            'total_notifications': Notification.query.count(),

            # Storage information
            'database_size': get_database_size(),
            'upload_folder_size': get_upload_folder_size(),
            'temp_folder_size': get_temp_folder_size(),
            'backup_folder_size': get_backup_folder_size(),

            # Time-based statistics
            'documents_this_month': Document.query.filter(
                Document.created_at >= now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            ).count(),
            'activities_today': ActivityLog.query.filter(
                ActivityLog.created_at >= now.replace(hour=0, minute=0, second=0, microsecond=0)
            ).count(),
            'users_active_today': User.query.filter(
                User.last_login >= now.replace(hour=0, minute=0, second=0, microsecond=0)
            ).count(),

            # System health indicators
            'old_logs_count': ActivityLog.query.filter(
                ActivityLog.created_at < now - timedelta(days=90)
            ).count(),
            'large_files_count': get_large_files_count(),
            'orphaned_files_count': get_orphaned_files_count(),
            'duplicate_files_count': get_duplicate_files_count(),

            # Performance metrics
            'avg_response_time': get_average_response_time(),
            'memory_usage': get_memory_usage(),
            'disk_usage': get_disk_usage(),
            'cpu_usage': get_cpu_usage()
        }

        print(f"✅ MAINTENANCE: System stats collected - {stats['total_documents']} documents")
        return stats

    except Exception as e:
        print(f"❌ MAINTENANCE: Error getting system stats: {e}")
        return {}

def get_maintenance_history():
    """Get maintenance operation history"""
    try:
        print("🔄 MAINTENANCE: Getting maintenance history...")

        history = {
            'last_archive_date': get_last_maintenance_date('archive'),
            'last_cleanup_date': get_last_maintenance_date('cleanup'),
            'last_backup_date': get_last_maintenance_date('backup'),
            'last_optimization_date': get_last_maintenance_date('optimization'),
            'system_uptime': get_system_uptime(),
            'recent_operations': get_recent_maintenance_operations()
        }

        print("✅ MAINTENANCE: Maintenance history collected")
        return history

    except Exception as e:
        print(f"❌ MAINTENANCE: Error getting maintenance history: {e}")
        return {}

def perform_comprehensive_health_check():
    """CRITICAL: Perform comprehensive system health check"""
    try:
        print("🔄 MAINTENANCE: Performing comprehensive health check...")

        health_check = {
            'overall_status': 'healthy',
            'issues': [],
            'warnings': [],
            'recommendations': [],
            'checks': {
                'database_connection': check_database_connection(),
                'file_system': check_file_system_health(),
                'storage_space': check_storage_space(),
                'backup_status': check_backup_status(),
                'log_rotation': check_log_rotation_needed(),
                'orphaned_files': check_orphaned_files(),
                'data_integrity': check_data_integrity(),
                'performance': check_performance_metrics()
            }
        }

        # Analyze check results
        critical_issues = 0
        warnings = 0

        for check_name, check_result in health_check['checks'].items():
            if check_result['status'] == 'critical':
                critical_issues += 1
                health_check['issues'].append({
                    'type': 'critical',
                    'check': check_name,
                    'message': check_result['message'],
                    'recommendation': check_result.get('recommendation', '')
                })
            elif check_result['status'] == 'warning':
                warnings += 1
                health_check['warnings'].append({
                    'type': 'warning',
                    'check': check_name,
                    'message': check_result['message'],
                    'recommendation': check_result.get('recommendation', '')
                })

        # Determine overall status
        if critical_issues > 0:
            health_check['overall_status'] = 'critical'
        elif warnings > 2:
            health_check['overall_status'] = 'warning'
        else:
            health_check['overall_status'] = 'healthy'

        # Add general recommendations
        if health_check['overall_status'] != 'healthy':
            health_check['recommendations'].extend([
                'قم بتشغيل عمليات الصيانة التلقائية',
                'راجع سجلات النظام للحصول على تفاصيل أكثر',
                'تأكد من توفر مساحة كافية على القرص الصلب',
                'قم بإنشاء نسخة احتياطية قبل إجراء أي تغييرات'
            ])

        print(f"✅ MAINTENANCE: Health check completed - Status: {health_check['overall_status']}")
        return health_check

    except Exception as e:
        print(f"❌ MAINTENANCE: Error in health check: {e}")
        return {
            'overall_status': 'error',
            'issues': [{'type': 'critical', 'message': f'خطأ في فحص صحة النظام: {e}'}],
            'warnings': [],
            'recommendations': ['اتصل بمدير النظام لحل هذه المشكلة'],
            'checks': {}
        }

def get_auto_maintenance_settings():
    """Get automatic maintenance settings"""
    try:
        # This would typically come from a settings table
        # For now, return default settings
        settings = {
            'auto_archive_enabled': True,
            'auto_archive_days': 365,
            'auto_cleanup_enabled': True,
            'cleanup_logs_days': 90,
            'auto_backup_enabled': True,
            'backup_frequency': 'weekly',
            'backup_retention_days': 30,
            'optimization_enabled': True,
            'optimization_frequency': 'monthly',
            'notification_enabled': True,
            'notification_email': '<EMAIL>'
        }

        return settings

    except Exception as e:
        print(f"❌ MAINTENANCE: Error getting auto settings: {e}")
        return {}

# Helper functions for maintenance
def get_temp_folder_size():
    """Get temporary folder size"""
    try:
        import tempfile
        temp_dir = tempfile.gettempdir()
        total_size = 0
        for dirpath, dirnames, filenames in os.walk(temp_dir):
            for filename in filenames:
                filepath = os.path.join(dirpath, filename)
                try:
                    total_size += os.path.getsize(filepath)
                except (OSError, IOError):
                    pass
        return format_file_size(total_size)
    except Exception:
        return "غير محدد"

def get_backup_folder_size():
    """Get backup folder size"""
    try:
        backup_dir = os.path.join(os.getcwd(), 'backups')
        if not os.path.exists(backup_dir):
            return "0 MB"

        total_size = 0
        for dirpath, dirnames, filenames in os.walk(backup_dir):
            for filename in filenames:
                filepath = os.path.join(dirpath, filename)
                try:
                    total_size += os.path.getsize(filepath)
                except (OSError, IOError):
                    pass
        return format_file_size(total_size)
    except Exception:
        return "غير محدد"

def get_large_files_count():
    """Count files larger than 10MB"""
    try:
        upload_folder = app.config.get('UPLOAD_FOLDER', 'uploads')
        if not os.path.exists(upload_folder):
            return 0

        large_files = 0
        for dirpath, dirnames, filenames in os.walk(upload_folder):
            for filename in filenames:
                filepath = os.path.join(dirpath, filename)
                try:
                    if os.path.getsize(filepath) > 10 * 1024 * 1024:  # 10MB
                        large_files += 1
                except (OSError, IOError):
                    pass
        return large_files
    except Exception:
        return 0

def get_orphaned_files_count():
    """Count orphaned files (files without database records)"""
    try:
        upload_folder = app.config.get('UPLOAD_FOLDER', 'uploads')
        if not os.path.exists(upload_folder):
            return 0

        # Get all file paths from database
        db_files = set()
        documents = Document.query.all()
        for doc in documents:
            if doc.file_path:
                db_files.add(os.path.basename(doc.file_path))

        # Count files in upload folder not in database
        orphaned = 0
        for filename in os.listdir(upload_folder):
            if filename not in db_files and os.path.isfile(os.path.join(upload_folder, filename)):
                orphaned += 1

        return orphaned
    except Exception:
        return 0

def get_duplicate_files_count():
    """Count potential duplicate files"""
    try:
        # This is a simplified check - in reality you'd use file hashes
        documents = Document.query.all()
        file_sizes = {}
        duplicates = 0

        for doc in documents:
            if doc.file_path and os.path.exists(doc.file_path):
                try:
                    size = os.path.getsize(doc.file_path)
                    if size in file_sizes:
                        duplicates += 1
                    else:
                        file_sizes[size] = 1
                except (OSError, IOError):
                    pass

        return duplicates
    except Exception:
        return 0

def get_average_response_time():
    """Get average response time (simulated)"""
    # In a real application, this would come from performance monitoring
    return 1.2  # seconds

def get_memory_usage():
    """Get memory usage (simulated)"""
    try:
        import psutil
        return f"{psutil.virtual_memory().percent}%"
    except ImportError:
        return "غير متاح"

def get_disk_usage():
    """Get disk usage"""
    try:
        import shutil
        total, used, free = shutil.disk_usage('/')
        used_percent = (used / total) * 100
        return f"{used_percent:.1f}%"
    except Exception:
        return "غير متاح"

def get_cpu_usage():
    """Get CPU usage (simulated)"""
    try:
        import psutil
        return f"{psutil.cpu_percent()}%"
    except ImportError:
        return "غير متاح"

def get_memory_usage_percent():
    """Get memory usage as percentage"""
    try:
        import psutil
        return psutil.virtual_memory().percent
    except ImportError:
        return 50  # Default value

def get_disk_usage_percent():
    """Get disk usage as percentage"""
    try:
        import shutil
        total, used, free = shutil.disk_usage('/')
        return (used / total) * 100
    except Exception:
        return 50  # Default value

def get_cpu_usage_percent():
    """Get CPU usage as percentage"""
    try:
        import psutil
        return psutil.cpu_percent()
    except ImportError:
        return 25  # Default value

def check_database_connection():
    """Check database connection health"""
    try:
        db.session.execute('SELECT 1')
        return {
            'status': 'healthy',
            'message': 'اتصال قاعدة البيانات يعمل بشكل طبيعي'
        }
    except Exception as e:
        return {
            'status': 'critical',
            'message': f'خطأ في اتصال قاعدة البيانات: {e}',
            'recommendation': 'تحقق من إعدادات قاعدة البيانات'
        }

def check_file_system_health():
    """Check file system health"""
    try:
        upload_folder = app.config.get('UPLOAD_FOLDER', 'uploads')

        # Check if upload folder exists and is writable
        if not os.path.exists(upload_folder):
            return {
                'status': 'critical',
                'message': 'مجلد الرفع غير موجود',
                'recommendation': 'إنشاء مجلد الرفع'
            }

        if not os.access(upload_folder, os.W_OK):
            return {
                'status': 'critical',
                'message': 'مجلد الرفع غير قابل للكتابة',
                'recommendation': 'تحقق من صلاحيات المجلد'
            }

        return {
            'status': 'healthy',
            'message': 'نظام الملفات يعمل بشكل طبيعي'
        }
    except Exception as e:
        return {
            'status': 'warning',
            'message': f'تحذير في نظام الملفات: {e}'
        }

def check_storage_space():
    """Check available storage space"""
    try:
        import shutil
        total, used, free = shutil.disk_usage('/')
        free_percent = (free / total) * 100

        if free_percent < 5:
            return {
                'status': 'critical',
                'message': f'مساحة التخزين منخفضة جداً: {free_percent:.1f}% متبقية',
                'recommendation': 'حذف الملفات غير الضرورية أو توسيع مساحة التخزين'
            }
        elif free_percent < 15:
            return {
                'status': 'warning',
                'message': f'مساحة التخزين منخفضة: {free_percent:.1f}% متبقية',
                'recommendation': 'مراقبة استخدام المساحة وتنظيف الملفات القديمة'
            }
        else:
            return {
                'status': 'healthy',
                'message': f'مساحة التخزين كافية: {free_percent:.1f}% متبقية'
            }
    except Exception as e:
        return {
            'status': 'warning',
            'message': f'لا يمكن فحص مساحة التخزين: {e}'
        }

def check_backup_status():
    """Check backup system status"""
    try:
        backup_dir = os.path.join(os.getcwd(), 'backups')

        if not os.path.exists(backup_dir):
            return {
                'status': 'warning',
                'message': 'مجلد النسخ الاحتياطية غير موجود',
                'recommendation': 'إنشاء مجلد النسخ الاحتياطية وتشغيل نسخة احتياطية'
            }

        # Check for recent backups
        backup_files = [f for f in os.listdir(backup_dir) if f.endswith('.sql') or f.endswith('.zip')]

        if not backup_files:
            return {
                'status': 'warning',
                'message': 'لا توجد نسخ احتياطية',
                'recommendation': 'إنشاء نسخة احتياطية فورية'
            }

        # Check if latest backup is recent (within 7 days)
        latest_backup = max(backup_files, key=lambda f: os.path.getctime(os.path.join(backup_dir, f)))
        backup_age = datetime.now() - datetime.fromtimestamp(os.path.getctime(os.path.join(backup_dir, latest_backup)))

        if backup_age.days > 7:
            return {
                'status': 'warning',
                'message': f'آخر نسخة احتياطية قديمة ({backup_age.days} أيام)',
                'recommendation': 'إنشاء نسخة احتياطية جديدة'
            }

        return {
            'status': 'healthy',
            'message': f'النسخ الاحتياطية محدثة (آخر نسخة منذ {backup_age.days} أيام)'
        }

    except Exception as e:
        return {
            'status': 'warning',
            'message': f'خطأ في فحص النسخ الاحتياطية: {e}'
        }

def check_log_rotation_needed():
    """Check if log rotation is needed"""
    try:
        old_logs = ActivityLog.query.filter(
            ActivityLog.created_at < datetime.now() - timedelta(days=90)
        ).count()

        if old_logs > 10000:
            return {
                'status': 'warning',
                'message': f'يوجد {old_logs} سجل قديم يحتاج للحذف',
                'recommendation': 'تشغيل عملية تنظيف السجلات'
            }
        elif old_logs > 1000:
            return {
                'status': 'warning',
                'message': f'يوجد {old_logs} سجل قديم',
                'recommendation': 'جدولة تنظيف السجلات قريباً'
            }
        else:
            return {
                'status': 'healthy',
                'message': 'السجلات في حالة جيدة'
            }

    except Exception as e:
        return {
            'status': 'warning',
            'message': f'خطأ في فحص السجلات: {e}'
        }

def check_orphaned_files():
    """Check for orphaned files"""
    try:
        orphaned_count = get_orphaned_files_count()

        if orphaned_count > 50:
            return {
                'status': 'warning',
                'message': f'يوجد {orphaned_count} ملف يتيم',
                'recommendation': 'تنظيف الملفات اليتيمة'
            }
        elif orphaned_count > 0:
            return {
                'status': 'healthy',
                'message': f'يوجد {orphaned_count} ملف يتيم (عدد قليل)'
            }
        else:
            return {
                'status': 'healthy',
                'message': 'لا توجد ملفات يتيمة'
            }

    except Exception as e:
        return {
            'status': 'warning',
            'message': f'خطأ في فحص الملفات اليتيمة: {e}'
        }

def check_data_integrity():
    """Check data integrity"""
    try:
        # Check for documents without files
        docs_without_files = Document.query.filter(
            Document.file_path.isnot(None)
        ).all()

        missing_files = 0
        for doc in docs_without_files:
            if doc.file_path and not os.path.exists(doc.file_path):
                missing_files += 1

        if missing_files > 10:
            return {
                'status': 'critical',
                'message': f'{missing_files} وثيقة تشير لملفات مفقودة',
                'recommendation': 'مراجعة سلامة البيانات وإصلاح الروابط المكسورة'
            }
        elif missing_files > 0:
            return {
                'status': 'warning',
                'message': f'{missing_files} وثيقة تشير لملفات مفقودة',
                'recommendation': 'مراجعة الوثائق المتأثرة'
            }
        else:
            return {
                'status': 'healthy',
                'message': 'سلامة البيانات جيدة'
            }

    except Exception as e:
        return {
            'status': 'warning',
            'message': f'خطأ في فحص سلامة البيانات: {e}'
        }

def check_performance_metrics():
    """Check performance metrics"""
    try:
        # Simulate performance check
        memory_percent = get_memory_usage_percent()
        cpu_percent = get_cpu_usage_percent()

        if memory_percent > 90 or cpu_percent > 90:
            return {
                'status': 'critical',
                'message': f'استخدام عالي للموارد (ذاكرة: {memory_percent}%, معالج: {cpu_percent}%)',
                'recommendation': 'مراقبة الأداء وإعادة تشغيل الخدمات إذا لزم الأمر'
            }
        elif memory_percent > 75 or cpu_percent > 75:
            return {
                'status': 'warning',
                'message': f'استخدام متوسط للموارد (ذاكرة: {memory_percent}%, معالج: {cpu_percent}%)',
                'recommendation': 'مراقبة الأداء'
            }
        else:
            return {
                'status': 'healthy',
                'message': f'الأداء جيد (ذاكرة: {memory_percent}%, معالج: {cpu_percent}%)'
            }

    except Exception as e:
        return {
            'status': 'warning',
            'message': f'خطأ في فحص الأداء: {e}'
        }

def get_recent_maintenance_operations():
    """Get recent maintenance operations"""
    try:
        # This would typically come from a maintenance_log table
        # For now, return sample data
        operations = [
            {
                'operation': 'تنظيف السجلات',
                'date': datetime.now() - timedelta(days=2),
                'status': 'completed',
                'details': 'تم حذف 1,234 سجل قديم'
            },
            {
                'operation': 'نسخة احتياطية',
                'date': datetime.now() - timedelta(days=5),
                'status': 'completed',
                'details': 'تم إنشاء نسخة احتياطية بحجم 45 MB'
            },
            {
                'operation': 'أرشفة تلقائية',
                'date': datetime.now() - timedelta(days=7),
                'status': 'completed',
                'details': 'تم أرشفة 23 وثيقة قديمة'
            }
        ]

        return operations

    except Exception as e:
        print(f"❌ MAINTENANCE: Error getting recent operations: {e}")
        return []

def identify_performance_bottlenecks():
    """Identify system performance bottlenecks"""
    try:
        bottlenecks = []

        # Check database performance
        if ActivityLog.query.count() > 100000:
            bottlenecks.append({
                'type': 'database',
                'issue': 'جدول السجلات كبير جداً',
                'impact': 'high',
                'solution': 'تنظيف السجلات القديمة'
            })

        # Check file system
        large_files = get_large_files_count()
        if large_files > 50:
            bottlenecks.append({
                'type': 'storage',
                'issue': f'{large_files} ملف كبير الحجم',
                'impact': 'medium',
                'solution': 'ضغط أو أرشفة الملفات الكبيرة'
            })

        # Check orphaned files
        orphaned = get_orphaned_files_count()
        if orphaned > 100:
            bottlenecks.append({
                'type': 'storage',
                'issue': f'{orphaned} ملف يتيم',
                'impact': 'low',
                'solution': 'تنظيف الملفات اليتيمة'
            })

        return bottlenecks

    except Exception as e:
        print(f"❌ MAINTENANCE: Error identifying bottlenecks: {e}")
        return []

def get_memory_usage():
    """Get memory usage percentage"""
    try:
        import psutil
        return psutil.virtual_memory().percent
    except:
        return 25  # Default value

def get_disk_usage():
    """Get disk usage percentage"""
    try:
        import psutil
        return psutil.disk_usage('/').percent
    except:
        return 30  # Default value

def get_cpu_usage():
    """Get CPU usage percentage"""
    try:
        import psutil
        return psutil.cpu_percent(interval=1)
    except:
        return 15  # Default value

def get_disk_usage_percent():
    """Get disk usage percentage"""
    return get_disk_usage()

def get_query_count():
    """Get database query count"""
    try:
        # This is a simplified count - in production you'd track actual queries
        return Document.query.count() + User.query.count() + ActivityLog.query.count()
    except:
        return 0

def get_average_response_time():
    """Get average response time in milliseconds"""
    try:
        # This would typically be tracked by monitoring tools
        # For now, return a simulated value
        return 150  # milliseconds
    except:
        return 0

def get_slow_queries_count():
    """Get count of slow queries"""
    try:
        # This would typically be tracked by database monitoring
        # For now, return a simulated value
        return 2
    except:
        return 0

def get_total_files_count():
    """Get total files count"""
    try:
        upload_folder = app.config.get('UPLOAD_FOLDER', 'uploads')
        if os.path.exists(upload_folder):
            count = 0
            for root, dirs, files in os.walk(upload_folder):
                count += len(files)
            return count
        return 0
    except:
        return 0

def get_large_files_count():
    """Get count of large files (>10MB)"""
    try:
        upload_folder = app.config.get('UPLOAD_FOLDER', 'uploads')
        if os.path.exists(upload_folder):
            count = 0
            for root, dirs, files in os.walk(upload_folder):
                for file in files:
                    file_path = os.path.join(root, file)
                    if os.path.getsize(file_path) > 10 * 1024 * 1024:  # 10MB
                        count += 1
            return count
        return 0
    except:
        return 0

def get_orphaned_files_count():
    """Get count of orphaned files"""
    try:
        # Files that exist in filesystem but not referenced in database
        upload_folder = app.config.get('UPLOAD_FOLDER', 'uploads')
        if not os.path.exists(upload_folder):
            return 0

        # Get all file paths from database
        db_files = set()
        documents = Document.query.all()
        for doc in documents:
            if doc.file_path:
                db_files.add(os.path.basename(doc.file_path))

        # Count files in filesystem not in database
        orphaned_count = 0
        for root, dirs, files in os.walk(upload_folder):
            for file in files:
                if file not in db_files:
                    orphaned_count += 1

        return orphaned_count
    except:
        return 0

def get_duplicate_files_count():
    """Get count of duplicate files"""
    try:
        # This would typically use file hashing to detect duplicates
        # For now, return a simulated value
        return 5
    except:
        return 0

def get_recent_maintenance_operations():
    """Get recent maintenance operations"""
    try:
        # Get recent maintenance activities from log
        recent_ops = ActivityLog.query.filter(
            ActivityLog.action.in_(['backup', 'cleanup', 'archive', 'optimize'])
        ).order_by(ActivityLog.created_at.desc()).limit(10).all()

        operations = []
        for op in recent_ops:
            operations.append({
                'action': op.action,
                'description': op.description,
                'timestamp': op.created_at.strftime('%Y/%m/%d %H:%M'),
                'user': op.user.username if op.user else 'النظام'
            })

        return operations
    except:
        return []

def get_system_performance_analysis():
    """Get comprehensive system performance analysis"""
    try:
        print("🔄 MAINTENANCE: Getting system performance analysis...")

        analysis = {
            'resource_usage': {
                'memory_usage_percent': get_memory_usage(),
                'disk_usage_percent': get_disk_usage(),
                'cpu_usage_percent': get_cpu_usage()
            },
            'database_performance': {
                'query_count': get_query_count(),
                'avg_response_time': get_average_response_time(),
                'slow_queries': get_slow_queries_count()
            },
            'file_system_performance': {
                'total_files': get_total_files_count(),
                'large_files': get_large_files_count(),
                'orphaned_files': get_orphaned_files_count()
            },
            'bottlenecks': identify_performance_bottlenecks(),
            'recommendations': generate_performance_recommendations()
        }

        print("✅ MAINTENANCE: Performance analysis completed")
        return analysis

    except Exception as e:
        print(f"❌ MAINTENANCE: Error getting performance analysis: {e}")
        return {
            'resource_usage': {'memory_usage_percent': 0, 'disk_usage_percent': 0, 'cpu_usage_percent': 0},
            'database_performance': {'query_count': 0, 'avg_response_time': 0, 'slow_queries': 0},
            'file_system_performance': {'total_files': 0, 'large_files': 0, 'orphaned_files': 0},
            'bottlenecks': [],
            'recommendations': []
        }

def generate_performance_recommendations():
    """Generate performance improvement recommendations"""
    try:
        recommendations = []

        # Database recommendations
        if ActivityLog.query.count() > 50000:
            recommendations.append({
                'category': 'database',
                'priority': 'high',
                'title': 'تنظيف السجلات',
                'description': 'حذف السجلات القديمة لتحسين أداء قاعدة البيانات'
            })

        # Storage recommendations
        if get_disk_usage_percent() > 80:
            recommendations.append({
                'category': 'storage',
                'priority': 'high',
                'title': 'تحرير مساحة التخزين',
                'description': 'حذف الملفات غير الضرورية أو أرشفة الملفات القديمة'
            })

        # Performance recommendations
        if get_memory_usage_percent() > 70:
            recommendations.append({
                'category': 'performance',
                'priority': 'medium',
                'title': 'تحسين استخدام الذاكرة',
                'description': 'إعادة تشغيل الخدمات أو تحسين الاستعلامات'
            })

        # General recommendations
        recommendations.extend([
            {
                'category': 'maintenance',
                'priority': 'low',
                'title': 'جدولة الصيانة التلقائية',
                'description': 'تفعيل الصيانة التلقائية لتحسين الأداء المستمر'
            },
            {
                'category': 'backup',
                'priority': 'medium',
                'title': 'نسخ احتياطية منتظمة',
                'description': 'التأكد من إنشاء نسخ احتياطية بانتظام'
            }
        ])

        return recommendations

    except Exception as e:
        print(f"❌ MAINTENANCE: Error generating recommendations: {e}")
        return []

# API الإشعارات - تم دمجها مع النظام المحسن أعلاه

# مسارات OCR
@app.route('/documents/<int:doc_id>/ocr')
@login_required
def process_ocr(doc_id):
    """معالجة وثيقة بـ OCR"""
    try:
        extracted_text = process_document_ocr(doc_id)
        if extracted_text:
            flash('تم استخراج النص من الوثيقة بنجاح', 'success')
        else:
            flash('لم يتم العثور على نص في الوثيقة أو حدث خطأ', 'warning')
    except Exception as e:
        logger.error(f"خطأ في معالجة OCR: {e}")
        flash('حدث خطأ في معالجة OCR', 'error')

    return redirect(url_for('view_document', id=doc_id))

@app.route('/documents/<int:doc_id>/extracted-text')
@login_required
def view_extracted_text(doc_id):
    """عرض النص المستخرج من الوثيقة"""
    document = Document.query.get_or_404(doc_id)

    if not document.extracted_text:
        flash('لا يوجد نص مستخرج لهذه الوثيقة', 'info')
        return redirect(url_for('view_document', id=doc_id))

    return render_template('documents/extracted_text.html', document=document)

@app.route('/ocr-scanner')
@login_required
def ocr_scanner():
    """صفحة ماسح OCR المتقدم"""
    return render_template('ocr/scanner.html')

@app.route('/ocr-scanner/upload', methods=['POST'])
@login_required
@limiter.limit("10 per minute")  # Rate limiting for file uploads
def ocr_upload():
    """رفع ملف لمعالجة OCR - محسن للأمان"""
    try:
        # Validate CSRF token
        validate_csrf(request.form.get('csrf_token'))

        # التحقق من وجود الملف
        if 'file' not in request.files:
            log_security_event('OCR_UPLOAD_NO_FILE', 'OCR upload attempt without file')
            return jsonify({'success': False, 'message': 'لم يتم اختيار ملف'})

        file = request.files['file']
        if file.filename == '':
            return jsonify({'success': False, 'message': 'لم يتم اختيار ملف'})

        # Security validation
        is_valid, error_message = validate_file_upload(file)
        if not is_valid:
            log_security_event('OCR_UPLOAD_INVALID_FILE', f'Invalid OCR file upload: {error_message}')
            return jsonify({'success': False, 'message': error_message})

        # التحقق من نوع الملف
        if not file or not allowed_file(file.filename):
            log_security_event('OCR_UPLOAD_DISALLOWED_TYPE', f'Disallowed file type: {file.filename}')
            return jsonify({'success': False, 'message': 'نوع الملف غير مدعوم. الأنواع المدعومة: PDF, JPG, PNG, TIFF, BMP'})

        # التحقق من حجم الملف (10MB)
        file.seek(0, 2)  # الانتقال لنهاية الملف
        file_size = file.tell()
        file.seek(0)  # العودة للبداية

        if file_size > 10 * 1024 * 1024:  # 10MB
            return jsonify({'success': False, 'message': 'حجم الملف كبير جداً. الحد الأقصى 10 ميجابايت'})

        # حفظ الملف مؤقتاً
        filename = secure_filename(file.filename)
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S_')
        filename = timestamp + filename

        temp_dir = os.path.join(app.config['UPLOAD_FOLDER'], 'temp')
        os.makedirs(temp_dir, exist_ok=True)
        temp_path = os.path.join(temp_dir, filename)

        try:
            file.save(temp_path)
            logger.info(f"تم حفظ الملف مؤقتاً: {temp_path}")
        except Exception as save_error:
            logger.error(f"فشل في حفظ الملف: {save_error}")
            return jsonify({'success': False, 'message': 'فشل في حفظ الملف'})

        # استخراج النص
        extracted_text = ""
        file_ext = os.path.splitext(filename)[1].lower()

        try:
            if file_ext in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']:
                logger.info(f"معالجة صورة: {file_ext}")
                extracted_text = extract_text_from_image(temp_path)
            elif file_ext == '.pdf':
                logger.info("معالجة ملف PDF")
                extracted_text = extract_text_from_pdf(temp_path)
            else:
                return jsonify({'success': False, 'message': f'نوع الملف {file_ext} غير مدعوم'})

        except Exception as ocr_error:
            logger.error(f"خطأ في معالجة OCR: {ocr_error}")
            return jsonify({'success': False, 'message': f'خطأ في معالجة OCR: {str(ocr_error)}'})

        finally:
            # حذف الملف المؤقت
            try:
                if os.path.exists(temp_path):
                    os.remove(temp_path)
                    logger.info(f"تم حذف الملف المؤقت: {temp_path}")
            except Exception as cleanup_error:
                logger.warning(f"فشل في حذف الملف المؤقت: {cleanup_error}")

        # التحقق من النتيجة
        if extracted_text and extracted_text.strip():
            # تنظيف النص
            cleaned_text = extracted_text.strip()

            # إحصائيات النص
            char_count = len(cleaned_text)
            word_count = len(cleaned_text.split())
            line_count = len(cleaned_text.split('\n'))

            logger.info(f"تم استخراج النص بنجاح: {char_count} حرف، {word_count} كلمة، {line_count} سطر")

            return jsonify({
                'success': True,
                'text': cleaned_text,
                'filename': file.filename,
                'char_count': char_count,
                'word_count': word_count,
                'line_count': line_count,
                'file_type': file_ext,
                'file_size_mb': round(file_size / (1024 * 1024), 2)
            })
        else:
            logger.warning("لم يتم العثور على نص في الملف")

            # رسائل مساعدة حسب نوع الملف
            help_message = "لم يتم العثور على نص في الملف. "

            if file_ext in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']:
                help_message += """
                نصائح لتحسين نتائج OCR للصور:
                • تأكد من وضوح النص في الصورة
                • استخدم صور عالية الجودة (300 DPI أو أكثر)
                • تأكد من التباين الجيد بين النص والخلفية
                • تجنب الصور المائلة أو المشوهة
                • استخدم خطوط واضحة مثل Arial أو Times New Roman
                • تجنب الخلفيات المعقدة خلف النص
                """
            elif file_ext == '.pdf':
                help_message += """
                نصائح لتحسين نتائج OCR لملفات PDF:
                • تأكد من أن PDF يحتوي على نص أو صور واضحة
                • تجنب ملفات PDF المحمية بكلمة مرور
                • استخدم ملفات PDF بجودة عالية
                • تأكد من أن النص غير مشفر في PDF
                """

            return jsonify({
                'success': False,
                'message': help_message.strip()
            })

    except Exception as e:
        logger.error(f"خطأ عام في معالجة OCR: {e}")
        return jsonify({'success': False, 'message': f'حدث خطأ في معالجة الملف: {str(e)}'})

# مسارات التوقيع الرقمي المتدرج
@app.route('/documents/<int:doc_id>/sign')
@login_required
def sign_document_page(doc_id):
    """صفحة اختيار نوع التوقيع الرقمي"""
    # التحقق من صلاحية التوقيع
    if not current_user.is_admin():
        flash('ليس لديك صلاحية للتوقيع الرقمي. التوقيع الرقمي مقتصر على المديرين فقط.', 'error')
        return redirect(url_for('view_document', id=doc_id))

    document = Document.query.get_or_404(doc_id)

    # التحقق من وجود توقيعات سابقة
    existing_signatures = DigitalSignature.query.filter_by(
        document_id=doc_id,
        user_id=current_user.id
    ).all()

    return render_template('documents/sign_options.html',
                         document=document,
                         existing_signatures=existing_signatures)

@app.route('/documents/<int:doc_id>/sign/<signature_type>', methods=['POST'])
@login_required
@limiter.limit("5 per minute")  # Rate limiting for digital signatures
def sign_document(doc_id, signature_type):
    """توقيع وثيقة رقمياً بنوع محدد - محسن للأمان"""
    try:
        # Validate CSRF token
        validate_csrf(request.form.get('csrf_token') or request.json.get('csrf_token'))

        # التحقق من صلاحية التوقيع
        if not current_user.is_admin():
            log_security_event('UNAUTHORIZED_SIGNATURE_ATTEMPT', f'User {current_user.username} attempted to sign document {doc_id}')
            return jsonify({
                'success': False,
                'message': 'ليس لديك صلاحية للتوقيع الرقمي. التوقيع الرقمي مقتصر على المديرين فقط.'
            })

        # التحقق من نوع التوقيع
        if signature_type not in ['normal', 'certified']:
            log_security_event('INVALID_SIGNATURE_TYPE', f'Invalid signature type: {signature_type}')
            return jsonify({'success': False, 'message': 'نوع التوقيع غير صحيح'})

        document = Document.query.get_or_404(doc_id)
        # التحقق من عدم وجود توقيع من نفس النوع
        existing_signature = DigitalSignature.query.filter_by(
            document_id=doc_id,
            user_id=current_user.id,
            signature_type=signature_type
        ).first()

        if existing_signature:
            return jsonify({
                'success': False,
                'message': f'لقد قمت بـ{existing_signature.get_signature_type_display()} لهذه الوثيقة مسبقاً'
            })

        # الحصول على الملاحظات
        notes = request.json.get('notes', '') if request.is_json else ''

        # توليد التوقيع الرقمي
        signature_data = generate_digital_signature(doc_id, current_user.id, signature_type, notes)

        if signature_data:
            # حفظ التوقيع في قاعدة البيانات
            digital_signature = DigitalSignature(
                document_id=doc_id,
                user_id=current_user.id,
                signature_hash=signature_data['signature'],
                signature_data=json.dumps(signature_data),
                signature_type=signature_type,
                algorithm=signature_data['algorithm'],
                notes=notes
            )

            db.session.add(digital_signature)
            db.session.commit()

            # تسجيل النشاط
            activity_desc = f'{digital_signature.get_signature_type_display()}: {document.title}'
            log_activity('sign', 'document', doc_id, activity_desc)

            # إنشاء إشعار
            notification = Notification(
                title=f'تم {digital_signature.get_signature_type_display()}',
                message=f'تم {digital_signature.get_signature_type_display()} للوثيقة "{document.title}" بنجاح',
                type='success',
                user_id=current_user.id,
                entity_type='document',
                entity_id=doc_id
            )
            db.session.add(notification)
            db.session.commit()

            logger.info(f"تم {digital_signature.get_signature_type_display()} للوثيقة {doc_id} بواسطة {current_user.username}")

            return jsonify({
                'success': True,
                'message': f'تم {digital_signature.get_signature_type_display()} بنجاح',
                'signature_type': digital_signature.get_signature_type_display(),
                'signature_id': digital_signature.id
            })
        else:
            return jsonify({'success': False, 'message': 'حدث خطأ في توليد التوقيع الرقمي'})

    except Exception as e:
        db.session.rollback()
        logger.error(f"خطأ في توقيع الوثيقة: {e}")
        return jsonify({'success': False, 'message': 'حدث خطأ في التوقيع الرقمي'})

@app.route('/documents/<int:doc_id>/signatures')
@login_required
def view_signatures(doc_id):
    """عرض توقيعات الوثيقة"""
    try:
        document = Document.query.get_or_404(doc_id)
        signatures = DigitalSignature.query.filter_by(document_id=doc_id).order_by(DigitalSignature.timestamp.desc()).all()

        logger.info(f"عرض توقيعات الوثيقة {doc_id} - عدد التوقيعات: {len(signatures)}")
        return render_template('documents/signatures.html', document=document, signatures=signatures)

    except Exception as e:
        logger.error(f"خطأ في عرض توقيعات الوثيقة {doc_id}: {e}")
        flash('حدث خطأ في عرض التوقيعات', 'error')
        return redirect(url_for('view_document', id=doc_id))

@app.route('/signatures/<int:signature_id>/certificate')
@login_required
def download_signature_certificate(signature_id):
    """تحميل شهادة التوقيع الرقمي"""
    signature = DigitalSignature.query.get_or_404(signature_id)

    try:
        import json
        signature_data = json.loads(signature.signature_data)
        certificate_data = create_signature_certificate(
            signature.document_id,
            signature.user_id,
            signature_data
        )

        if certificate_data:
            return send_file(
                io.BytesIO(certificate_data),
                mimetype='image/png',
                as_attachment=True,
                download_name=f'signature_certificate_{signature_id}.png'
            )
        else:
            flash('حدث خطأ في إنشاء شهادة التوقيع', 'error')

    except Exception as e:
        logger.error(f"خطأ في تحميل شهادة التوقيع: {e}")
        flash('حدث خطأ في تحميل شهادة التوقيع', 'error')

    return redirect(url_for('view_signatures', doc_id=signature.document_id))

@app.route('/signatures/<int:signature_id>/verify')
@login_required
def verify_signature(signature_id):
    """التحقق من صحة التوقيع الرقمي"""
    try:
        signature = DigitalSignature.query.get_or_404(signature_id)
        logger.info(f"بدء التحقق من التوقيع {signature_id}")

        # التحقق من صحة التوقيع
        is_valid = signature.verify_signature()

        # تحديث حالة التوقيع
        signature.is_valid = is_valid
        db.session.commit()

        # تسجيل النشاط
        activity_desc = f"التحقق من التوقيع #{signature_id} - النتيجة: {'صحيح' if is_valid else 'غير صحيح'}"
        log_activity('verify', 'signature', signature_id, activity_desc)

        if is_valid:
            flash(f'التوقيع {signature.get_signature_type_display()} صحيح وموثوق', 'success')
            logger.info(f"التوقيع {signature_id} صحيح")
        else:
            flash(f'التوقيع {signature.get_signature_type_display()} غير صحيح أو تم التلاعب به', 'error')
            logger.warning(f"التوقيع {signature_id} غير صحيح")

    except Exception as e:
        db.session.rollback()
        logger.error(f"خطأ في التحقق من التوقيع {signature_id}: {e}")
        flash('حدث خطأ في التحقق من التوقيع', 'error')
        return redirect(url_for('dashboard'))

    return redirect(url_for('view_signatures', doc_id=signature.document_id))

@app.route('/digital-signatures')
@login_required
def digital_signatures_manager():
    """صفحة إدارة التوقيعات الرقمية"""
    page = request.args.get('page', 1, type=int)
    per_page = 20

    # جلب التوقيعات مع الفلاتر
    query = DigitalSignature.query

    # فلترة حسب المستخدم إذا لم يكن مديراً
    if not current_user.is_admin():
        query = query.filter_by(user_id=current_user.id)

    # فلترة حسب الحالة
    status = request.args.get('status')
    if status == 'valid':
        query = query.filter_by(is_valid=True)
    elif status == 'invalid':
        query = query.filter_by(is_valid=False)

    # فلترة حسب نوع التوقيع
    signature_type = request.args.get('signature_type')
    if signature_type in ['normal', 'certified']:
        query = query.filter_by(signature_type=signature_type)

    # فلترة حسب المستخدم
    user_filter = request.args.get('user')
    if user_filter:
        query = query.join(User).filter(User.username.contains(user_filter))

    # فلترة حسب التاريخ
    date_from = request.args.get('date_from')
    if date_from:
        try:
            query = query.filter(DigitalSignature.timestamp >= datetime.strptime(date_from, '%Y-%m-%d'))
        except ValueError:
            pass

    date_to = request.args.get('date_to')
    if date_to:
        try:
            query = query.filter(DigitalSignature.timestamp <= datetime.strptime(date_to, '%Y-%m-%d'))
        except ValueError:
            pass

    signatures = query.order_by(DigitalSignature.timestamp.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )

    # إحصائيات التوقيعات
    stats = {
        'total_signatures': DigitalSignature.query.count(),
        'valid_signatures': DigitalSignature.query.filter_by(is_valid=True).count(),
        'invalid_signatures': DigitalSignature.query.filter_by(is_valid=False).count(),
        'my_signatures': DigitalSignature.query.filter_by(user_id=current_user.id).count(),
        'normal_signatures': DigitalSignature.query.filter_by(signature_type='normal').count(),
        'certified_signatures': DigitalSignature.query.filter_by(signature_type='certified').count()
    }

    return render_template('signatures/manager.html', signatures=signatures, stats=stats)

# معالجة الأخطاء العامة
@app.errorhandler(404)
def not_found_error(error):
    """معالجة خطأ 404 - الصفحة غير موجودة"""
    logger.warning(f"صفحة غير موجودة: {request.url}")
    return render_template('errors/404.html'), 404

@app.errorhandler(500)
def internal_error(error):
    """معالجة خطأ 500 - خطأ داخلي في الخادم"""
    db.session.rollback()
    logger.error(f"خطأ داخلي في الخادم: {error}")
    return render_template('errors/500.html'), 500

@app.errorhandler(403)
def forbidden_error(error):
    """معالجة خطأ 403 - ممنوع الوصول"""
    logger.warning(f"محاولة وصول غير مصرح بها: {request.url} من {request.remote_addr}")
    return render_template('errors/403.html'), 403

@app.errorhandler(413)
def request_entity_too_large(error):
    """معالجة خطأ 413 - حجم الملف كبير جداً"""
    logger.warning(f"محاولة رفع ملف كبير جداً من {request.remote_addr}")
    flash('حجم الملف كبير جداً. الحد الأقصى المسموح 50 ميجابايت.', 'error')
    return redirect(request.url)

# دالة تحسين الأداء
@app.before_request
def before_request():
    """تنفيذ قبل كل طلب - محسن"""
    # تسجيل الطلبات للمراقبة (فقط في حالة Debug)
    if app.debug and request.endpoint and not request.endpoint.startswith('static'):
        logger.debug(f"طلب: {request.method} {request.url}")

@app.errorhandler(500)
def internal_error(error):
    """معالج الأخطاء الداخلية"""
    db.session.rollback()
    logger.error(f"خطأ داخلي: {error}")
    flash('حدث خطأ داخلي في النظام', 'error')
    try:
        return render_template('errors/500.html'), 500
    except:
        return '<h1>خطأ داخلي في النظام</h1><p>يرجى المحاولة مرة أخرى</p>', 500

@app.errorhandler(404)
def not_found_error(error):
    """معالج الصفحات غير الموجودة"""
    try:
        return render_template('errors/404.html'), 404
    except:
        return '<h1>الصفحة غير موجودة</h1><p>الصفحة المطلوبة غير موجودة</p>', 404

@app.errorhandler(403)
def forbidden_error(error):
    """معالج الوصول المحظور"""
    try:
        return render_template('errors/403.html'), 403
    except:
        return '<h1>وصول محظور</h1><p>ليس لديك صلاحية للوصول إلى هذه الصفحة</p>', 403

@app.after_request
def after_request(response):
    """تنفيذ بعد كل طلب"""
    # إضافة headers للأمان
    response.headers['X-Content-Type-Options'] = 'nosniff'
    response.headers['X-Frame-Options'] = 'DENY'
    response.headers['X-XSS-Protection'] = '1; mode=block'
    return response

if __name__ == '__main__':
    # إنشاء مجلد السجلات
    Path('logs').mkdir(exist_ok=True)
    
    # تهيئة قاعدة البيانات
    init_database()
    
    # تشغيل التطبيق
    logger.info("بدء تشغيل نظام إدارة الأرشيف العام")
    app.run(debug=False, host='127.0.0.1', port=5000, threaded=True)
