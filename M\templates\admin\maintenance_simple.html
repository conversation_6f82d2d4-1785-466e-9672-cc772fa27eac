{% extends "base.html" %}

{% block title %}صفحة الصيانة{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-tools"></i>
                        صفحة الصيانة
                    </h3>
                </div>
                <div class="card-body">
                    <!-- System Health Status -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <h4>حالة النظام</h4>
                            {% if health_check %}
                                <div class="alert alert-{{ 'success' if health_check.overall_status == 'healthy' else 'warning' if health_check.overall_status == 'warning' else 'danger' }}">
                                    <strong>الحالة العامة:</strong> 
                                    {% if health_check.overall_status == 'healthy' %}
                                        النظام يعمل بشكل طبيعي
                                    {% elif health_check.overall_status == 'warning' %}
                                        يوجد تحذيرات
                                    {% else %}
                                        يوجد مشاكل حرجة
                                    {% endif %}
                                </div>
                            {% else %}
                                <div class="alert alert-info">
                                    لا توجد بيانات فحص متاحة
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- System Checks -->
                    {% if health_check and health_check.checks %}
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <h4>فحوصات النظام</h4>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>الفحص</th>
                                            <th>الحالة</th>
                                            <th>الرسالة</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for check_name, check_result in health_check.checks.items() %}
                                        <tr>
                                            <td>
                                                {% if check_name == 'database_connection' %}
                                                    اتصال قاعدة البيانات
                                                {% elif check_name == 'file_system' %}
                                                    نظام الملفات
                                                {% elif check_name == 'storage_space' %}
                                                    مساحة التخزين
                                                {% elif check_name == 'backup_status' %}
                                                    حالة النسخ الاحتياطي
                                                {% elif check_name == 'log_rotation' %}
                                                    دوران السجلات
                                                {% elif check_name == 'orphaned_files' %}
                                                    الملفات اليتيمة
                                                {% elif check_name == 'data_integrity' %}
                                                    سلامة البيانات
                                                {% elif check_name == 'performance' %}
                                                    الأداء
                                                {% else %}
                                                    {{ check_name }}
                                                {% endif %}
                                            </td>
                                            <td>
                                                <span class="badge badge-{{ 'success' if check_result.status == 'healthy' else 'warning' if check_result.status == 'warning' else 'danger' }}">
                                                    {% if check_result.status == 'healthy' %}
                                                        سليم
                                                    {% elif check_result.status == 'warning' %}
                                                        تحذير
                                                    {% else %}
                                                        خطر
                                                    {% endif %}
                                                </span>
                                            </td>
                                            <td>{{ check_result.message }}</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Performance Analysis -->
                    {% if performance_analysis %}
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <h4>تحليل الأداء</h4>
                            <div class="row">
                                {% if performance_analysis.resource_usage %}
                                <div class="col-md-4">
                                    <div class="card">
                                        <div class="card-header">
                                            <h5>استخدام الموارد</h5>
                                        </div>
                                        <div class="card-body">
                                            <p><strong>المعالج:</strong> {{ performance_analysis.resource_usage.cpu_usage_percent or 0 }}%</p>
                                            <p><strong>الذاكرة:</strong> {{ performance_analysis.resource_usage.memory_usage_percent or 0 }}%</p>
                                            <p><strong>القرص الصلب:</strong> {{ performance_analysis.resource_usage.disk_usage_percent or 0 }}%</p>
                                        </div>
                                    </div>
                                </div>
                                {% endif %}
                                
                                {% if performance_analysis.database_performance %}
                                <div class="col-md-4">
                                    <div class="card">
                                        <div class="card-header">
                                            <h5>أداء قاعدة البيانات</h5>
                                        </div>
                                        <div class="card-body">
                                            <p><strong>عدد الاستعلامات:</strong> {{ performance_analysis.database_performance.query_count or 0 }}</p>
                                            <p><strong>متوسط الاستجابة:</strong> {{ performance_analysis.database_performance.avg_response_time or 0 }}ms</p>
                                            <p><strong>الاستعلامات البطيئة:</strong> {{ performance_analysis.database_performance.slow_queries or 0 }}</p>
                                        </div>
                                    </div>
                                </div>
                                {% endif %}
                                
                                {% if performance_analysis.file_system_performance %}
                                <div class="col-md-4">
                                    <div class="card">
                                        <div class="card-header">
                                            <h5>أداء نظام الملفات</h5>
                                        </div>
                                        <div class="card-body">
                                            <p><strong>إجمالي الملفات:</strong> {{ performance_analysis.file_system_performance.total_files or 0 }}</p>
                                            <p><strong>الملفات الكبيرة:</strong> {{ performance_analysis.file_system_performance.large_files or 0 }}</p>
                                            <p><strong>الملفات اليتيمة:</strong> {{ performance_analysis.file_system_performance.orphaned_files or 0 }}</p>
                                        </div>
                                    </div>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Maintenance Operations -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <h4>عمليات الصيانة</h4>
                            <div class="row">
                                <div class="col-md-3">
                                    <button class="btn btn-primary btn-block" onclick="performMaintenance('auto-archive')">
                                        <i class="fas fa-archive"></i>
                                        أرشفة تلقائية
                                    </button>
                                </div>
                                <div class="col-md-3">
                                    <button class="btn btn-warning btn-block" onclick="performMaintenance('cleanup-logs')">
                                        <i class="fas fa-broom"></i>
                                        تنظيف السجلات
                                    </button>
                                </div>
                                <div class="col-md-3">
                                    <button class="btn btn-success btn-block" onclick="performMaintenance('create-backup')">
                                        <i class="fas fa-download"></i>
                                        نسخة احتياطية
                                    </button>
                                </div>
                                <div class="col-md-3">
                                    <button class="btn btn-info btn-block" onclick="performMaintenance('optimize-database')">
                                        <i class="fas fa-database"></i>
                                        تحسين قاعدة البيانات
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Maintenance History -->
                    {% if maintenance_history %}
                    <div class="row">
                        <div class="col-md-12">
                            <h4>معلومات النظام</h4>
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <tr>
                                        <td><strong>وقت التشغيل:</strong></td>
                                        <td>{{ maintenance_history.system_uptime or 'غير محدد' }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>آخر أرشفة:</strong></td>
                                        <td>{{ maintenance_history.last_archive_date or 'لم تتم' }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>آخر تنظيف:</strong></td>
                                        <td>{{ maintenance_history.last_cleanup_date or 'لم يتم' }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>آخر نسخة احتياطية:</strong></td>
                                        <td>{{ maintenance_history.last_backup_date or 'لم تتم' }}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function performMaintenance(operation) {
    if (confirm('هل أنت متأكد من تنفيذ هذه العملية؟')) {
        fetch('/admin/' + operation, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token() }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم تنفيذ العملية بنجاح: ' + data.message);
                location.reload();
            } else {
                alert('خطأ في تنفيذ العملية: ' + data.message);
            }
        })
        .catch(error => {
            alert('خطأ في الاتصال: ' + error);
        });
    }
}
</script>
{% endblock %}
