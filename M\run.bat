@echo off
echo ========================================
echo    نظام إدارة الأرشيف العام
echo    Archive Management System
echo    تطبيق ويب حديث ومتطور
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: Python غير مثبت على النظام
    echo Error: Python is not installed
    pause
    exit /b 1
)

echo تثبيت المتطلبات...
echo Installing requirements...
python -m pip install -r requirements.txt

if errorlevel 1 (
    echo خطأ في تثبيت المتطلبات
    echo Error installing requirements
    pause
    exit /b 1
)

echo.
echo تشغيل التطبيق...
echo Starting application...
echo.
echo بيانات الدخول الافتراضية:
echo Default login credentials:
echo اسم المستخدم / Username: admin
echo كلمة المرور / Password: Admin123!
echo.
echo سيتم فتح التطبيق على:
echo Application will open at:
echo http://localhost:5000
echo.

REM Start the application
python app.py

REM Keep window open if there's an error
if errorlevel 1 (
    echo.
    echo حدث خطأ في تشغيل التطبيق
    echo An error occurred while running the application
    pause
)
